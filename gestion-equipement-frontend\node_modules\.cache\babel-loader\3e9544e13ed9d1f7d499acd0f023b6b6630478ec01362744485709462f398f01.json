{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\gestion-equipement-frontend\\\\src\\\\pages\\\\Equipements.js\",\n  _s = $RefreshSig$();\nimport './Equipements.css';\nimport { Link, useLocation } from 'react-router-dom';\nimport logo from '../assets/images/asment logo.jpg';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { faLaptop, faUsers, faExchangeAlt, faTruck, faUserCog, faSignOutAlt, faSearch, faBell, faTimes, faPlus, faEdit, faTrash } from '@fortawesome/free-solid-svg-icons';\nimport { useEffect, useState } from \"react\";\nimport jsPDF from \"jspdf\";\nimport autoTable from \"jspdf-autotable\";\nimport * as XLSX from \"xlsx\";\nimport { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement } from 'chart.js';\nimport { Bar, Doughnut } from 'react-chartjs-2';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement);\nexport default function Equipements() {\n  _s();\n  const location = useLocation();\n  const [equipements, setEquipements] = useState([]);\n  const [filteredEquipements, setFilteredEquipements] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [showModal, setShowModal] = useState(false);\n  const [fournisseurs, setFournisseurs] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [submitting, setSubmitting] = useState(false);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [editingId, setEditingId] = useState(null);\n  const [showStats, setShowStats] = useState(false);\n\n  // État du formulaire\n  const [formData, setFormData] = useState({\n    nom: '',\n    type: '',\n    numero_serie: '',\n    statut: 'DISPONIBLE',\n    date_achat: '',\n    fournisseur_id: '',\n    date_debut_garantie: '',\n    date_fin_garantie: '',\n    en_stock: true,\n    stock_actuel: 1,\n    stock_max: 1,\n    stock_min: 1,\n    categorie_id: ''\n  });\n\n  // Options pour les statuts et types\n  const statutOptions = ['DISPONIBLE', 'EN_MAINTENANCE', 'OCCUPE', 'HORS_SERVICE'];\n  const typeOptions = ['ORDINATEUR', 'IMPRIMANTE', 'SCANNER', 'TELEPHONE', 'TABLETTE', 'SERVEUR', 'AUTRE'];\n  useEffect(() => {\n    console.log(\"🚀 Composant Equipements monté - Début du chargement\");\n    // Forcer le rechargement avec les vraies données\n    localStorage.removeItem('equipements_test');\n    loadEquipements();\n    loadFournisseurs();\n    loadCategories();\n  }, []);\n\n  // Fonction pour sauvegarder les données localement\n  const saveToLocalStorage = equipements => {\n    localStorage.setItem('equipements_test', JSON.stringify(equipements));\n  };\n\n  // Fonction pour générer un nouvel ID\n  const generateNewId = equipements => {\n    const maxId = equipements.reduce((max, eq) => Math.max(max, eq.id), 0);\n    return maxId + 1;\n  };\n  useEffect(() => {\n    if (searchTerm === '') {\n      setFilteredEquipements(equipements);\n    } else {\n      const filtered = equipements.filter(equipement => {\n        var _equipement$fournisse;\n        return equipement.nom.toLowerCase().includes(searchTerm.toLowerCase()) || equipement.type.toLowerCase().includes(searchTerm.toLowerCase()) || equipement.numero_serie && equipement.numero_serie.toLowerCase().includes(searchTerm.toLowerCase()) || equipement.statut.toLowerCase().includes(searchTerm.toLowerCase()) || ((_equipement$fournisse = equipement.fournisseur) === null || _equipement$fournisse === void 0 ? void 0 : _equipement$fournisse.nom) && equipement.fournisseur.nom.toLowerCase().includes(searchTerm.toLowerCase());\n      });\n      setFilteredEquipements(filtered);\n    }\n  }, [searchTerm, equipements]);\n  const loadEquipements = () => {\n    console.log(\"Chargement des équipements...\");\n    setLoading(true);\n    const token = localStorage.getItem('authToken');\n    const headers = {\n      'Content-Type': 'application/json'\n    };\n    if (token) {\n      headers['Authorization'] = `Bearer ${token}`;\n    }\n    fetch(\"http://localhost:8081/api/equipements\", {\n      method: 'GET',\n      headers: headers\n    }).then(res => {\n      console.log(\"Status de la réponse équipements:\", res.status);\n      if (!res.ok) {\n        throw new Error(`Erreur HTTP: ${res.status}`);\n      }\n      return res.json();\n    }).then(data => {\n      console.log(\"✅ Données équipements reçues du backend:\", data);\n      console.log(\"📊 Nombre d'équipements:\", data === null || data === void 0 ? void 0 : data.length);\n      if (Array.isArray(data)) {\n        console.log(\"🔄 Mise à jour de l'état avec les données backend\");\n        setEquipements(data);\n        setFilteredEquipements(data);\n        setError(null);\n        // Sauvegarder les données réelles dans localStorage\n        localStorage.setItem('equipements_test', JSON.stringify(data));\n      } else {\n        console.warn(\"⚠️ Les données équipements ne sont pas un tableau:\", data);\n        throw new Error(\"Format de données incorrect\");\n      }\n      setLoading(false);\n    }).catch(err => {\n      console.error(\"Erreur lors du chargement des équipements:\", err);\n\n      // Utiliser les données de votre base de données en cas d'erreur backend\n      const realData = [{\n        id: 3,\n        nom: \"Dell\",\n        type: \"ORDINATEUR\",\n        numero_serie: \"Sf4567677\",\n        statut: \"DISPONIBLE\",\n        date_achat: \"2025-07-31\",\n        date_fin_garantie: \"2025-07-31\",\n        fournisseur: null,\n        fournisseur_id: null\n      }, {\n        id: 5,\n        nom: \"Hp-EliteBook\",\n        type: \"ORDINATEUR\",\n        numero_serie: \"\",\n        statut: \"DISPONIBLE\",\n        date_achat: \"2025-07-30\",\n        date_fin_garantie: \"2025-07-26\",\n        fournisseur: {\n          id: 2,\n          nom: \"Fournisseur 2\"\n        },\n        fournisseur_id: 2\n      }, {\n        id: 6,\n        nom: \"Canon\",\n        type: \"IMPRIMANTE\",\n        numero_serie: \"34444554\",\n        statut: \"EN_MAINTENANCE\",\n        date_achat: \"2025-07-30\",\n        date_fin_garantie: \"2025-07-30\",\n        fournisseur: {\n          id: 1,\n          nom: \"Fournisseur 1\"\n        },\n        fournisseur_id: 1\n      }];\n      console.log(\"🔄 Backend indisponible - Utilisation des données de fallback:\", realData);\n      console.log(\"📊 Nombre d'équipements de fallback:\", realData.length);\n      setEquipements(realData);\n      setFilteredEquipements(realData);\n      localStorage.setItem('equipements_test', JSON.stringify(realData));\n      setError(\"⚠️ Backend indisponible - Affichage des données de votre base de données\");\n      setLoading(false);\n    });\n  };\n  const loadFournisseurs = () => {\n    console.log(\"Chargement des fournisseurs...\");\n    const token = localStorage.getItem('authToken');\n    const headers = {\n      'Content-Type': 'application/json'\n    };\n    if (token) {\n      headers['Authorization'] = `Bearer ${token}`;\n    }\n    fetch(\"http://localhost:8081/api/fournisseurs\", {\n      method: 'GET',\n      headers: headers\n    }).then(res => {\n      console.log(\"Status de la réponse fournisseurs:\", res.status);\n      if (!res.ok) {\n        throw new Error(`Erreur HTTP: ${res.status}`);\n      }\n      return res.json();\n    }).then(data => {\n      console.log(\"Données fournisseurs reçues:\", data);\n      if (Array.isArray(data)) {\n        setFournisseurs(data);\n        console.log(\"Fournisseurs chargés:\", data.length, \"éléments\");\n      } else {\n        console.warn(\"Les données fournisseurs ne sont pas un tableau:\", data);\n        setFournisseurs([]);\n      }\n    }).catch(err => {\n      console.error(\"Erreur lors du chargement des fournisseurs:\", err);\n      setFournisseurs([]);\n    });\n  };\n  const loadCategories = () => {\n    const token = localStorage.getItem('authToken');\n    const headers = {\n      'Content-Type': 'application/json'\n    };\n    if (token) {\n      headers['Authorization'] = `Bearer ${token}`;\n    }\n    fetch(\"http://localhost:8081/api/categories\", {\n      method: 'GET',\n      headers: headers\n    }).then(res => res.ok ? res.json() : []).then(data => setCategories(Array.isArray(data) ? data : [])).catch(err => {\n      console.error(\"Erreur lors du chargement des catégories:\", err);\n      setCategories([]);\n    });\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value,\n      type,\n      checked\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setSubmitting(true);\n    console.log(\"handleSubmit - editingId:\", editingId);\n    console.log(\"handleSubmit - formData:\", formData);\n    const dataToSend = {\n      ...formData,\n      fournisseur_id: formData.fournisseur_id || null\n    };\n\n    // Essayer d'abord le backend\n    try {\n      const token = localStorage.getItem('authToken');\n      const headers = {\n        'Content-Type': 'application/json'\n      };\n      if (token) {\n        headers['Authorization'] = `Bearer ${token}`;\n      }\n      const url = editingId ? `http://localhost:8081/api/equipements/${editingId}` : \"http://localhost:8081/api/equipements\";\n      console.log(\"URL générée:\", url);\n      const method = editingId ? 'PUT' : 'POST';\n      const response = await fetch(url, {\n        method: method,\n        headers: headers,\n        body: JSON.stringify(dataToSend)\n      });\n      if (!response.ok) {\n        throw new Error(`Erreur HTTP: ${response.status}`);\n      }\n      const result = await response.json();\n      if (editingId) {\n        setEquipements(prev => prev.map(eq => String(eq.id).split(':')[0] === String(editingId).split(':')[0] ? result : eq));\n        setFilteredEquipements(prev => prev.map(eq => String(eq.id).split(':')[0] === String(editingId).split(':')[0] ? result : eq));\n      } else {\n        setEquipements(prev => [...prev, result]);\n        setFilteredEquipements(prev => [...prev, result]);\n      }\n    } catch (backendError) {\n      console.log(\"Backend non disponible, utilisation du stockage local\");\n\n      // Utiliser le stockage local\n      const currentEquipements = [...equipements];\n      if (editingId) {\n        // Modification\n        const updatedEquipements = currentEquipements.map(eq => {\n          if (String(eq.id).split(':')[0] === String(editingId).split(':')[0]) {\n            const fournisseur = fournisseurs.find(f => f.id == dataToSend.fournisseur_id);\n            return {\n              ...eq,\n              ...dataToSend,\n              fournisseur: fournisseur || null\n            };\n          }\n          return eq;\n        });\n        setEquipements(updatedEquipements);\n        setFilteredEquipements(updatedEquipements);\n        saveToLocalStorage(updatedEquipements);\n      } else {\n        // Ajout\n        const newId = generateNewId(currentEquipements);\n        const fournisseur = fournisseurs.find(f => f.id == dataToSend.fournisseur_id);\n        const newEquipement = {\n          ...dataToSend,\n          id: newId,\n          fournisseur: fournisseur || null\n        };\n        const updatedEquipements = [...currentEquipements, newEquipement];\n        setEquipements(updatedEquipements);\n        setFilteredEquipements(updatedEquipements);\n        saveToLocalStorage(updatedEquipements);\n      }\n    }\n\n    // Réinitialiser le formulaire\n    setFormData({\n      nom: '',\n      type: '',\n      numero_serie: '',\n      statut: 'DISPONIBLE',\n      date_achat: '',\n      fournisseur_id: '',\n      date_debut_garantie: '',\n      date_fin_garantie: '',\n      en_stock: true,\n      stock_actuel: 1,\n      stock_max: 1,\n      stock_min: 1,\n      categorie_id: ''\n    });\n    setShowModal(false);\n    setEditingId(null);\n    alert(editingId ? 'Équipement modifié avec succès !' : 'Équipement ajouté avec succès !');\n    setSubmitting(false);\n  };\n  const handleEdit = equipement => {\n    var _equipement$fournisse2;\n    console.log(\"handleEdit - equipement complet:\", equipement);\n    console.log(\"handleEdit - equipement.id:\", equipement.id);\n\n    // S'assurer que l'ID est bien un nombre ou une chaîne simple\n    const cleanId = String(equipement.id).split(':')[0];\n    console.log(\"handleEdit - cleanId:\", cleanId);\n    setEditingId(cleanId);\n    setFormData({\n      nom: equipement.nom,\n      type: equipement.type,\n      numero_serie: equipement.numero_serie || equipement.numeroSerie || '',\n      statut: equipement.statut,\n      date_achat: equipement.date_achat || '',\n      fournisseur_id: ((_equipement$fournisse2 = equipement.fournisseur) === null || _equipement$fournisse2 === void 0 ? void 0 : _equipement$fournisse2.id) || equipement.fournisseur_id || '',\n      date_debut_garantie: equipement.date_debut_garantie || '',\n      date_fin_garantie: equipement.date_fin_garantie || '',\n      en_stock: equipement.en_stock !== undefined ? equipement.en_stock : true,\n      stock_actuel: equipement.stock_actuel || 1,\n      stock_max: equipement.stock_max || 1,\n      stock_min: equipement.stock_min || 1,\n      categorie_id: equipement.categorie_id || ''\n    });\n    setShowModal(true);\n  };\n  const handleDelete = async id => {\n    if (!window.confirm(\"Êtes-vous sûr de vouloir supprimer cet équipement ?\")) {\n      return;\n    }\n    console.log(\"handleDelete - id original:\", id);\n    // S'assurer que l'ID est bien un nombre ou une chaîne simple\n    const cleanId = String(id).split(':')[0];\n    console.log(\"handleDelete - cleanId:\", cleanId);\n    try {\n      const token = localStorage.getItem('authToken');\n      const headers = {\n        'Content-Type': 'application/json'\n      };\n      if (token) {\n        headers['Authorization'] = `Bearer ${token}`;\n      }\n      const response = await fetch(`http://localhost:8081/api/equipements/${cleanId}`, {\n        method: 'DELETE',\n        headers: headers\n      });\n      if (!response.ok) {\n        throw new Error(`Erreur HTTP: ${response.status}`);\n      }\n      setEquipements(prev => prev.filter(eq => String(eq.id).split(':')[0] !== cleanId));\n      setFilteredEquipements(prev => prev.filter(eq => String(eq.id).split(':')[0] !== cleanId));\n      alert('Équipement supprimé avec succès !');\n    } catch (backendError) {\n      console.log(\"Backend non disponible, suppression locale\");\n\n      // Supprimer localement\n      const updatedEquipements = equipements.filter(eq => String(eq.id).split(':')[0] !== cleanId);\n      setEquipements(updatedEquipements);\n      setFilteredEquipements(updatedEquipements);\n      saveToLocalStorage(updatedEquipements);\n      alert('Équipement supprimé avec succès (mode hors ligne) !');\n    }\n  };\n  const handleSearchChange = e => {\n    setSearchTerm(e.target.value);\n  };\n  const formatDate = dateInput => {\n    if (!dateInput || dateInput === null || dateInput === undefined || dateInput === '') {\n      return 'N/A';\n    }\n    try {\n      const dateObj = new Date(dateInput);\n      if (!isNaN(dateObj.getTime())) {\n        return dateObj.toLocaleDateString('fr-FR');\n      }\n      return 'N/A';\n    } catch (e) {\n      console.error(\"Erreur de formatage de date\", e, \"pour la valeur:\", dateInput);\n      return 'N/A';\n    }\n  };\n  const handleExportExcel = () => {\n    if (!Array.isArray(filteredEquipements) || filteredEquipements.length === 0) {\n      alert(\"Aucune donnée à exporter\");\n      return;\n    }\n    const columns = [\"Nom\", \"Type\", \"Numéro de série\", \"Statut\", \"Date d'achat\", \"Date fin garantie\", \"Fournisseur\", \"Actions\"];\n    const rows = filteredEquipements.map(eq => {\n      var _eq$fournisseur;\n      return [eq.nom || \"\", eq.type || \"\", eq.numero_serie || eq.numeroSerie || \"\", eq.statut || \"\", formatDate(eq.dateAchat || eq.date_achat) || \"\", formatDate(eq.dateFinGarantie || eq.date_fin_garantie) || \"\", ((_eq$fournisseur = eq.fournisseur) === null || _eq$fournisseur === void 0 ? void 0 : _eq$fournisseur.nom) || \"\", \"\"];\n    });\n    const worksheet = XLSX.utils.aoa_to_sheet([columns, ...rows]);\n    const workbook = XLSX.utils.book_new();\n    XLSX.utils.book_append_sheet(workbook, worksheet, \"Equipements\");\n    XLSX.writeFile(workbook, \"equipements.xlsx\");\n  };\n  const handleExportPDF = () => {\n    if (!Array.isArray(filteredEquipements) || filteredEquipements.length === 0) {\n      alert(\"Aucune donnée à exporter\");\n      return;\n    }\n    const doc = new jsPDF();\n    doc.text(\"Liste des équipements\", 14, 16);\n    const columns = [\"Nom\", \"Type\", \"Numéro de série\", \"Statut\", \"Date d'achat\", \"Date fin garantie\", \"Fournisseur\"];\n    const rows = filteredEquipements.map(eq => {\n      var _eq$fournisseur2;\n      return [eq.nom || \"\", eq.type || \"\", eq.numero_serie || eq.numeroSerie || \"\", eq.statut || \"\", formatDate(eq.dateAchat || eq.date_achat) || \"\", formatDate(eq.dateFinGarantie || eq.date_fin_garantie) || \"\", ((_eq$fournisseur2 = eq.fournisseur) === null || _eq$fournisseur2 === void 0 ? void 0 : _eq$fournisseur2.nom) || \"\"];\n    });\n    autoTable(doc, {\n      head: [columns],\n      body: rows,\n      startY: 22,\n      styles: {\n        fontSize: 10\n      }\n    });\n    doc.save(\"equipements.pdf\");\n  };\n  const handlePrint = () => {\n    window.print();\n  };\n\n  // Fonctions pour calculer les statistiques\n  const getStatutStats = () => {\n    const statutCounts = {};\n    filteredEquipements.forEach(eq => {\n      const statut = eq.statut || 'Non défini';\n      statutCounts[statut] = (statutCounts[statut] || 0) + 1;\n    });\n    return statutCounts;\n  };\n  const getTypeStats = () => {\n    const typeCounts = {};\n    filteredEquipements.forEach(eq => {\n      const type = eq.type || 'Non défini';\n      typeCounts[type] = (typeCounts[type] || 0) + 1;\n    });\n    return typeCounts;\n  };\n\n  // Configuration des graphiques\n  const statutStats = getStatutStats();\n  const typeStats = getTypeStats();\n  const statutChartData = {\n    labels: Object.keys(statutStats),\n    datasets: [{\n      label: 'Nombre d\\'équipements',\n      data: Object.values(statutStats),\n      backgroundColor: ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b'],\n      borderColor: ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b'],\n      borderWidth: 1\n    }]\n  };\n  const typeChartData = {\n    labels: Object.keys(typeStats),\n    datasets: [{\n      data: Object.values(typeStats),\n      backgroundColor: ['#1976d2', '#43a047', '#d32f2f', '#ffa000', '#9c27b0', '#00acc1', '#8bc34a'],\n      borderWidth: 2,\n      borderColor: '#fff'\n    }]\n  };\n  const chartOptions = {\n    responsive: true,\n    plugins: {\n      legend: {\n        position: 'top'\n      },\n      title: {\n        display: true,\n        text: 'Répartition par statut'\n      }\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        ticks: {\n          stepSize: 1\n        }\n      }\n    }\n  };\n  const doughnutOptions = {\n    responsive: true,\n    plugins: {\n      legend: {\n        position: 'right'\n      },\n      title: {\n        display: true,\n        text: 'Répartition par type'\n      }\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"equipements-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sidebar\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"logo-section\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: logo,\n          alt: \"Logo Entreprise\",\n          className: \"company-logo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 626,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 625,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"main-menu\",\n        children: /*#__PURE__*/_jsxDEV(\"ul\", {\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/\",\n              className: location.pathname === '/' ? 'home-active active' : '',\n              children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: faLaptop,\n                style: {\n                  marginRight: 8\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 632,\n                columnNumber: 17\n              }, this), \" Accueil\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 631,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 630,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/equipements\",\n              className: location.pathname === '/equipements' ? 'active' : '',\n              children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: faLaptop,\n                style: {\n                  marginRight: 8\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 637,\n                columnNumber: 17\n              }, this), \" G\\xE9rer les \\xE9quipements\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 636,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 635,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/employes\",\n              className: location.pathname === '/employes' ? 'active' : '',\n              children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: faUsers,\n                style: {\n                  marginRight: 8\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 642,\n                columnNumber: 17\n              }, this), \" G\\xE9rer les employ\\xE9s\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 641,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 640,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/affectations\",\n              className: location.pathname === '/affectations' ? 'active' : '',\n              children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: faExchangeAlt,\n                style: {\n                  marginRight: 8\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 647,\n                columnNumber: 17\n              }, this), \" Suivi des affectations\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 646,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 645,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/fournisseurs\",\n              className: location.pathname === '/fournisseurs' ? 'active' : '',\n              children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: faTruck,\n                style: {\n                  marginRight: 8\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 652,\n                columnNumber: 17\n              }, this), \" G\\xE9rer les fournisseurs\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 651,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 650,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 629,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 628,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"secondary-links\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/profil\",\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faUserCog,\n            style: {\n              marginRight: 8\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 659,\n            columnNumber: 13\n          }, this), \" Mon profil & param\\xE8tres\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 658,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 657,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: 'auto',\n          padding: '20px 0 0 0',\n          textAlign: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"logout-btn\",\n          onClick: () => {\n            localStorage.removeItem('authToken');\n            window.location.href = '/';\n          },\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faSignOutAlt,\n            style: {\n              marginRight: 8\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 670,\n            columnNumber: 13\n          }, this), \" D\\xE9connexion\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 663,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 662,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 624,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"equipements-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-header-row\",\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between',\n          marginBottom: 32\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-bar\",\n          style: {\n            flex: 1,\n            maxWidth: 480,\n            marginRight: 24,\n            position: 'relative'\n          },\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faSearch,\n            style: {\n              position: 'absolute',\n              left: 14,\n              top: '50%',\n              transform: 'translateY(-50%)',\n              color: '#1976d2',\n              fontSize: '1.1rem',\n              opacity: 0.8\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 680,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Recherche par mot cl\\xE9\",\n            value: searchTerm,\n            onChange: handleSearchChange,\n            style: {\n              width: '100%',\n              padding: '10px 16px 10px 38px',\n              borderRadius: 8,\n              border: '1px solid #dbeafe',\n              fontSize: '1rem',\n              boxShadow: '0 2px 8px rgba(21,101,192,0.06)'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 681,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 679,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"profile-block\",\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 20\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"notification-icon\",\n            style: {\n              position: 'relative',\n              marginRight: 8,\n              cursor: 'pointer'\n            },\n            children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n              icon: faBell,\n              style: {\n                fontSize: '1.3rem',\n                color: '#1976d2'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 691,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                position: 'absolute',\n                top: -6,\n                right: -6,\n                background: '#e74a3b',\n                color: '#fff',\n                borderRadius: '50%',\n                fontSize: '0.7rem',\n                padding: '2px 6px',\n                fontWeight: 600\n              },\n              children: \"3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 692,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 690,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              fontWeight: 500,\n              color: '#2e3a4e',\n              fontSize: '1rem',\n              marginRight: 8\n            },\n            children: \"Responsable IT\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 694,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faUserCog,\n            style: {\n              fontSize: '1.5rem',\n              color: '#1976d2',\n              background: '#fff',\n              borderRadius: '50%',\n              padding: 6,\n              boxShadow: '0 2px 8px rgba(21,101,192,0.10)'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 695,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 689,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 678,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"equipements-title\",\n        children: [\"Liste des \\xE9quipements (\", filteredEquipements.length, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 699,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: '#fff3cd',\n          border: '1px solid #ffeaa7',\n          borderRadius: 8,\n          padding: 16,\n          marginBottom: 20,\n          color: '#856404',\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u26A0\\uFE0F Attention :\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 717,\n            columnNumber: 15\n          }, this), \" \", error]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 716,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 705,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: 16,\n          marginBottom: 24\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          style: {\n            fontWeight: 700,\n            fontSize: '1rem',\n            borderRadius: 10,\n            background: 'linear-gradient(90deg, #1976d2 0%, #42a5f5 100%)',\n            color: '#fff',\n            boxShadow: '0 4px 16px rgba(21,101,192,0.18)'\n          },\n          onClick: () => {\n            setEditingId(null);\n            setShowModal(true);\n          },\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faPlus,\n            style: {\n              marginRight: 8\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 732,\n            columnNumber: 13\n          }, this), \"Ajouter un \\xE9quipement\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 724,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          style: {\n            fontWeight: 700,\n            fontSize: '1rem',\n            borderRadius: 10,\n            background: 'linear-gradient(90deg, #43a047 0%, #66bb6a 100%)',\n            color: '#fff',\n            boxShadow: '0 4px 16px rgba(67,160,71,0.18)'\n          },\n          onClick: handleExportExcel,\n          children: \"Exporter Excel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 735,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          style: {\n            fontWeight: 700,\n            fontSize: '1rem',\n            borderRadius: 10,\n            background: 'linear-gradient(90deg, #d32f2f 0%, #e57373 100%)',\n            color: '#fff',\n            boxShadow: '0 4px 16px rgba(211,47,47,0.18)'\n          },\n          onClick: handleExportPDF,\n          children: \"Exporter PDF\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 738,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          style: {\n            fontWeight: 700,\n            fontSize: '1rem',\n            borderRadius: 10,\n            background: 'linear-gradient(90deg, #ffa000 0%, #ffd54f 100%)',\n            color: '#fff',\n            boxShadow: '0 4px 16px rgba(255,160,0,0.18)'\n          },\n          onClick: handlePrint,\n          children: \"Imprimer\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 741,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          style: {\n            fontWeight: 700,\n            fontSize: '1rem',\n            borderRadius: 10,\n            background: 'linear-gradient(90deg, #9c27b0 0%, #ba68c8 100%)',\n            color: '#fff',\n            boxShadow: '0 4px 16px rgba(156,39,176,0.18)'\n          },\n          onClick: () => setShowStats(!showStats),\n          children: [showStats ? 'Masquer' : 'Afficher', \" les statistiques\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 744,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 723,\n        columnNumber: 9\n      }, this), showStats && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: '#fff',\n          borderRadius: 12,\n          padding: 24,\n          marginBottom: 24,\n          boxShadow: '0 4px 16px rgba(0,0,0,0.1)',\n          border: '1px solid #e0e0e0'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            color: '#1976d2',\n            marginBottom: 24,\n            fontSize: '1.3rem',\n            fontWeight: 700,\n            textAlign: 'center'\n          },\n          children: \"\\uD83D\\uDCCA Statistiques des \\xE9quipements\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 763,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'grid',\n            gridTemplateColumns: '1fr 1fr',\n            gap: 32,\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: '#fafafa',\n              padding: 20,\n              borderRadius: 8,\n              border: '1px solid #e0e0e0',\n              height: '350px'\n            },\n            children: /*#__PURE__*/_jsxDEV(Bar, {\n              data: statutChartData,\n              options: chartOptions\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 782,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 775,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: '#fafafa',\n              padding: 20,\n              borderRadius: 8,\n              border: '1px solid #e0e0e0',\n              height: '350px'\n            },\n            children: /*#__PURE__*/_jsxDEV(Doughnut, {\n              data: typeChartData,\n              options: doughnutOptions\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 793,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 786,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 773,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: 24,\n            padding: 16,\n            background: '#f8f9fa',\n            borderRadius: 8,\n            border: '1px solid #dee2e6'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              color: '#495057',\n              marginBottom: 12,\n              fontSize: '1.1rem'\n            },\n            children: \"\\uD83D\\uDCC8 R\\xE9sum\\xE9\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 805,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'grid',\n              gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n              gap: 16\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center',\n                padding: 12,\n                background: '#fff',\n                borderRadius: 6,\n                border: '1px solid #e9ecef'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '2rem',\n                  fontWeight: 'bold',\n                  color: '#1976d2'\n                },\n                children: filteredEquipements.length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 808,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: '#6c757d',\n                  fontSize: '0.9rem'\n                },\n                children: \"Total \\xE9quipements\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 811,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 807,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center',\n                padding: 12,\n                background: '#fff',\n                borderRadius: 6,\n                border: '1px solid #e9ecef'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '2rem',\n                  fontWeight: 'bold',\n                  color: '#28a745'\n                },\n                children: statutStats['DISPONIBLE'] || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 814,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: '#6c757d',\n                  fontSize: '0.9rem'\n                },\n                children: \"Disponibles\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 817,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 813,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center',\n                padding: 12,\n                background: '#fff',\n                borderRadius: 6,\n                border: '1px solid #e9ecef'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '2rem',\n                  fontWeight: 'bold',\n                  color: '#ffc107'\n                },\n                children: statutStats['EN_MAINTENANCE'] || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 820,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: '#6c757d',\n                  fontSize: '0.9rem'\n                },\n                children: \"En maintenance\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 823,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 819,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center',\n                padding: 12,\n                background: '#fff',\n                borderRadius: 6,\n                border: '1px solid #e9ecef'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '2rem',\n                  fontWeight: 'bold',\n                  color: '#dc3545'\n                },\n                children: statutStats['HORS_SERVICE'] || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 826,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: '#6c757d',\n                  fontSize: '0.9rem'\n                },\n                children: \"Hors service\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 829,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 825,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 806,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 798,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 755,\n        columnNumber: 11\n      }, this), showModal && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          backgroundColor: 'rgba(0, 0, 0, 0.5)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 1000\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            backgroundColor: 'white',\n            borderRadius: 12,\n            padding: 32,\n            maxWidth: 600,\n            width: '90%',\n            maxHeight: '90vh',\n            overflowY: 'auto',\n            boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              marginBottom: 24\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                margin: 0,\n                color: '#1976d2',\n                fontSize: '1.5rem',\n                fontWeight: 700\n              },\n              children: editingId ? 'Modifier un équipement' : 'Ajouter un nouvel équipement'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 861,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                setShowModal(false);\n                setEditingId(null);\n              },\n              style: {\n                background: 'none',\n                border: 'none',\n                fontSize: '1.5rem',\n                color: '#666',\n                cursor: 'pointer',\n                padding: 8,\n                borderRadius: '50%'\n              },\n              children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: faTimes\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 879,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 864,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 860,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSubmit,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: '1fr 1fr',\n                gap: 16,\n                marginBottom: 16\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: 8,\n                    fontWeight: 600,\n                    color: '#333'\n                  },\n                  children: \"Nom de l'\\xE9quipement *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 886,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"nom\",\n                  value: formData.nom,\n                  onChange: handleInputChange,\n                  required: true,\n                  style: {\n                    width: '100%',\n                    padding: '12px',\n                    border: '2px solid #e0e0e0',\n                    borderRadius: 8,\n                    fontSize: '1rem',\n                    transition: 'border-color 0.2s'\n                  },\n                  onFocus: e => e.target.style.borderColor = '#1976d2',\n                  onBlur: e => e.target.style.borderColor = '#e0e0e0'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 889,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 885,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: 8,\n                    fontWeight: 600,\n                    color: '#333'\n                  },\n                  children: \"Type *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 909,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"type\",\n                  value: formData.type,\n                  onChange: handleInputChange,\n                  required: true,\n                  style: {\n                    width: '100%',\n                    padding: '12px',\n                    border: '2px solid #e0e0e0',\n                    borderRadius: 8,\n                    fontSize: '1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"S\\xE9lectionner un type\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 925,\n                    columnNumber: 23\n                  }, this), typeOptions.map(type => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: type,\n                    children: type\n                  }, type, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 927,\n                    columnNumber: 25\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 912,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 908,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 884,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: '1fr 1fr',\n                gap: 16,\n                marginBottom: 16\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: 8,\n                    fontWeight: 600,\n                    color: '#333'\n                  },\n                  children: \"Num\\xE9ro de s\\xE9rie\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 935,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"numero_serie\",\n                  value: formData.numero_serie,\n                  onChange: handleInputChange,\n                  style: {\n                    width: '100%',\n                    padding: '12px',\n                    border: '2px solid #e0e0e0',\n                    borderRadius: 8,\n                    fontSize: '1rem'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 938,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 934,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: 8,\n                    fontWeight: 600,\n                    color: '#333'\n                  },\n                  children: \"Statut *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 954,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"statut\",\n                  value: formData.statut,\n                  onChange: handleInputChange,\n                  required: true,\n                  style: {\n                    width: '100%',\n                    padding: '12px',\n                    border: '2px solid #e0e0e0',\n                    borderRadius: 8,\n                    fontSize: '1rem'\n                  },\n                  children: statutOptions.map(statut => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: statut,\n                    children: statut\n                  }, statut, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 971,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 957,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 953,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 933,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: '1fr 1fr',\n                gap: 16,\n                marginBottom: 16\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: 8,\n                    fontWeight: 600,\n                    color: '#333'\n                  },\n                  children: \"Date d'achat\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 979,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  name: \"date_achat\",\n                  value: formData.date_achat,\n                  onChange: handleInputChange,\n                  style: {\n                    width: '100%',\n                    padding: '12px',\n                    border: '2px solid #e0e0e0',\n                    borderRadius: 8,\n                    fontSize: '1rem'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 982,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 978,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: 8,\n                    fontWeight: 600,\n                    color: '#333'\n                  },\n                  children: \"Fournisseur\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 998,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"fournisseur_id\",\n                  value: formData.fournisseur_id,\n                  onChange: handleInputChange,\n                  style: {\n                    width: '100%',\n                    padding: '12px',\n                    border: '2px solid #e0e0e0',\n                    borderRadius: 8,\n                    fontSize: '1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"S\\xE9lectionner un fournisseur\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1013,\n                    columnNumber: 23\n                  }, this), fournisseurs.map(fournisseur => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: fournisseur.id,\n                    children: fournisseur.nom\n                  }, fournisseur.id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1015,\n                    columnNumber: 25\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1001,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 997,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 977,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: '1fr 1fr',\n                gap: 16,\n                marginBottom: 16\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: 8,\n                    fontWeight: 600,\n                    color: '#333'\n                  },\n                  children: \"D\\xE9but de garantie\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1025,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  name: \"date_debut_garantie\",\n                  value: formData.date_debut_garantie,\n                  onChange: handleInputChange,\n                  style: {\n                    width: '100%',\n                    padding: '12px',\n                    border: '2px solid #e0e0e0',\n                    borderRadius: 8,\n                    fontSize: '1rem'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1028,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1024,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: 8,\n                    fontWeight: 600,\n                    color: '#333'\n                  },\n                  children: \"Fin de garantie\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1044,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  name: \"date_fin_garantie\",\n                  value: formData.date_fin_garantie,\n                  onChange: handleInputChange,\n                  style: {\n                    width: '100%',\n                    padding: '12px',\n                    border: '2px solid #e0e0e0',\n                    borderRadius: 8,\n                    fontSize: '1rem'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1047,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1043,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1023,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: '1fr 1fr 1fr',\n                gap: 16,\n                marginBottom: 16\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: 8,\n                    fontWeight: 600,\n                    color: '#333'\n                  },\n                  children: \"Stock actuel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1065,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  name: \"stock_actuel\",\n                  value: formData.stock_actuel,\n                  onChange: handleInputChange,\n                  min: \"0\",\n                  style: {\n                    width: '100%',\n                    padding: '12px',\n                    border: '2px solid #e0e0e0',\n                    borderRadius: 8,\n                    fontSize: '1rem'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1068,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1064,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: 8,\n                    fontWeight: 600,\n                    color: '#333'\n                  },\n                  children: \"Stock maximum\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1085,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  name: \"stock_max\",\n                  value: formData.stock_max,\n                  onChange: handleInputChange,\n                  min: \"1\",\n                  style: {\n                    width: '100%',\n                    padding: '12px',\n                    border: '2px solid #e0e0e0',\n                    borderRadius: 8,\n                    fontSize: '1rem'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1088,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1084,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: 8,\n                    fontWeight: 600,\n                    color: '#333'\n                  },\n                  children: \"Stock minimum\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1105,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  name: \"stock_min\",\n                  value: formData.stock_min,\n                  onChange: handleInputChange,\n                  min: \"0\",\n                  style: {\n                    width: '100%',\n                    padding: '12px',\n                    border: '2px solid #e0e0e0',\n                    borderRadius: 8,\n                    fontSize: '1rem'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1108,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1104,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1063,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: 24\n              },\n              children: /*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  fontWeight: 600,\n                  color: '#333',\n                  cursor: 'pointer'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  name: \"en_stock\",\n                  checked: formData.en_stock,\n                  onChange: handleInputChange,\n                  style: {\n                    marginRight: 8,\n                    transform: 'scale(1.2)'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1127,\n                  columnNumber: 21\n                }, this), \"\\xC9quipement en stock\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1126,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1125,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                gap: 12,\n                justifyContent: 'flex-end'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => {\n                  setShowModal(false);\n                  setEditingId(null);\n                },\n                style: {\n                  padding: '12px 24px',\n                  border: '2px solid #e0e0e0',\n                  borderRadius: 8,\n                  background: 'white',\n                  color: '#666',\n                  fontSize: '1rem',\n                  fontWeight: 600,\n                  cursor: 'pointer'\n                },\n                children: \"Annuler\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1139,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                disabled: submitting,\n                style: {\n                  padding: '12px 24px',\n                  border: 'none',\n                  borderRadius: 8,\n                  background: submitting ? '#ccc' : 'linear-gradient(90deg, #1976d2 0%, #42a5f5 100%)',\n                  color: 'white',\n                  fontSize: '1rem',\n                  fontWeight: 600,\n                  cursor: submitting ? 'not-allowed' : 'pointer',\n                  boxShadow: '0 4px 16px rgba(21,101,192,0.18)'\n                },\n                children: submitting ? editingId ? 'Modification en cours...' : 'Ajout en cours...' : editingId ? 'Modifier l\\'équipement' : 'Ajouter l\\'équipement'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1158,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1138,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 883,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 850,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 838,\n        columnNumber: 11\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '40px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Chargement des \\xE9quipements...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1186,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1185,\n        columnNumber: 11\n      }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '40px',\n          color: '#e74a3b'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"Erreur lors du chargement : \", error]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1190,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.location.reload(),\n          style: {\n            marginTop: '10px',\n            padding: '8px 16px',\n            background: '#1976d2',\n            color: 'white',\n            border: 'none',\n            borderRadius: '4px',\n            cursor: 'pointer'\n          },\n          children: \"Actualiser la page\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1191,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1189,\n        columnNumber: 11\n      }, this) : !Array.isArray(filteredEquipements) || filteredEquipements.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '40px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: searchTerm ? 'Aucun résultat trouvé' : 'Aucun équipement trouvé'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1200,\n          columnNumber: 13\n        }, this), console.log(\"🚫 Aucun équipement à afficher. État actuel:\", {\n          filteredEquipements,\n          isArray: Array.isArray(filteredEquipements),\n          length: filteredEquipements === null || filteredEquipements === void 0 ? void 0 : filteredEquipements.length,\n          searchTerm\n        })]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1199,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"table\", {\n        className: \"equipements-table\",\n        children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Nom\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1212,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Type\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1213,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Num\\xE9ro de s\\xE9rie\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1214,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Statut\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1215,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Date d'achat\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1216,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Date fin garantie\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1217,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Fournisseur\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1218,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Actions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1219,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1211,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1210,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n          children: filteredEquipements.map((eq, index) => {\n            var _eq$fournisseur3;\n            console.log(`🔍 Rendu équipement ${index}:`, eq);\n            const numeroSerie = eq.numero_serie || eq.numeroSerie || 'N/A';\n            const dateAchat = formatDate(eq.dateAchat || eq.date_achat);\n            const dateFinGarantie = formatDate(eq.dateFinGarantie || eq.date_fin_garantie);\n            const fournisseur = ((_eq$fournisseur3 = eq.fournisseur) === null || _eq$fournisseur3 === void 0 ? void 0 : _eq$fournisseur3.nom) || eq.fournisseur_id || 'Non spécifié';\n            console.log(`📝 Valeurs calculées pour ${eq.nom}:`, {\n              nom: eq.nom,\n              type: eq.type,\n              numeroSerie,\n              statut: eq.statut,\n              dateAchat,\n              dateFinGarantie,\n              fournisseur\n            });\n            return /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                children: eq.nom || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1242,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: eq.type || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1243,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: numeroSerie\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1244,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: eq.statut || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1245,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: dateAchat\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1246,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: dateFinGarantie\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1247,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: fournisseur\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1248,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    gap: '8px'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleEdit(eq),\n                    style: {\n                      background: '#1976d2',\n                      color: 'white',\n                      border: 'none',\n                      borderRadius: '4px',\n                      padding: '6px 12px',\n                      cursor: 'pointer',\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '4px'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                      icon: faEdit\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1265,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Modifier\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1266,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1251,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleDelete(eq.id),\n                    style: {\n                      background: '#e74a3b',\n                      color: 'white',\n                      border: 'none',\n                      borderRadius: '4px',\n                      padding: '6px 12px',\n                      cursor: 'pointer',\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '4px'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                      icon: faTrash\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1282,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Supprimer\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1283,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1268,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1250,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1249,\n                columnNumber: 21\n              }, this)]\n            }, eq.id || index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1241,\n              columnNumber: 19\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1222,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1209,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 676,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 622,\n    columnNumber: 5\n  }, this);\n}\n_s(Equipements, \"1cxR1r6yJnKkkIU+QDQbjlFE5TQ=\", false, function () {\n  return [useLocation];\n});\n_c = Equipements;\nvar _c;\n$RefreshReg$(_c, \"Equipements\");", "map": {"version": 3, "names": ["Link", "useLocation", "logo", "FontAwesomeIcon", "faLaptop", "faUsers", "faExchangeAlt", "faTruck", "faUserCog", "faSignOutAlt", "faSearch", "faBell", "faTimes", "faPlus", "faEdit", "faTrash", "useEffect", "useState", "jsPDF", "autoTable", "XLSX", "Chart", "ChartJS", "CategoryScale", "LinearScale", "BarElement", "Title", "<PERSON><PERSON><PERSON>", "Legend", "ArcElement", "Bar", "Doughnut", "jsxDEV", "_jsxDEV", "register", "Equipements", "_s", "location", "equipements", "setEquipements", "filteredEquipements", "setFilteredEquipements", "loading", "setLoading", "error", "setError", "showModal", "setShowModal", "fournisseurs", "setFournisseurs", "categories", "setCategories", "submitting", "setSubmitting", "searchTerm", "setSearchTerm", "editingId", "setEditingId", "showStats", "setShowStats", "formData", "setFormData", "nom", "type", "numero_serie", "statut", "date_achat", "fournisseur_id", "date_debut_garantie", "date_fin_garantie", "en_stock", "stock_actuel", "stock_max", "stock_min", "categorie_id", "statutOptions", "typeOptions", "console", "log", "localStorage", "removeItem", "loadEquipements", "loadFournisseurs", "loadCategories", "saveToLocalStorage", "setItem", "JSON", "stringify", "generateNewId", "maxId", "reduce", "max", "eq", "Math", "id", "filtered", "filter", "equipement", "_equipement$fournisse", "toLowerCase", "includes", "<PERSON><PERSON><PERSON><PERSON>", "token", "getItem", "headers", "fetch", "method", "then", "res", "status", "ok", "Error", "json", "data", "length", "Array", "isArray", "warn", "catch", "err", "realData", "handleInputChange", "e", "name", "value", "checked", "target", "prev", "handleSubmit", "preventDefault", "dataToSend", "url", "response", "body", "result", "map", "String", "split", "backendError", "currentEquipements", "updatedEquipements", "find", "f", "newId", "newEquipement", "alert", "handleEdit", "_equipement$fournisse2", "cleanId", "numeroSerie", "undefined", "handleDelete", "window", "confirm", "handleSearchChange", "formatDate", "dateInput", "date<PERSON><PERSON>j", "Date", "isNaN", "getTime", "toLocaleDateString", "handleExportExcel", "columns", "rows", "_eq$fournisseur", "dateAchat", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "worksheet", "utils", "aoa_to_sheet", "workbook", "book_new", "book_append_sheet", "writeFile", "handleExportPDF", "doc", "text", "_eq$fournisseur2", "head", "startY", "styles", "fontSize", "save", "handlePrint", "print", "getStatutStats", "statutCounts", "for<PERSON>ach", "getTypeStats", "typeCounts", "statutStats", "typeStats", "statutChartData", "labels", "Object", "keys", "datasets", "label", "values", "backgroundColor", "borderColor", "borderWidth", "typeChartData", "chartOptions", "responsive", "plugins", "legend", "position", "title", "display", "scales", "y", "beginAtZero", "ticks", "stepSize", "doughnutOptions", "className", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "pathname", "icon", "style", "marginRight", "marginTop", "padding", "textAlign", "onClick", "href", "alignItems", "justifyContent", "marginBottom", "flex", "max<PERSON><PERSON><PERSON>", "left", "top", "transform", "color", "opacity", "placeholder", "onChange", "width", "borderRadius", "border", "boxShadow", "gap", "cursor", "right", "background", "fontWeight", "gridTemplateColumns", "height", "options", "bottom", "zIndex", "maxHeight", "overflowY", "margin", "onSubmit", "required", "transition", "onFocus", "onBlur", "min", "disabled", "reload", "index", "_eq$fournisseur3", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/gestion-equipement-frontend/src/pages/Equipements.js"], "sourcesContent": ["import './Equipements.css';\nimport { Link, useLocation } from 'react-router-dom';\nimport logo from '../assets/images/asment logo.jpg';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { faLaptop, faUsers, faExchangeAlt, faTruck, faUserCog, faSignOutAlt, faSearch, faBell, faTimes, faPlus, faEdit, faTrash } from '@fortawesome/free-solid-svg-icons';\nimport { useEffect, useState } from \"react\";\nimport jsPDF from \"jspdf\";\nimport autoTable from \"jspdf-autotable\";\nimport * as XLSX from \"xlsx\";\nimport { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement } from 'chart.js';\nimport { Bar, Doughnut } from 'react-chartjs-2';\n\nChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement);\n\nexport default function Equipements() {\n  const location = useLocation();\n  const [equipements, setEquipements] = useState([]);\n  const [filteredEquipements, setFilteredEquipements] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [showModal, setShowModal] = useState(false);\n  const [fournisseurs, setFournisseurs] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [submitting, setSubmitting] = useState(false);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [editingId, setEditingId] = useState(null);\n  const [showStats, setShowStats] = useState(false);\n\n  // État du formulaire\n  const [formData, setFormData] = useState({\n    nom: '',\n    type: '',\n    numero_serie: '',\n    statut: 'DISPONIBLE',\n    date_achat: '',\n    fournisseur_id: '',\n    date_debut_garantie: '',\n    date_fin_garantie: '',\n    en_stock: true,\n    stock_actuel: 1,\n    stock_max: 1,\n    stock_min: 1,\n    categorie_id: ''\n  });\n\n  // Options pour les statuts et types\n  const statutOptions = ['DISPONIBLE', 'EN_MAINTENANCE', 'OCCUPE', 'HORS_SERVICE'];\n  const typeOptions = ['ORDINATEUR', 'IMPRIMANTE', 'SCANNER', 'TELEPHONE', 'TABLETTE', 'SERVEUR', 'AUTRE'];\n\n  useEffect(() => {\n    console.log(\"🚀 Composant Equipements monté - Début du chargement\");\n    // Forcer le rechargement avec les vraies données\n    localStorage.removeItem('equipements_test');\n\n    loadEquipements();\n    loadFournisseurs();\n    loadCategories();\n  }, []);\n\n\n\n  // Fonction pour sauvegarder les données localement\n  const saveToLocalStorage = (equipements) => {\n    localStorage.setItem('equipements_test', JSON.stringify(equipements));\n  };\n\n  // Fonction pour générer un nouvel ID\n  const generateNewId = (equipements) => {\n    const maxId = equipements.reduce((max, eq) => Math.max(max, eq.id), 0);\n    return maxId + 1;\n  };\n\n  useEffect(() => {\n    if (searchTerm === '') {\n      setFilteredEquipements(equipements);\n    } else {\n      const filtered = equipements.filter(equipement =>\n        equipement.nom.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        equipement.type.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        (equipement.numero_serie && equipement.numero_serie.toLowerCase().includes(searchTerm.toLowerCase())) ||\n        equipement.statut.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        (equipement.fournisseur?.nom && equipement.fournisseur.nom.toLowerCase().includes(searchTerm.toLowerCase()))\n      );\n      setFilteredEquipements(filtered);\n    }\n  }, [searchTerm, equipements]);\n\n  const loadEquipements = () => {\n    console.log(\"Chargement des équipements...\");\n    setLoading(true);\n\n    const token = localStorage.getItem('authToken');\n    const headers = {\n      'Content-Type': 'application/json',\n    };\n    if (token) {\n      headers['Authorization'] = `Bearer ${token}`;\n    }\n\n    fetch(\"http://localhost:8081/api/equipements\", {\n      method: 'GET',\n      headers: headers\n    })\n      .then(res => {\n        console.log(\"Status de la réponse équipements:\", res.status);\n        if (!res.ok) {\n          throw new Error(`Erreur HTTP: ${res.status}`);\n        }\n        return res.json();\n      })\n      .then(data => {\n        console.log(\"✅ Données équipements reçues du backend:\", data);\n        console.log(\"📊 Nombre d'équipements:\", data?.length);\n        if (Array.isArray(data)) {\n          console.log(\"🔄 Mise à jour de l'état avec les données backend\");\n          setEquipements(data);\n          setFilteredEquipements(data);\n          setError(null);\n          // Sauvegarder les données réelles dans localStorage\n          localStorage.setItem('equipements_test', JSON.stringify(data));\n        } else {\n          console.warn(\"⚠️ Les données équipements ne sont pas un tableau:\", data);\n          throw new Error(\"Format de données incorrect\");\n        }\n        setLoading(false);\n      })\n      .catch(err => {\n        console.error(\"Erreur lors du chargement des équipements:\", err);\n\n        // Utiliser les données de votre base de données en cas d'erreur backend\n        const realData = [\n          {\n            id: 3,\n            nom: \"Dell\",\n            type: \"ORDINATEUR\",\n            numero_serie: \"Sf4567677\",\n            statut: \"DISPONIBLE\",\n            date_achat: \"2025-07-31\",\n            date_fin_garantie: \"2025-07-31\",\n            fournisseur: null,\n            fournisseur_id: null\n          },\n          {\n            id: 5,\n            nom: \"Hp-EliteBook\",\n            type: \"ORDINATEUR\",\n            numero_serie: \"\",\n            statut: \"DISPONIBLE\",\n            date_achat: \"2025-07-30\",\n            date_fin_garantie: \"2025-07-26\",\n            fournisseur: { id: 2, nom: \"Fournisseur 2\" },\n            fournisseur_id: 2\n          },\n          {\n            id: 6,\n            nom: \"Canon\",\n            type: \"IMPRIMANTE\",\n            numero_serie: \"34444554\",\n            statut: \"EN_MAINTENANCE\",\n            date_achat: \"2025-07-30\",\n            date_fin_garantie: \"2025-07-30\",\n            fournisseur: { id: 1, nom: \"Fournisseur 1\" },\n            fournisseur_id: 1\n          }\n        ];\n\n        console.log(\"🔄 Backend indisponible - Utilisation des données de fallback:\", realData);\n        console.log(\"📊 Nombre d'équipements de fallback:\", realData.length);\n        setEquipements(realData);\n        setFilteredEquipements(realData);\n        localStorage.setItem('equipements_test', JSON.stringify(realData));\n        setError(\"⚠️ Backend indisponible - Affichage des données de votre base de données\");\n        setLoading(false);\n      });\n  };\n\n  const loadFournisseurs = () => {\n    console.log(\"Chargement des fournisseurs...\");\n\n    const token = localStorage.getItem('authToken');\n    const headers = {\n      'Content-Type': 'application/json',\n    };\n    if (token) {\n      headers['Authorization'] = `Bearer ${token}`;\n    }\n\n    fetch(\"http://localhost:8081/api/fournisseurs\", {\n      method: 'GET',\n      headers: headers\n    })\n      .then(res => {\n        console.log(\"Status de la réponse fournisseurs:\", res.status);\n        if (!res.ok) {\n          throw new Error(`Erreur HTTP: ${res.status}`);\n        }\n        return res.json();\n      })\n      .then(data => {\n        console.log(\"Données fournisseurs reçues:\", data);\n        if (Array.isArray(data)) {\n          setFournisseurs(data);\n          console.log(\"Fournisseurs chargés:\", data.length, \"éléments\");\n        } else {\n          console.warn(\"Les données fournisseurs ne sont pas un tableau:\", data);\n          setFournisseurs([]);\n        }\n      })\n      .catch(err => {\n        console.error(\"Erreur lors du chargement des fournisseurs:\", err);\n        setFournisseurs([]);\n      });\n  };\n\n  const loadCategories = () => {\n    const token = localStorage.getItem('authToken');\n    const headers = {\n      'Content-Type': 'application/json',\n    };\n    if (token) {\n      headers['Authorization'] = `Bearer ${token}`;\n    }\n\n    fetch(\"http://localhost:8081/api/categories\", {\n      method: 'GET',\n      headers: headers\n    })\n      .then(res => res.ok ? res.json() : [])\n      .then(data => setCategories(Array.isArray(data) ? data : []))\n      .catch(err => {\n        console.error(\"Erreur lors du chargement des catégories:\", err);\n        setCategories([]);\n      });\n  };\n\n  const handleInputChange = (e) => {\n    const { name, value, type, checked } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setSubmitting(true);\n\n    console.log(\"handleSubmit - editingId:\", editingId);\n    console.log(\"handleSubmit - formData:\", formData);\n\n    const dataToSend = {\n      ...formData,\n      fournisseur_id: formData.fournisseur_id || null\n    };\n\n    // Essayer d'abord le backend\n    try {\n      const token = localStorage.getItem('authToken');\n      const headers = {\n        'Content-Type': 'application/json',\n      };\n      if (token) {\n        headers['Authorization'] = `Bearer ${token}`;\n      }\n\n      const url = editingId\n        ? `http://localhost:8081/api/equipements/${editingId}`\n        : \"http://localhost:8081/api/equipements\";\n\n      console.log(\"URL générée:\", url);\n      const method = editingId ? 'PUT' : 'POST';\n\n      const response = await fetch(url, {\n        method: method,\n        headers: headers,\n        body: JSON.stringify(dataToSend)\n      });\n\n      if (!response.ok) {\n        throw new Error(`Erreur HTTP: ${response.status}`);\n      }\n\n      const result = await response.json();\n\n      if (editingId) {\n        setEquipements(prev => prev.map(eq => String(eq.id).split(':')[0] === String(editingId).split(':')[0] ? result : eq));\n        setFilteredEquipements(prev => prev.map(eq => String(eq.id).split(':')[0] === String(editingId).split(':')[0] ? result : eq));\n      } else {\n        setEquipements(prev => [...prev, result]);\n        setFilteredEquipements(prev => [...prev, result]);\n      }\n\n    } catch (backendError) {\n      console.log(\"Backend non disponible, utilisation du stockage local\");\n\n      // Utiliser le stockage local\n      const currentEquipements = [...equipements];\n\n      if (editingId) {\n        // Modification\n        const updatedEquipements = currentEquipements.map(eq => {\n          if (String(eq.id).split(':')[0] === String(editingId).split(':')[0]) {\n            const fournisseur = fournisseurs.find(f => f.id == dataToSend.fournisseur_id);\n            return {\n              ...eq,\n              ...dataToSend,\n              fournisseur: fournisseur || null\n            };\n          }\n          return eq;\n        });\n\n        setEquipements(updatedEquipements);\n        setFilteredEquipements(updatedEquipements);\n        saveToLocalStorage(updatedEquipements);\n\n      } else {\n        // Ajout\n        const newId = generateNewId(currentEquipements);\n        const fournisseur = fournisseurs.find(f => f.id == dataToSend.fournisseur_id);\n\n        const newEquipement = {\n          ...dataToSend,\n          id: newId,\n          fournisseur: fournisseur || null\n        };\n\n        const updatedEquipements = [...currentEquipements, newEquipement];\n        setEquipements(updatedEquipements);\n        setFilteredEquipements(updatedEquipements);\n        saveToLocalStorage(updatedEquipements);\n      }\n    }\n\n    // Réinitialiser le formulaire\n    setFormData({\n      nom: '',\n      type: '',\n      numero_serie: '',\n      statut: 'DISPONIBLE',\n      date_achat: '',\n      fournisseur_id: '',\n      date_debut_garantie: '',\n      date_fin_garantie: '',\n      en_stock: true,\n      stock_actuel: 1,\n      stock_max: 1,\n      stock_min: 1,\n      categorie_id: ''\n    });\n    setShowModal(false);\n    setEditingId(null);\n\n    alert(editingId ? 'Équipement modifié avec succès !' : 'Équipement ajouté avec succès !');\n    setSubmitting(false);\n  };\n\n  const handleEdit = (equipement) => {\n    console.log(\"handleEdit - equipement complet:\", equipement);\n    console.log(\"handleEdit - equipement.id:\", equipement.id);\n\n    // S'assurer que l'ID est bien un nombre ou une chaîne simple\n    const cleanId = String(equipement.id).split(':')[0];\n    console.log(\"handleEdit - cleanId:\", cleanId);\n\n    setEditingId(cleanId);\n    setFormData({\n      nom: equipement.nom,\n      type: equipement.type,\n      numero_serie: equipement.numero_serie || equipement.numeroSerie || '',\n      statut: equipement.statut,\n      date_achat: equipement.date_achat || '',\n      fournisseur_id: equipement.fournisseur?.id || equipement.fournisseur_id || '',\n      date_debut_garantie: equipement.date_debut_garantie || '',\n      date_fin_garantie: equipement.date_fin_garantie || '',\n      en_stock: equipement.en_stock !== undefined ? equipement.en_stock : true,\n      stock_actuel: equipement.stock_actuel || 1,\n      stock_max: equipement.stock_max || 1,\n      stock_min: equipement.stock_min || 1,\n      categorie_id: equipement.categorie_id || ''\n    });\n    setShowModal(true);\n  };\n\n  const handleDelete = async (id) => {\n    if (!window.confirm(\"Êtes-vous sûr de vouloir supprimer cet équipement ?\")) {\n      return;\n    }\n\n    console.log(\"handleDelete - id original:\", id);\n    // S'assurer que l'ID est bien un nombre ou une chaîne simple\n    const cleanId = String(id).split(':')[0];\n    console.log(\"handleDelete - cleanId:\", cleanId);\n\n    try {\n      const token = localStorage.getItem('authToken');\n      const headers = {\n        'Content-Type': 'application/json',\n      };\n      if (token) {\n        headers['Authorization'] = `Bearer ${token}`;\n      }\n\n      const response = await fetch(`http://localhost:8081/api/equipements/${cleanId}`, {\n        method: 'DELETE',\n        headers: headers\n      });\n\n      if (!response.ok) {\n        throw new Error(`Erreur HTTP: ${response.status}`);\n      }\n\n      setEquipements(prev => prev.filter(eq => String(eq.id).split(':')[0] !== cleanId));\n      setFilteredEquipements(prev => prev.filter(eq => String(eq.id).split(':')[0] !== cleanId));\n\n      alert('Équipement supprimé avec succès !');\n\n    } catch (backendError) {\n      console.log(\"Backend non disponible, suppression locale\");\n\n      // Supprimer localement\n      const updatedEquipements = equipements.filter(eq => String(eq.id).split(':')[0] !== cleanId);\n      setEquipements(updatedEquipements);\n      setFilteredEquipements(updatedEquipements);\n      saveToLocalStorage(updatedEquipements);\n\n      alert('Équipement supprimé avec succès (mode hors ligne) !');\n    }\n  };\n\n  const handleSearchChange = (e) => {\n    setSearchTerm(e.target.value);\n  };\n\n  const formatDate = (dateInput) => {\n    if (!dateInput || dateInput === null || dateInput === undefined || dateInput === '') {\n      return 'N/A';\n    }\n\n    try {\n      const dateObj = new Date(dateInput);\n      if (!isNaN(dateObj.getTime())) {\n        return dateObj.toLocaleDateString('fr-FR');\n      }\n      return 'N/A';\n    } catch (e) {\n      console.error(\"Erreur de formatage de date\", e, \"pour la valeur:\", dateInput);\n      return 'N/A';\n    }\n  };\n\n  const handleExportExcel = () => {\n    if (!Array.isArray(filteredEquipements) || filteredEquipements.length === 0) {\n      alert(\"Aucune donnée à exporter\");\n      return;\n    }\n    \n    const columns = [\n      \"Nom\",\n      \"Type\",\n      \"Numéro de série\",\n      \"Statut\",\n      \"Date d'achat\",\n      \"Date fin garantie\",\n      \"Fournisseur\",\n      \"Actions\"\n    ];\n    const rows = filteredEquipements.map(eq => [\n      eq.nom || \"\",\n      eq.type || \"\",\n      eq.numero_serie || eq.numeroSerie || \"\",\n      eq.statut || \"\",\n      formatDate(eq.dateAchat || eq.date_achat) || \"\",\n      formatDate(eq.dateFinGarantie || eq.date_fin_garantie) || \"\",\n      eq.fournisseur?.nom || \"\",\n      \"\"\n    ]);\n    const worksheet = XLSX.utils.aoa_to_sheet([columns, ...rows]);\n    const workbook = XLSX.utils.book_new();\n    XLSX.utils.book_append_sheet(workbook, worksheet, \"Equipements\");\n    XLSX.writeFile(workbook, \"equipements.xlsx\");\n  };\n\n  const handleExportPDF = () => {\n    if (!Array.isArray(filteredEquipements) || filteredEquipements.length === 0) {\n      alert(\"Aucune donnée à exporter\");\n      return;\n    }\n    \n    const doc = new jsPDF();\n    doc.text(\"Liste des équipements\", 14, 16);\n    const columns = [\n      \"Nom\",\n      \"Type\",\n      \"Numéro de série\",\n      \"Statut\",\n      \"Date d'achat\",\n      \"Date fin garantie\",\n      \"Fournisseur\"\n    ];\n    const rows = filteredEquipements.map(eq => [\n      eq.nom || \"\",\n      eq.type || \"\",\n      eq.numero_serie || eq.numeroSerie || \"\",\n      eq.statut || \"\",\n      formatDate(eq.dateAchat || eq.date_achat) || \"\",\n      formatDate(eq.dateFinGarantie || eq.date_fin_garantie) || \"\",\n      eq.fournisseur?.nom || \"\"\n    ]);\n    autoTable(doc, {\n      head: [columns],\n      body: rows,\n      startY: 22,\n      styles: { fontSize: 10 }\n    });\n    doc.save(\"equipements.pdf\");\n  };\n\n  const handlePrint = () => {\n    window.print();\n  };\n\n  // Fonctions pour calculer les statistiques\n  const getStatutStats = () => {\n    const statutCounts = {};\n    filteredEquipements.forEach(eq => {\n      const statut = eq.statut || 'Non défini';\n      statutCounts[statut] = (statutCounts[statut] || 0) + 1;\n    });\n    return statutCounts;\n  };\n\n  const getTypeStats = () => {\n    const typeCounts = {};\n    filteredEquipements.forEach(eq => {\n      const type = eq.type || 'Non défini';\n      typeCounts[type] = (typeCounts[type] || 0) + 1;\n    });\n    return typeCounts;\n  };\n\n  // Configuration des graphiques\n  const statutStats = getStatutStats();\n  const typeStats = getTypeStats();\n\n  const statutChartData = {\n    labels: Object.keys(statutStats),\n    datasets: [{\n      label: 'Nombre d\\'équipements',\n      data: Object.values(statutStats),\n      backgroundColor: [\n        '#4e73df',\n        '#1cc88a',\n        '#36b9cc',\n        '#f6c23e',\n        '#e74a3b'\n      ],\n      borderColor: [\n        '#4e73df',\n        '#1cc88a',\n        '#36b9cc',\n        '#f6c23e',\n        '#e74a3b'\n      ],\n      borderWidth: 1\n    }]\n  };\n\n  const typeChartData = {\n    labels: Object.keys(typeStats),\n    datasets: [{\n      data: Object.values(typeStats),\n      backgroundColor: [\n        '#1976d2',\n        '#43a047',\n        '#d32f2f',\n        '#ffa000',\n        '#9c27b0',\n        '#00acc1',\n        '#8bc34a'\n      ],\n      borderWidth: 2,\n      borderColor: '#fff'\n    }]\n  };\n\n  const chartOptions = {\n    responsive: true,\n    plugins: {\n      legend: {\n        position: 'top',\n      },\n      title: {\n        display: true,\n        text: 'Répartition par statut'\n      }\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        ticks: {\n          stepSize: 1\n        }\n      }\n    }\n  };\n\n  const doughnutOptions = {\n    responsive: true,\n    plugins: {\n      legend: {\n        position: 'right',\n      },\n      title: {\n        display: true,\n        text: 'Répartition par type'\n      }\n    }\n  };\n\n  return (\n    <div className=\"equipements-container\">\n      {/* Sidebar */}\n      <div className=\"sidebar\">\n        <div className=\"logo-section\">\n          <img src={logo} alt=\"Logo Entreprise\" className=\"company-logo\" />\n        </div>\n        <nav className=\"main-menu\">\n          <ul>\n            <li>\n              <Link to=\"/\" className={location.pathname === '/' ? 'home-active active' : ''}>\n                <FontAwesomeIcon icon={faLaptop} style={{marginRight:8}} /> Accueil\n              </Link>\n            </li>\n            <li>\n              <Link to=\"/equipements\" className={location.pathname === '/equipements' ? 'active' : ''}>\n                <FontAwesomeIcon icon={faLaptop} style={{marginRight:8}} /> Gérer les équipements\n              </Link>\n            </li>\n            <li>\n              <Link to=\"/employes\" className={location.pathname === '/employes' ? 'active' : ''}>\n                <FontAwesomeIcon icon={faUsers} style={{marginRight:8}} /> Gérer les employés\n              </Link>\n            </li>\n            <li>\n              <Link to=\"/affectations\" className={location.pathname === '/affectations' ? 'active' : ''}>\n                <FontAwesomeIcon icon={faExchangeAlt} style={{marginRight:8}} /> Suivi des affectations\n              </Link>\n            </li>\n            <li>\n              <Link to=\"/fournisseurs\" className={location.pathname === '/fournisseurs' ? 'active' : ''}>\n                <FontAwesomeIcon icon={faTruck} style={{marginRight:8}} /> Gérer les fournisseurs\n              </Link>\n            </li>\n          </ul>\n        </nav>\n        <div className=\"secondary-links\">\n          <Link to=\"/profil\">\n            <FontAwesomeIcon icon={faUserCog} style={{marginRight:8}} /> Mon profil & paramètres\n          </Link>\n        </div>\n        <div style={{ marginTop: 'auto', padding: '20px 0 0 0', textAlign: 'center' }}>\n          <button\n            className=\"logout-btn\"\n            onClick={() => {\n              localStorage.removeItem('authToken');\n              window.location.href = '/';\n            }}\n          >\n            <FontAwesomeIcon icon={faSignOutAlt} style={{marginRight:8}} /> Déconnexion\n          </button>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"equipements-content\">\n        {/* Dashboard header row */}\n        <div className=\"dashboard-header-row\" style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: 32 }}>\n          <div className=\"search-bar\" style={{ flex: 1, maxWidth: 480, marginRight: 24, position: 'relative' }}>\n            <FontAwesomeIcon icon={faSearch} style={{ position: 'absolute', left: 14, top: '50%', transform: 'translateY(-50%)', color: '#1976d2', fontSize: '1.1rem', opacity: 0.8 }} />\n            <input\n              type=\"text\"\n              placeholder=\"Recherche par mot clé\"\n              value={searchTerm}\n              onChange={handleSearchChange}\n              style={{ width: '100%', padding: '10px 16px 10px 38px', borderRadius: 8, border: '1px solid #dbeafe', fontSize: '1rem', boxShadow: '0 2px 8px rgba(21,101,192,0.06)' }}\n            />\n          </div>\n          <div className=\"profile-block\" style={{ display: 'flex', alignItems: 'center', gap: 20 }}>\n            <div className=\"notification-icon\" style={{ position: 'relative', marginRight: 8, cursor: 'pointer' }}>\n              <FontAwesomeIcon icon={faBell} style={{ fontSize: '1.3rem', color: '#1976d2' }} />\n              <span style={{ position: 'absolute', top: -6, right: -6, background: '#e74a3b', color: '#fff', borderRadius: '50%', fontSize: '0.7rem', padding: '2px 6px', fontWeight: 600 }}>3</span>\n            </div>\n            <span style={{ fontWeight: 500, color: '#2e3a4e', fontSize: '1rem', marginRight: 8 }}>Responsable IT</span>\n            <FontAwesomeIcon icon={faUserCog} style={{ fontSize: '1.5rem', color: '#1976d2', background: '#fff', borderRadius: '50%', padding: 6, boxShadow: '0 2px 8px rgba(21,101,192,0.10)' }} />\n          </div>\n        </div>\n\n        <h2 className=\"equipements-title\">\n          Liste des équipements ({filteredEquipements.length})\n        </h2>\n\n        {/* Message d'erreur si problème backend */}\n        {error && (\n          <div style={{\n            background: '#fff3cd',\n            border: '1px solid #ffeaa7',\n            borderRadius: 8,\n            padding: 16,\n            marginBottom: 20,\n            color: '#856404',\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          }}>\n            <div>\n              <strong>⚠️ Attention :</strong> {error}\n            </div>\n\n          </div>\n        )}\n        \n        <div style={{ display: 'flex', gap: 16, marginBottom: 24 }}>\n          <button\n            className=\"btn btn-primary\"\n            style={{ fontWeight: 700, fontSize: '1rem', borderRadius: 10, background: 'linear-gradient(90deg, #1976d2 0%, #42a5f5 100%)', color: '#fff', boxShadow: '0 4px 16px rgba(21,101,192,0.18)' }}\n            onClick={() => {\n              setEditingId(null);\n              setShowModal(true);\n            }}\n          >\n            <FontAwesomeIcon icon={faPlus} style={{marginRight: 8}} />\n            Ajouter un équipement\n          </button>\n          <button className=\"btn btn-primary\" style={{ fontWeight: 700, fontSize: '1rem', borderRadius: 10, background: 'linear-gradient(90deg, #43a047 0%, #66bb6a 100%)', color: '#fff', boxShadow: '0 4px 16px rgba(67,160,71,0.18)' }} onClick={handleExportExcel}>\n            Exporter Excel\n          </button>\n          <button className=\"btn btn-primary\" style={{ fontWeight: 700, fontSize: '1rem', borderRadius: 10, background: 'linear-gradient(90deg, #d32f2f 0%, #e57373 100%)', color: '#fff', boxShadow: '0 4px 16px rgba(211,47,47,0.18)' }} onClick={handleExportPDF}>\n            Exporter PDF\n          </button>\n          <button className=\"btn btn-primary\" style={{ fontWeight: 700, fontSize: '1rem', borderRadius: 10, background: 'linear-gradient(90deg, #ffa000 0%, #ffd54f 100%)', color: '#fff', boxShadow: '0 4px 16px rgba(255,160,0,0.18)' }} onClick={handlePrint}>\n            Imprimer\n          </button>\n          <button\n            className=\"btn btn-primary\"\n            style={{ fontWeight: 700, fontSize: '1rem', borderRadius: 10, background: 'linear-gradient(90deg, #9c27b0 0%, #ba68c8 100%)', color: '#fff', boxShadow: '0 4px 16px rgba(156,39,176,0.18)' }}\n            onClick={() => setShowStats(!showStats)}\n          >\n            {showStats ? 'Masquer' : 'Afficher'} les statistiques\n          </button>\n        </div>\n\n        {/* Section des statistiques */}\n        {showStats && (\n          <div style={{\n            background: '#fff',\n            borderRadius: 12,\n            padding: 24,\n            marginBottom: 24,\n            boxShadow: '0 4px 16px rgba(0,0,0,0.1)',\n            border: '1px solid #e0e0e0'\n          }}>\n            <h3 style={{\n              color: '#1976d2',\n              marginBottom: 24,\n              fontSize: '1.3rem',\n              fontWeight: 700,\n              textAlign: 'center'\n            }}>\n              📊 Statistiques des équipements\n            </h3>\n\n            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 32, alignItems: 'center' }}>\n              {/* Graphique en barres pour les statuts */}\n              <div style={{\n                background: '#fafafa',\n                padding: 20,\n                borderRadius: 8,\n                border: '1px solid #e0e0e0',\n                height: '350px'\n              }}>\n                <Bar data={statutChartData} options={chartOptions} />\n              </div>\n\n              {/* Graphique en donut pour les types */}\n              <div style={{\n                background: '#fafafa',\n                padding: 20,\n                borderRadius: 8,\n                border: '1px solid #e0e0e0',\n                height: '350px'\n              }}>\n                <Doughnut data={typeChartData} options={doughnutOptions} />\n              </div>\n            </div>\n\n            {/* Résumé textuel */}\n            <div style={{\n              marginTop: 24,\n              padding: 16,\n              background: '#f8f9fa',\n              borderRadius: 8,\n              border: '1px solid #dee2e6'\n            }}>\n              <h4 style={{ color: '#495057', marginBottom: 12, fontSize: '1.1rem' }}>📈 Résumé</h4>\n              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: 16 }}>\n                <div style={{ textAlign: 'center', padding: 12, background: '#fff', borderRadius: 6, border: '1px solid #e9ecef' }}>\n                  <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#1976d2' }}>\n                    {filteredEquipements.length}\n                  </div>\n                  <div style={{ color: '#6c757d', fontSize: '0.9rem' }}>Total équipements</div>\n                </div>\n                <div style={{ textAlign: 'center', padding: 12, background: '#fff', borderRadius: 6, border: '1px solid #e9ecef' }}>\n                  <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#28a745' }}>\n                    {statutStats['DISPONIBLE'] || 0}\n                  </div>\n                  <div style={{ color: '#6c757d', fontSize: '0.9rem' }}>Disponibles</div>\n                </div>\n                <div style={{ textAlign: 'center', padding: 12, background: '#fff', borderRadius: 6, border: '1px solid #e9ecef' }}>\n                  <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#ffc107' }}>\n                    {statutStats['EN_MAINTENANCE'] || 0}\n                  </div>\n                  <div style={{ color: '#6c757d', fontSize: '0.9rem' }}>En maintenance</div>\n                </div>\n                <div style={{ textAlign: 'center', padding: 12, background: '#fff', borderRadius: 6, border: '1px solid #e9ecef' }}>\n                  <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#dc3545' }}>\n                    {statutStats['HORS_SERVICE'] || 0}\n                  </div>\n                  <div style={{ color: '#6c757d', fontSize: '0.9rem' }}>Hors service</div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Modal d'ajout/modification d'équipement */}\n        {showModal && (\n          <div style={{\n            position: 'fixed',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            backgroundColor: 'rgba(0, 0, 0, 0.5)',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            zIndex: 1000\n          }}>\n            <div style={{\n              backgroundColor: 'white',\n              borderRadius: 12,\n              padding: 32,\n              maxWidth: 600,\n              width: '90%',\n              maxHeight: '90vh',\n              overflowY: 'auto',\n              boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)'\n            }}>\n              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>\n                <h3 style={{ margin: 0, color: '#1976d2', fontSize: '1.5rem', fontWeight: 700 }}>\n                  {editingId ? 'Modifier un équipement' : 'Ajouter un nouvel équipement'}\n                </h3>\n                <button\n                  onClick={() => {\n                    setShowModal(false);\n                    setEditingId(null);\n                  }}\n                  style={{\n                    background: 'none',\n                    border: 'none',\n                    fontSize: '1.5rem',\n                    color: '#666',\n                    cursor: 'pointer',\n                    padding: 8,\n                    borderRadius: '50%'\n                  }}\n                >\n                  <FontAwesomeIcon icon={faTimes} />\n                </button>\n              </div>\n\n              <form onSubmit={handleSubmit}>\n                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 16, marginBottom: 16 }}>\n                  <div>\n                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>\n                      Nom de l'équipement *\n                    </label>\n                    <input\n                      type=\"text\"\n                      name=\"nom\"\n                      value={formData.nom}\n                      onChange={handleInputChange}\n                      required\n                      style={{\n                        width: '100%',\n                        padding: '12px',\n                        border: '2px solid #e0e0e0',\n                        borderRadius: 8,\n                        fontSize: '1rem',\n                        transition: 'border-color 0.2s'\n                      }}\n                      onFocus={(e) => e.target.style.borderColor = '#1976d2'}\n                      onBlur={(e) => e.target.style.borderColor = '#e0e0e0'}\n                    />\n                  </div>\n\n                  <div>\n                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>\n                      Type *\n                    </label>\n                    <select\n                      name=\"type\"\n                      value={formData.type}\n                      onChange={handleInputChange}\n                      required\n                      style={{\n                        width: '100%',\n                        padding: '12px',\n                        border: '2px solid #e0e0e0',\n                        borderRadius: 8,\n                        fontSize: '1rem'\n                      }}\n                    >\n                      <option value=\"\">Sélectionner un type</option>\n                      {typeOptions.map(type => (\n                        <option key={type} value={type}>{type}</option>\n                      ))}\n                    </select>\n                  </div>\n                </div>\n\n                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 16, marginBottom: 16 }}>\n                  <div>\n                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>\n                      Numéro de série\n                    </label>\n                    <input\n                      type=\"text\"\n                      name=\"numero_serie\"\n                      value={formData.numero_serie}\n                      onChange={handleInputChange}\n                      style={{\n                        width: '100%',\n                        padding: '12px',\n                        border: '2px solid #e0e0e0',\n                        borderRadius: 8,\n                        fontSize: '1rem'\n                      }}\n                    />\n                  </div>\n\n                  <div>\n                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>\n                      Statut *\n                    </label>\n                    <select\n                      name=\"statut\"\n                      value={formData.statut}\n                      onChange={handleInputChange}\n                      required\n                      style={{\n                        width: '100%',\n                        padding: '12px',\n                        border: '2px solid #e0e0e0',\n                        borderRadius: 8,\n                        fontSize: '1rem'\n                      }}\n                    >\n                      {statutOptions.map(statut => (\n                        <option key={statut} value={statut}>{statut}</option>\n                      ))}\n                    </select>\n                  </div>\n                </div>\n\n                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 16, marginBottom: 16 }}>\n                  <div>\n                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>\n                      Date d'achat\n                    </label>\n                    <input\n                      type=\"date\"\n                      name=\"date_achat\"\n                      value={formData.date_achat}\n                      onChange={handleInputChange}\n                      style={{\n                        width: '100%',\n                        padding: '12px',\n                        border: '2px solid #e0e0e0',\n                        borderRadius: 8,\n                        fontSize: '1rem'\n                      }}\n                    />\n                  </div>\n\n                  <div>\n                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>\n                      Fournisseur\n                    </label>\n                    <select\n                      name=\"fournisseur_id\"\n                      value={formData.fournisseur_id}\n                      onChange={handleInputChange}\n                      style={{\n                        width: '100%',\n                        padding: '12px',\n                        border: '2px solid #e0e0e0',\n                        borderRadius: 8,\n                        fontSize: '1rem'\n                      }}\n                    >\n                      <option value=\"\">Sélectionner un fournisseur</option>\n                      {fournisseurs.map(fournisseur => (\n                        <option key={fournisseur.id} value={fournisseur.id}>\n                          {fournisseur.nom}\n                        </option>\n                      ))}\n                    </select>\n                  </div>\n                </div>\n\n                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 16, marginBottom: 16 }}>\n                  <div>\n                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>\n                      Début de garantie\n                    </label>\n                    <input\n                      type=\"date\"\n                      name=\"date_debut_garantie\"\n                      value={formData.date_debut_garantie}\n                      onChange={handleInputChange}\n                      style={{\n                        width: '100%',\n                        padding: '12px',\n                        border: '2px solid #e0e0e0',\n                        borderRadius: 8,\n                        fontSize: '1rem'\n                      }}\n                    />\n                  </div>\n\n                  <div>\n                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>\n                      Fin de garantie\n                    </label>\n                    <input\n                      type=\"date\"\n                      name=\"date_fin_garantie\"\n                      value={formData.date_fin_garantie}\n                      onChange={handleInputChange}\n                      style={{\n                        width: '100%',\n                        padding: '12px',\n                        border: '2px solid #e0e0e0',\n                        borderRadius: 8,\n                        fontSize: '1rem'\n                      }}\n                    />\n                  </div>\n                </div>\n\n                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: 16, marginBottom: 16 }}>\n                  <div>\n                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>\n                      Stock actuel\n                    </label>\n                    <input\n                      type=\"number\"\n                      name=\"stock_actuel\"\n                      value={formData.stock_actuel}\n                      onChange={handleInputChange}\n                      min=\"0\"\n                      style={{\n                        width: '100%',\n                        padding: '12px',\n                        border: '2px solid #e0e0e0',\n                        borderRadius: 8,\n                        fontSize: '1rem'\n                      }}\n                    />\n                  </div>\n\n                  <div>\n                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>\n                      Stock maximum\n                    </label>\n                    <input\n                      type=\"number\"\n                      name=\"stock_max\"\n                      value={formData.stock_max}\n                      onChange={handleInputChange}\n                      min=\"1\"\n                      style={{\n                        width: '100%',\n                        padding: '12px',\n                        border: '2px solid #e0e0e0',\n                        borderRadius: 8,\n                        fontSize: '1rem'\n                      }}\n                    />\n                  </div>\n\n                  <div>\n                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>\n                      Stock minimum\n                    </label>\n                    <input\n                      type=\"number\"\n                      name=\"stock_min\"\n                      value={formData.stock_min}\n                      onChange={handleInputChange}\n                      min=\"0\"\n                      style={{\n                        width: '100%',\n                        padding: '12px',\n                        border: '2px solid #e0e0e0',\n                        borderRadius: 8,\n                        fontSize: '1rem'\n                      }}\n                    />\n                  </div>\n                </div>\n\n                <div style={{ marginBottom: 24 }}>\n                  <label style={{ display: 'flex', alignItems: 'center', fontWeight: 600, color: '#333', cursor: 'pointer' }}>\n                    <input\n                      type=\"checkbox\"\n                      name=\"en_stock\"\n                      checked={formData.en_stock}\n                      onChange={handleInputChange}\n                      style={{ marginRight: 8, transform: 'scale(1.2)' }}\n                    />\n                    Équipement en stock\n                  </label>\n                </div>\n\n                <div style={{ display: 'flex', gap: 12, justifyContent: 'flex-end' }}>\n                  <button\n                    type=\"button\"\n                    onClick={() => {\n                      setShowModal(false);\n                      setEditingId(null);\n                    }}\n                    style={{\n                      padding: '12px 24px',\n                      border: '2px solid #e0e0e0',\n                      borderRadius: 8,\n                      background: 'white',\n                      color: '#666',\n                      fontSize: '1rem',\n                      fontWeight: 600,\n                      cursor: 'pointer'\n                    }}\n                  >\n                    Annuler\n                  </button>\n                  <button\n                    type=\"submit\"\n                    disabled={submitting}\n                    style={{\n                      padding: '12px 24px',\n                      border: 'none',\n                      borderRadius: 8,\n                      background: submitting ? '#ccc' : 'linear-gradient(90deg, #1976d2 0%, #42a5f5 100%)',\n                      color: 'white',\n                      fontSize: '1rem',\n                      fontWeight: 600,\n                      cursor: submitting ? 'not-allowed' : 'pointer',\n                      boxShadow: '0 4px 16px rgba(21,101,192,0.18)'\n                    }}\n                  >\n                    {submitting \n                      ? (editingId ? 'Modification en cours...' : 'Ajout en cours...') \n                      : (editingId ? 'Modifier l\\'équipement' : 'Ajouter l\\'équipement')}\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        )}\n\n        {/* Contenu principal */}\n        {loading ? (\n          <div style={{ textAlign: 'center', padding: '40px' }}>\n            <p>Chargement des équipements...</p>\n          </div>\n        ) : error ? (\n          <div style={{ textAlign: 'center', padding: '40px', color: '#e74a3b' }}>\n            <p>Erreur lors du chargement : {error}</p>\n            <button \n              onClick={() => window.location.reload()} \n              style={{ marginTop: '10px', padding: '8px 16px', background: '#1976d2', color: 'white', border: 'none', borderRadius: '4px', cursor: 'pointer' }}\n            >\n              Actualiser la page\n            </button>\n          </div>\n        ) : !Array.isArray(filteredEquipements) || filteredEquipements.length === 0 ? (\n          <div style={{ textAlign: 'center', padding: '40px' }}>\n            <p>{searchTerm ? 'Aucun résultat trouvé' : 'Aucun équipement trouvé'}</p>\n            {console.log(\"🚫 Aucun équipement à afficher. État actuel:\", {\n              filteredEquipements,\n              isArray: Array.isArray(filteredEquipements),\n              length: filteredEquipements?.length,\n              searchTerm\n            })}\n          </div>\n        ) : (\n          <table className=\"equipements-table\">\n            <thead>\n              <tr>\n                <th>Nom</th>\n                <th>Type</th>\n                <th>Numéro de série</th>\n                <th>Statut</th>\n                <th>Date d'achat</th>\n                <th>Date fin garantie</th>\n                <th>Fournisseur</th>\n                <th>Actions</th>\n              </tr>\n            </thead>\n            <tbody>\n              {filteredEquipements.map((eq, index) => {\n                console.log(`🔍 Rendu équipement ${index}:`, eq);\n                const numeroSerie = eq.numero_serie || eq.numeroSerie || 'N/A';\n                const dateAchat = formatDate(eq.dateAchat || eq.date_achat);\n                const dateFinGarantie = formatDate(eq.dateFinGarantie || eq.date_fin_garantie);\n                const fournisseur = eq.fournisseur?.nom || eq.fournisseur_id || 'Non spécifié';\n\n                console.log(`📝 Valeurs calculées pour ${eq.nom}:`, {\n                  nom: eq.nom,\n                  type: eq.type,\n                  numeroSerie,\n                  statut: eq.statut,\n                  dateAchat,\n                  dateFinGarantie,\n                  fournisseur\n                });\n\n                return (\n                  <tr key={eq.id || index}>\n                    <td>{eq.nom || 'N/A'}</td>\n                    <td>{eq.type || 'N/A'}</td>\n                    <td>{numeroSerie}</td>\n                    <td>{eq.statut || 'N/A'}</td>\n                    <td>{dateAchat}</td>\n                    <td>{dateFinGarantie}</td>\n                    <td>{fournisseur}</td>\n                    <td>\n                      <div style={{ display: 'flex', gap: '8px' }}>\n                        <button \n                          onClick={() => handleEdit(eq)}\n                          style={{\n                            background: '#1976d2',\n                            color: 'white',\n                            border: 'none',\n                            borderRadius: '4px',\n                            padding: '6px 12px',\n                            cursor: 'pointer',\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '4px'\n                          }}\n                        >\n                          <FontAwesomeIcon icon={faEdit} />\n                          <span>Modifier</span>\n                        </button>\n                        <button \n                          onClick={() => handleDelete(eq.id)}\n                          style={{\n                            background: '#e74a3b',\n                            color: 'white',\n                            border: 'none',\n                            borderRadius: '4px',\n                            padding: '6px 12px',\n                            cursor: 'pointer',\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '4px'\n                          }}\n                        >\n                          <FontAwesomeIcon icon={faTrash} />\n                          <span>Supprimer</span>\n                        </button>\n                      </div>\n                    </td>\n                  </tr>\n                );\n              })}\n            </tbody>\n          </table>\n        )}\n      </div>\n    </div>\n  );\n}"], "mappings": ";;AAAA,OAAO,mBAAmB;AAC1B,SAASA,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,OAAOC,IAAI,MAAM,kCAAkC;AACnD,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,QAAQ,EAAEC,OAAO,EAAEC,aAAa,EAAEC,OAAO,EAAEC,SAAS,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAEC,MAAM,EAAEC,MAAM,EAAEC,OAAO,QAAQ,mCAAmC;AAC1K,SAASC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAO,KAAKC,IAAI,MAAM,MAAM;AAC5B,SAASC,KAAK,IAAIC,OAAO,EAAEC,aAAa,EAAEC,WAAW,EAAEC,UAAU,EAAEC,KAAK,EAAEC,OAAO,EAAEC,MAAM,EAAEC,UAAU,QAAQ,UAAU;AACvH,SAASC,GAAG,EAAEC,QAAQ,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhDX,OAAO,CAACY,QAAQ,CAACX,aAAa,EAAEC,WAAW,EAAEC,UAAU,EAAEC,KAAK,EAAEC,OAAO,EAAEC,MAAM,EAAEC,UAAU,CAAC;AAE5F,eAAe,SAASM,WAAWA,CAAA,EAAG;EAAAC,EAAA;EACpC,MAAMC,QAAQ,GAAGpC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACqC,WAAW,EAAEC,cAAc,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACuB,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC2B,KAAK,EAAEC,QAAQ,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC6B,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC+B,YAAY,EAAEC,eAAe,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACiC,UAAU,EAAEC,aAAa,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACmC,UAAU,EAAEC,aAAa,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACqC,UAAU,EAAEC,aAAa,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACuC,SAAS,EAAEC,YAAY,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACyC,SAAS,EAAEC,YAAY,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;;EAEjD;EACA,MAAM,CAAC2C,QAAQ,EAAEC,WAAW,CAAC,GAAG5C,QAAQ,CAAC;IACvC6C,GAAG,EAAE,EAAE;IACPC,IAAI,EAAE,EAAE;IACRC,YAAY,EAAE,EAAE;IAChBC,MAAM,EAAE,YAAY;IACpBC,UAAU,EAAE,EAAE;IACdC,cAAc,EAAE,EAAE;IAClBC,mBAAmB,EAAE,EAAE;IACvBC,iBAAiB,EAAE,EAAE;IACrBC,QAAQ,EAAE,IAAI;IACdC,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE,CAAC;IACZC,SAAS,EAAE,CAAC;IACZC,YAAY,EAAE;EAChB,CAAC,CAAC;;EAEF;EACA,MAAMC,aAAa,GAAG,CAAC,YAAY,EAAE,gBAAgB,EAAE,QAAQ,EAAE,cAAc,CAAC;EAChF,MAAMC,WAAW,GAAG,CAAC,YAAY,EAAE,YAAY,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,SAAS,EAAE,OAAO,CAAC;EAExG5D,SAAS,CAAC,MAAM;IACd6D,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;IACnE;IACAC,YAAY,CAACC,UAAU,CAAC,kBAAkB,CAAC;IAE3CC,eAAe,CAAC,CAAC;IACjBC,gBAAgB,CAAC,CAAC;IAClBC,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;;EAIN;EACA,MAAMC,kBAAkB,GAAI9C,WAAW,IAAK;IAC1CyC,YAAY,CAACM,OAAO,CAAC,kBAAkB,EAAEC,IAAI,CAACC,SAAS,CAACjD,WAAW,CAAC,CAAC;EACvE,CAAC;;EAED;EACA,MAAMkD,aAAa,GAAIlD,WAAW,IAAK;IACrC,MAAMmD,KAAK,GAAGnD,WAAW,CAACoD,MAAM,CAAC,CAACC,GAAG,EAAEC,EAAE,KAAKC,IAAI,CAACF,GAAG,CAACA,GAAG,EAAEC,EAAE,CAACE,EAAE,CAAC,EAAE,CAAC,CAAC;IACtE,OAAOL,KAAK,GAAG,CAAC;EAClB,CAAC;EAEDzE,SAAS,CAAC,MAAM;IACd,IAAIsC,UAAU,KAAK,EAAE,EAAE;MACrBb,sBAAsB,CAACH,WAAW,CAAC;IACrC,CAAC,MAAM;MACL,MAAMyD,QAAQ,GAAGzD,WAAW,CAAC0D,MAAM,CAACC,UAAU;QAAA,IAAAC,qBAAA;QAAA,OAC5CD,UAAU,CAACnC,GAAG,CAACqC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC9C,UAAU,CAAC6C,WAAW,CAAC,CAAC,CAAC,IAC/DF,UAAU,CAAClC,IAAI,CAACoC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC9C,UAAU,CAAC6C,WAAW,CAAC,CAAC,CAAC,IAC/DF,UAAU,CAACjC,YAAY,IAAIiC,UAAU,CAACjC,YAAY,CAACmC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC9C,UAAU,CAAC6C,WAAW,CAAC,CAAC,CAAE,IACrGF,UAAU,CAAChC,MAAM,CAACkC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC9C,UAAU,CAAC6C,WAAW,CAAC,CAAC,CAAC,IACjE,EAAAD,qBAAA,GAAAD,UAAU,CAACI,WAAW,cAAAH,qBAAA,uBAAtBA,qBAAA,CAAwBpC,GAAG,KAAImC,UAAU,CAACI,WAAW,CAACvC,GAAG,CAACqC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC9C,UAAU,CAAC6C,WAAW,CAAC,CAAC,CAAE;MAAA,CAC9G,CAAC;MACD1D,sBAAsB,CAACsD,QAAQ,CAAC;IAClC;EACF,CAAC,EAAE,CAACzC,UAAU,EAAEhB,WAAW,CAAC,CAAC;EAE7B,MAAM2C,eAAe,GAAGA,CAAA,KAAM;IAC5BJ,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;IAC5CnC,UAAU,CAAC,IAAI,CAAC;IAEhB,MAAM2D,KAAK,GAAGvB,YAAY,CAACwB,OAAO,CAAC,WAAW,CAAC;IAC/C,MAAMC,OAAO,GAAG;MACd,cAAc,EAAE;IAClB,CAAC;IACD,IAAIF,KAAK,EAAE;MACTE,OAAO,CAAC,eAAe,CAAC,GAAG,UAAUF,KAAK,EAAE;IAC9C;IAEAG,KAAK,CAAC,uCAAuC,EAAE;MAC7CC,MAAM,EAAE,KAAK;MACbF,OAAO,EAAEA;IACX,CAAC,CAAC,CACCG,IAAI,CAACC,GAAG,IAAI;MACX/B,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE8B,GAAG,CAACC,MAAM,CAAC;MAC5D,IAAI,CAACD,GAAG,CAACE,EAAE,EAAE;QACX,MAAM,IAAIC,KAAK,CAAC,gBAAgBH,GAAG,CAACC,MAAM,EAAE,CAAC;MAC/C;MACA,OAAOD,GAAG,CAACI,IAAI,CAAC,CAAC;IACnB,CAAC,CAAC,CACDL,IAAI,CAACM,IAAI,IAAI;MACZpC,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEmC,IAAI,CAAC;MAC7DpC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEmC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEC,MAAM,CAAC;MACrD,IAAIC,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,EAAE;QACvBpC,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;QAChEvC,cAAc,CAAC0E,IAAI,CAAC;QACpBxE,sBAAsB,CAACwE,IAAI,CAAC;QAC5BpE,QAAQ,CAAC,IAAI,CAAC;QACd;QACAkC,YAAY,CAACM,OAAO,CAAC,kBAAkB,EAAEC,IAAI,CAACC,SAAS,CAAC0B,IAAI,CAAC,CAAC;MAChE,CAAC,MAAM;QACLpC,OAAO,CAACwC,IAAI,CAAC,oDAAoD,EAAEJ,IAAI,CAAC;QACxE,MAAM,IAAIF,KAAK,CAAC,6BAA6B,CAAC;MAChD;MACApE,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,CACD2E,KAAK,CAACC,GAAG,IAAI;MACZ1C,OAAO,CAACjC,KAAK,CAAC,4CAA4C,EAAE2E,GAAG,CAAC;;MAEhE;MACA,MAAMC,QAAQ,GAAG,CACf;QACE1B,EAAE,EAAE,CAAC;QACLhC,GAAG,EAAE,MAAM;QACXC,IAAI,EAAE,YAAY;QAClBC,YAAY,EAAE,WAAW;QACzBC,MAAM,EAAE,YAAY;QACpBC,UAAU,EAAE,YAAY;QACxBG,iBAAiB,EAAE,YAAY;QAC/BgC,WAAW,EAAE,IAAI;QACjBlC,cAAc,EAAE;MAClB,CAAC,EACD;QACE2B,EAAE,EAAE,CAAC;QACLhC,GAAG,EAAE,cAAc;QACnBC,IAAI,EAAE,YAAY;QAClBC,YAAY,EAAE,EAAE;QAChBC,MAAM,EAAE,YAAY;QACpBC,UAAU,EAAE,YAAY;QACxBG,iBAAiB,EAAE,YAAY;QAC/BgC,WAAW,EAAE;UAAEP,EAAE,EAAE,CAAC;UAAEhC,GAAG,EAAE;QAAgB,CAAC;QAC5CK,cAAc,EAAE;MAClB,CAAC,EACD;QACE2B,EAAE,EAAE,CAAC;QACLhC,GAAG,EAAE,OAAO;QACZC,IAAI,EAAE,YAAY;QAClBC,YAAY,EAAE,UAAU;QACxBC,MAAM,EAAE,gBAAgB;QACxBC,UAAU,EAAE,YAAY;QACxBG,iBAAiB,EAAE,YAAY;QAC/BgC,WAAW,EAAE;UAAEP,EAAE,EAAE,CAAC;UAAEhC,GAAG,EAAE;QAAgB,CAAC;QAC5CK,cAAc,EAAE;MAClB,CAAC,CACF;MAEDU,OAAO,CAACC,GAAG,CAAC,gEAAgE,EAAE0C,QAAQ,CAAC;MACvF3C,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE0C,QAAQ,CAACN,MAAM,CAAC;MACpE3E,cAAc,CAACiF,QAAQ,CAAC;MACxB/E,sBAAsB,CAAC+E,QAAQ,CAAC;MAChCzC,YAAY,CAACM,OAAO,CAAC,kBAAkB,EAAEC,IAAI,CAACC,SAAS,CAACiC,QAAQ,CAAC,CAAC;MAClE3E,QAAQ,CAAC,0EAA0E,CAAC;MACpFF,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC;EACN,CAAC;EAED,MAAMuC,gBAAgB,GAAGA,CAAA,KAAM;IAC7BL,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;IAE7C,MAAMwB,KAAK,GAAGvB,YAAY,CAACwB,OAAO,CAAC,WAAW,CAAC;IAC/C,MAAMC,OAAO,GAAG;MACd,cAAc,EAAE;IAClB,CAAC;IACD,IAAIF,KAAK,EAAE;MACTE,OAAO,CAAC,eAAe,CAAC,GAAG,UAAUF,KAAK,EAAE;IAC9C;IAEAG,KAAK,CAAC,wCAAwC,EAAE;MAC9CC,MAAM,EAAE,KAAK;MACbF,OAAO,EAAEA;IACX,CAAC,CAAC,CACCG,IAAI,CAACC,GAAG,IAAI;MACX/B,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE8B,GAAG,CAACC,MAAM,CAAC;MAC7D,IAAI,CAACD,GAAG,CAACE,EAAE,EAAE;QACX,MAAM,IAAIC,KAAK,CAAC,gBAAgBH,GAAG,CAACC,MAAM,EAAE,CAAC;MAC/C;MACA,OAAOD,GAAG,CAACI,IAAI,CAAC,CAAC;IACnB,CAAC,CAAC,CACDL,IAAI,CAACM,IAAI,IAAI;MACZpC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEmC,IAAI,CAAC;MACjD,IAAIE,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,EAAE;QACvBhE,eAAe,CAACgE,IAAI,CAAC;QACrBpC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEmC,IAAI,CAACC,MAAM,EAAE,UAAU,CAAC;MAC/D,CAAC,MAAM;QACLrC,OAAO,CAACwC,IAAI,CAAC,kDAAkD,EAAEJ,IAAI,CAAC;QACtEhE,eAAe,CAAC,EAAE,CAAC;MACrB;IACF,CAAC,CAAC,CACDqE,KAAK,CAACC,GAAG,IAAI;MACZ1C,OAAO,CAACjC,KAAK,CAAC,6CAA6C,EAAE2E,GAAG,CAAC;MACjEtE,eAAe,CAAC,EAAE,CAAC;IACrB,CAAC,CAAC;EACN,CAAC;EAED,MAAMkC,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMmB,KAAK,GAAGvB,YAAY,CAACwB,OAAO,CAAC,WAAW,CAAC;IAC/C,MAAMC,OAAO,GAAG;MACd,cAAc,EAAE;IAClB,CAAC;IACD,IAAIF,KAAK,EAAE;MACTE,OAAO,CAAC,eAAe,CAAC,GAAG,UAAUF,KAAK,EAAE;IAC9C;IAEAG,KAAK,CAAC,sCAAsC,EAAE;MAC5CC,MAAM,EAAE,KAAK;MACbF,OAAO,EAAEA;IACX,CAAC,CAAC,CACCG,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACE,EAAE,GAAGF,GAAG,CAACI,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,CACrCL,IAAI,CAACM,IAAI,IAAI9D,aAAa,CAACgE,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,GAAGA,IAAI,GAAG,EAAE,CAAC,CAAC,CAC5DK,KAAK,CAACC,GAAG,IAAI;MACZ1C,OAAO,CAACjC,KAAK,CAAC,2CAA2C,EAAE2E,GAAG,CAAC;MAC/DpE,aAAa,CAAC,EAAE,CAAC;IACnB,CAAC,CAAC;EACN,CAAC;EAED,MAAMsE,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC,KAAK;MAAE7D,IAAI;MAAE8D;IAAQ,CAAC,GAAGH,CAAC,CAACI,MAAM;IAC/CjE,WAAW,CAACkE,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACJ,IAAI,GAAG5D,IAAI,KAAK,UAAU,GAAG8D,OAAO,GAAGD;IAC1C,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMI,YAAY,GAAG,MAAON,CAAC,IAAK;IAChCA,CAAC,CAACO,cAAc,CAAC,CAAC;IAClB5E,aAAa,CAAC,IAAI,CAAC;IAEnBwB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEtB,SAAS,CAAC;IACnDqB,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAElB,QAAQ,CAAC;IAEjD,MAAMsE,UAAU,GAAG;MACjB,GAAGtE,QAAQ;MACXO,cAAc,EAAEP,QAAQ,CAACO,cAAc,IAAI;IAC7C,CAAC;;IAED;IACA,IAAI;MACF,MAAMmC,KAAK,GAAGvB,YAAY,CAACwB,OAAO,CAAC,WAAW,CAAC;MAC/C,MAAMC,OAAO,GAAG;QACd,cAAc,EAAE;MAClB,CAAC;MACD,IAAIF,KAAK,EAAE;QACTE,OAAO,CAAC,eAAe,CAAC,GAAG,UAAUF,KAAK,EAAE;MAC9C;MAEA,MAAM6B,GAAG,GAAG3E,SAAS,GACjB,yCAAyCA,SAAS,EAAE,GACpD,uCAAuC;MAE3CqB,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEqD,GAAG,CAAC;MAChC,MAAMzB,MAAM,GAAGlD,SAAS,GAAG,KAAK,GAAG,MAAM;MAEzC,MAAM4E,QAAQ,GAAG,MAAM3B,KAAK,CAAC0B,GAAG,EAAE;QAChCzB,MAAM,EAAEA,MAAM;QACdF,OAAO,EAAEA,OAAO;QAChB6B,IAAI,EAAE/C,IAAI,CAACC,SAAS,CAAC2C,UAAU;MACjC,CAAC,CAAC;MAEF,IAAI,CAACE,QAAQ,CAACtB,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,gBAAgBqB,QAAQ,CAACvB,MAAM,EAAE,CAAC;MACpD;MAEA,MAAMyB,MAAM,GAAG,MAAMF,QAAQ,CAACpB,IAAI,CAAC,CAAC;MAEpC,IAAIxD,SAAS,EAAE;QACbjB,cAAc,CAACwF,IAAI,IAAIA,IAAI,CAACQ,GAAG,CAAC3C,EAAE,IAAI4C,MAAM,CAAC5C,EAAE,CAACE,EAAE,CAAC,CAAC2C,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAKD,MAAM,CAAChF,SAAS,CAAC,CAACiF,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGH,MAAM,GAAG1C,EAAE,CAAC,CAAC;QACrHnD,sBAAsB,CAACsF,IAAI,IAAIA,IAAI,CAACQ,GAAG,CAAC3C,EAAE,IAAI4C,MAAM,CAAC5C,EAAE,CAACE,EAAE,CAAC,CAAC2C,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAKD,MAAM,CAAChF,SAAS,CAAC,CAACiF,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGH,MAAM,GAAG1C,EAAE,CAAC,CAAC;MAC/H,CAAC,MAAM;QACLrD,cAAc,CAACwF,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEO,MAAM,CAAC,CAAC;QACzC7F,sBAAsB,CAACsF,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEO,MAAM,CAAC,CAAC;MACnD;IAEF,CAAC,CAAC,OAAOI,YAAY,EAAE;MACrB7D,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;;MAEpE;MACA,MAAM6D,kBAAkB,GAAG,CAAC,GAAGrG,WAAW,CAAC;MAE3C,IAAIkB,SAAS,EAAE;QACb;QACA,MAAMoF,kBAAkB,GAAGD,kBAAkB,CAACJ,GAAG,CAAC3C,EAAE,IAAI;UACtD,IAAI4C,MAAM,CAAC5C,EAAE,CAACE,EAAE,CAAC,CAAC2C,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAKD,MAAM,CAAChF,SAAS,CAAC,CAACiF,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;YACnE,MAAMpC,WAAW,GAAGrD,YAAY,CAAC6F,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAChD,EAAE,IAAIoC,UAAU,CAAC/D,cAAc,CAAC;YAC7E,OAAO;cACL,GAAGyB,EAAE;cACL,GAAGsC,UAAU;cACb7B,WAAW,EAAEA,WAAW,IAAI;YAC9B,CAAC;UACH;UACA,OAAOT,EAAE;QACX,CAAC,CAAC;QAEFrD,cAAc,CAACqG,kBAAkB,CAAC;QAClCnG,sBAAsB,CAACmG,kBAAkB,CAAC;QAC1CxD,kBAAkB,CAACwD,kBAAkB,CAAC;MAExC,CAAC,MAAM;QACL;QACA,MAAMG,KAAK,GAAGvD,aAAa,CAACmD,kBAAkB,CAAC;QAC/C,MAAMtC,WAAW,GAAGrD,YAAY,CAAC6F,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAChD,EAAE,IAAIoC,UAAU,CAAC/D,cAAc,CAAC;QAE7E,MAAM6E,aAAa,GAAG;UACpB,GAAGd,UAAU;UACbpC,EAAE,EAAEiD,KAAK;UACT1C,WAAW,EAAEA,WAAW,IAAI;QAC9B,CAAC;QAED,MAAMuC,kBAAkB,GAAG,CAAC,GAAGD,kBAAkB,EAAEK,aAAa,CAAC;QACjEzG,cAAc,CAACqG,kBAAkB,CAAC;QAClCnG,sBAAsB,CAACmG,kBAAkB,CAAC;QAC1CxD,kBAAkB,CAACwD,kBAAkB,CAAC;MACxC;IACF;;IAEA;IACA/E,WAAW,CAAC;MACVC,GAAG,EAAE,EAAE;MACPC,IAAI,EAAE,EAAE;MACRC,YAAY,EAAE,EAAE;MAChBC,MAAM,EAAE,YAAY;MACpBC,UAAU,EAAE,EAAE;MACdC,cAAc,EAAE,EAAE;MAClBC,mBAAmB,EAAE,EAAE;MACvBC,iBAAiB,EAAE,EAAE;MACrBC,QAAQ,EAAE,IAAI;MACdC,YAAY,EAAE,CAAC;MACfC,SAAS,EAAE,CAAC;MACZC,SAAS,EAAE,CAAC;MACZC,YAAY,EAAE;IAChB,CAAC,CAAC;IACF3B,YAAY,CAAC,KAAK,CAAC;IACnBU,YAAY,CAAC,IAAI,CAAC;IAElBwF,KAAK,CAACzF,SAAS,GAAG,kCAAkC,GAAG,iCAAiC,CAAC;IACzFH,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,MAAM6F,UAAU,GAAIjD,UAAU,IAAK;IAAA,IAAAkD,sBAAA;IACjCtE,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEmB,UAAU,CAAC;IAC3DpB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEmB,UAAU,CAACH,EAAE,CAAC;;IAEzD;IACA,MAAMsD,OAAO,GAAGZ,MAAM,CAACvC,UAAU,CAACH,EAAE,CAAC,CAAC2C,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACnD5D,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEsE,OAAO,CAAC;IAE7C3F,YAAY,CAAC2F,OAAO,CAAC;IACrBvF,WAAW,CAAC;MACVC,GAAG,EAAEmC,UAAU,CAACnC,GAAG;MACnBC,IAAI,EAAEkC,UAAU,CAAClC,IAAI;MACrBC,YAAY,EAAEiC,UAAU,CAACjC,YAAY,IAAIiC,UAAU,CAACoD,WAAW,IAAI,EAAE;MACrEpF,MAAM,EAAEgC,UAAU,CAAChC,MAAM;MACzBC,UAAU,EAAE+B,UAAU,CAAC/B,UAAU,IAAI,EAAE;MACvCC,cAAc,EAAE,EAAAgF,sBAAA,GAAAlD,UAAU,CAACI,WAAW,cAAA8C,sBAAA,uBAAtBA,sBAAA,CAAwBrD,EAAE,KAAIG,UAAU,CAAC9B,cAAc,IAAI,EAAE;MAC7EC,mBAAmB,EAAE6B,UAAU,CAAC7B,mBAAmB,IAAI,EAAE;MACzDC,iBAAiB,EAAE4B,UAAU,CAAC5B,iBAAiB,IAAI,EAAE;MACrDC,QAAQ,EAAE2B,UAAU,CAAC3B,QAAQ,KAAKgF,SAAS,GAAGrD,UAAU,CAAC3B,QAAQ,GAAG,IAAI;MACxEC,YAAY,EAAE0B,UAAU,CAAC1B,YAAY,IAAI,CAAC;MAC1CC,SAAS,EAAEyB,UAAU,CAACzB,SAAS,IAAI,CAAC;MACpCC,SAAS,EAAEwB,UAAU,CAACxB,SAAS,IAAI,CAAC;MACpCC,YAAY,EAAEuB,UAAU,CAACvB,YAAY,IAAI;IAC3C,CAAC,CAAC;IACF3B,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMwG,YAAY,GAAG,MAAOzD,EAAE,IAAK;IACjC,IAAI,CAAC0D,MAAM,CAACC,OAAO,CAAC,qDAAqD,CAAC,EAAE;MAC1E;IACF;IAEA5E,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEgB,EAAE,CAAC;IAC9C;IACA,MAAMsD,OAAO,GAAGZ,MAAM,CAAC1C,EAAE,CAAC,CAAC2C,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACxC5D,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEsE,OAAO,CAAC;IAE/C,IAAI;MACF,MAAM9C,KAAK,GAAGvB,YAAY,CAACwB,OAAO,CAAC,WAAW,CAAC;MAC/C,MAAMC,OAAO,GAAG;QACd,cAAc,EAAE;MAClB,CAAC;MACD,IAAIF,KAAK,EAAE;QACTE,OAAO,CAAC,eAAe,CAAC,GAAG,UAAUF,KAAK,EAAE;MAC9C;MAEA,MAAM8B,QAAQ,GAAG,MAAM3B,KAAK,CAAC,yCAAyC2C,OAAO,EAAE,EAAE;QAC/E1C,MAAM,EAAE,QAAQ;QAChBF,OAAO,EAAEA;MACX,CAAC,CAAC;MAEF,IAAI,CAAC4B,QAAQ,CAACtB,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,gBAAgBqB,QAAQ,CAACvB,MAAM,EAAE,CAAC;MACpD;MAEAtE,cAAc,CAACwF,IAAI,IAAIA,IAAI,CAAC/B,MAAM,CAACJ,EAAE,IAAI4C,MAAM,CAAC5C,EAAE,CAACE,EAAE,CAAC,CAAC2C,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAKW,OAAO,CAAC,CAAC;MAClF3G,sBAAsB,CAACsF,IAAI,IAAIA,IAAI,CAAC/B,MAAM,CAACJ,EAAE,IAAI4C,MAAM,CAAC5C,EAAE,CAACE,EAAE,CAAC,CAAC2C,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAKW,OAAO,CAAC,CAAC;MAE1FH,KAAK,CAAC,mCAAmC,CAAC;IAE5C,CAAC,CAAC,OAAOP,YAAY,EAAE;MACrB7D,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;;MAEzD;MACA,MAAM8D,kBAAkB,GAAGtG,WAAW,CAAC0D,MAAM,CAACJ,EAAE,IAAI4C,MAAM,CAAC5C,EAAE,CAACE,EAAE,CAAC,CAAC2C,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAKW,OAAO,CAAC;MAC5F7G,cAAc,CAACqG,kBAAkB,CAAC;MAClCnG,sBAAsB,CAACmG,kBAAkB,CAAC;MAC1CxD,kBAAkB,CAACwD,kBAAkB,CAAC;MAEtCK,KAAK,CAAC,qDAAqD,CAAC;IAC9D;EACF,CAAC;EAED,MAAMS,kBAAkB,GAAIhC,CAAC,IAAK;IAChCnE,aAAa,CAACmE,CAAC,CAACI,MAAM,CAACF,KAAK,CAAC;EAC/B,CAAC;EAED,MAAM+B,UAAU,GAAIC,SAAS,IAAK;IAChC,IAAI,CAACA,SAAS,IAAIA,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAKN,SAAS,IAAIM,SAAS,KAAK,EAAE,EAAE;MACnF,OAAO,KAAK;IACd;IAEA,IAAI;MACF,MAAMC,OAAO,GAAG,IAAIC,IAAI,CAACF,SAAS,CAAC;MACnC,IAAI,CAACG,KAAK,CAACF,OAAO,CAACG,OAAO,CAAC,CAAC,CAAC,EAAE;QAC7B,OAAOH,OAAO,CAACI,kBAAkB,CAAC,OAAO,CAAC;MAC5C;MACA,OAAO,KAAK;IACd,CAAC,CAAC,OAAOvC,CAAC,EAAE;MACV7C,OAAO,CAACjC,KAAK,CAAC,6BAA6B,EAAE8E,CAAC,EAAE,iBAAiB,EAAEkC,SAAS,CAAC;MAC7E,OAAO,KAAK;IACd;EACF,CAAC;EAED,MAAMM,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI,CAAC/C,KAAK,CAACC,OAAO,CAAC5E,mBAAmB,CAAC,IAAIA,mBAAmB,CAAC0E,MAAM,KAAK,CAAC,EAAE;MAC3E+B,KAAK,CAAC,0BAA0B,CAAC;MACjC;IACF;IAEA,MAAMkB,OAAO,GAAG,CACd,KAAK,EACL,MAAM,EACN,iBAAiB,EACjB,QAAQ,EACR,cAAc,EACd,mBAAmB,EACnB,aAAa,EACb,SAAS,CACV;IACD,MAAMC,IAAI,GAAG5H,mBAAmB,CAAC+F,GAAG,CAAC3C,EAAE;MAAA,IAAAyE,eAAA;MAAA,OAAI,CACzCzE,EAAE,CAAC9B,GAAG,IAAI,EAAE,EACZ8B,EAAE,CAAC7B,IAAI,IAAI,EAAE,EACb6B,EAAE,CAAC5B,YAAY,IAAI4B,EAAE,CAACyD,WAAW,IAAI,EAAE,EACvCzD,EAAE,CAAC3B,MAAM,IAAI,EAAE,EACf0F,UAAU,CAAC/D,EAAE,CAAC0E,SAAS,IAAI1E,EAAE,CAAC1B,UAAU,CAAC,IAAI,EAAE,EAC/CyF,UAAU,CAAC/D,EAAE,CAAC2E,eAAe,IAAI3E,EAAE,CAACvB,iBAAiB,CAAC,IAAI,EAAE,EAC5D,EAAAgG,eAAA,GAAAzE,EAAE,CAACS,WAAW,cAAAgE,eAAA,uBAAdA,eAAA,CAAgBvG,GAAG,KAAI,EAAE,EACzB,EAAE,CACH;IAAA,EAAC;IACF,MAAM0G,SAAS,GAAGpJ,IAAI,CAACqJ,KAAK,CAACC,YAAY,CAAC,CAACP,OAAO,EAAE,GAAGC,IAAI,CAAC,CAAC;IAC7D,MAAMO,QAAQ,GAAGvJ,IAAI,CAACqJ,KAAK,CAACG,QAAQ,CAAC,CAAC;IACtCxJ,IAAI,CAACqJ,KAAK,CAACI,iBAAiB,CAACF,QAAQ,EAAEH,SAAS,EAAE,aAAa,CAAC;IAChEpJ,IAAI,CAAC0J,SAAS,CAACH,QAAQ,EAAE,kBAAkB,CAAC;EAC9C,CAAC;EAED,MAAMI,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAAC5D,KAAK,CAACC,OAAO,CAAC5E,mBAAmB,CAAC,IAAIA,mBAAmB,CAAC0E,MAAM,KAAK,CAAC,EAAE;MAC3E+B,KAAK,CAAC,0BAA0B,CAAC;MACjC;IACF;IAEA,MAAM+B,GAAG,GAAG,IAAI9J,KAAK,CAAC,CAAC;IACvB8J,GAAG,CAACC,IAAI,CAAC,uBAAuB,EAAE,EAAE,EAAE,EAAE,CAAC;IACzC,MAAMd,OAAO,GAAG,CACd,KAAK,EACL,MAAM,EACN,iBAAiB,EACjB,QAAQ,EACR,cAAc,EACd,mBAAmB,EACnB,aAAa,CACd;IACD,MAAMC,IAAI,GAAG5H,mBAAmB,CAAC+F,GAAG,CAAC3C,EAAE;MAAA,IAAAsF,gBAAA;MAAA,OAAI,CACzCtF,EAAE,CAAC9B,GAAG,IAAI,EAAE,EACZ8B,EAAE,CAAC7B,IAAI,IAAI,EAAE,EACb6B,EAAE,CAAC5B,YAAY,IAAI4B,EAAE,CAACyD,WAAW,IAAI,EAAE,EACvCzD,EAAE,CAAC3B,MAAM,IAAI,EAAE,EACf0F,UAAU,CAAC/D,EAAE,CAAC0E,SAAS,IAAI1E,EAAE,CAAC1B,UAAU,CAAC,IAAI,EAAE,EAC/CyF,UAAU,CAAC/D,EAAE,CAAC2E,eAAe,IAAI3E,EAAE,CAACvB,iBAAiB,CAAC,IAAI,EAAE,EAC5D,EAAA6G,gBAAA,GAAAtF,EAAE,CAACS,WAAW,cAAA6E,gBAAA,uBAAdA,gBAAA,CAAgBpH,GAAG,KAAI,EAAE,CAC1B;IAAA,EAAC;IACF3C,SAAS,CAAC6J,GAAG,EAAE;MACbG,IAAI,EAAE,CAAChB,OAAO,CAAC;MACf9B,IAAI,EAAE+B,IAAI;MACVgB,MAAM,EAAE,EAAE;MACVC,MAAM,EAAE;QAAEC,QAAQ,EAAE;MAAG;IACzB,CAAC,CAAC;IACFN,GAAG,CAACO,IAAI,CAAC,iBAAiB,CAAC;EAC7B,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxBhC,MAAM,CAACiC,KAAK,CAAC,CAAC;EAChB,CAAC;;EAED;EACA,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,YAAY,GAAG,CAAC,CAAC;IACvBnJ,mBAAmB,CAACoJ,OAAO,CAAChG,EAAE,IAAI;MAChC,MAAM3B,MAAM,GAAG2B,EAAE,CAAC3B,MAAM,IAAI,YAAY;MACxC0H,YAAY,CAAC1H,MAAM,CAAC,GAAG,CAAC0H,YAAY,CAAC1H,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;IACxD,CAAC,CAAC;IACF,OAAO0H,YAAY;EACrB,CAAC;EAED,MAAME,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,UAAU,GAAG,CAAC,CAAC;IACrBtJ,mBAAmB,CAACoJ,OAAO,CAAChG,EAAE,IAAI;MAChC,MAAM7B,IAAI,GAAG6B,EAAE,CAAC7B,IAAI,IAAI,YAAY;MACpC+H,UAAU,CAAC/H,IAAI,CAAC,GAAG,CAAC+H,UAAU,CAAC/H,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;IAChD,CAAC,CAAC;IACF,OAAO+H,UAAU;EACnB,CAAC;;EAED;EACA,MAAMC,WAAW,GAAGL,cAAc,CAAC,CAAC;EACpC,MAAMM,SAAS,GAAGH,YAAY,CAAC,CAAC;EAEhC,MAAMI,eAAe,GAAG;IACtBC,MAAM,EAAEC,MAAM,CAACC,IAAI,CAACL,WAAW,CAAC;IAChCM,QAAQ,EAAE,CAAC;MACTC,KAAK,EAAE,uBAAuB;MAC9BrF,IAAI,EAAEkF,MAAM,CAACI,MAAM,CAACR,WAAW,CAAC;MAChCS,eAAe,EAAE,CACf,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACV;MACDC,WAAW,EAAE,CACX,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACV;MACDC,WAAW,EAAE;IACf,CAAC;EACH,CAAC;EAED,MAAMC,aAAa,GAAG;IACpBT,MAAM,EAAEC,MAAM,CAACC,IAAI,CAACJ,SAAS,CAAC;IAC9BK,QAAQ,EAAE,CAAC;MACTpF,IAAI,EAAEkF,MAAM,CAACI,MAAM,CAACP,SAAS,CAAC;MAC9BQ,eAAe,EAAE,CACf,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACV;MACDE,WAAW,EAAE,CAAC;MACdD,WAAW,EAAE;IACf,CAAC;EACH,CAAC;EAED,MAAMG,YAAY,GAAG;IACnBC,UAAU,EAAE,IAAI;IAChBC,OAAO,EAAE;MACPC,MAAM,EAAE;QACNC,QAAQ,EAAE;MACZ,CAAC;MACDC,KAAK,EAAE;QACLC,OAAO,EAAE,IAAI;QACbjC,IAAI,EAAE;MACR;IACF,CAAC;IACDkC,MAAM,EAAE;MACNC,CAAC,EAAE;QACDC,WAAW,EAAE,IAAI;QACjBC,KAAK,EAAE;UACLC,QAAQ,EAAE;QACZ;MACF;IACF;EACF,CAAC;EAED,MAAMC,eAAe,GAAG;IACtBX,UAAU,EAAE,IAAI;IAChBC,OAAO,EAAE;MACPC,MAAM,EAAE;QACNC,QAAQ,EAAE;MACZ,CAAC;MACDC,KAAK,EAAE;QACLC,OAAO,EAAE,IAAI;QACbjC,IAAI,EAAE;MACR;IACF;EACF,CAAC;EAED,oBACEhJ,OAAA;IAAKwL,SAAS,EAAC,uBAAuB;IAAAC,QAAA,gBAEpCzL,OAAA;MAAKwL,SAAS,EAAC,SAAS;MAAAC,QAAA,gBACtBzL,OAAA;QAAKwL,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3BzL,OAAA;UAAK0L,GAAG,EAAEzN,IAAK;UAAC0N,GAAG,EAAC,iBAAiB;UAACH,SAAS,EAAC;QAAc;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC,eACN/L,OAAA;QAAKwL,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBzL,OAAA;UAAAyL,QAAA,gBACEzL,OAAA;YAAAyL,QAAA,eACEzL,OAAA,CAACjC,IAAI;cAACiO,EAAE,EAAC,GAAG;cAACR,SAAS,EAAEpL,QAAQ,CAAC6L,QAAQ,KAAK,GAAG,GAAG,oBAAoB,GAAG,EAAG;cAAAR,QAAA,gBAC5EzL,OAAA,CAAC9B,eAAe;gBAACgO,IAAI,EAAE/N,QAAS;gBAACgO,KAAK,EAAE;kBAACC,WAAW,EAAC;gBAAC;cAAE;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,YAC7D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACL/L,OAAA;YAAAyL,QAAA,eACEzL,OAAA,CAACjC,IAAI;cAACiO,EAAE,EAAC,cAAc;cAACR,SAAS,EAAEpL,QAAQ,CAAC6L,QAAQ,KAAK,cAAc,GAAG,QAAQ,GAAG,EAAG;cAAAR,QAAA,gBACtFzL,OAAA,CAAC9B,eAAe;gBAACgO,IAAI,EAAE/N,QAAS;gBAACgO,KAAK,EAAE;kBAACC,WAAW,EAAC;gBAAC;cAAE;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gCAC7D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACL/L,OAAA;YAAAyL,QAAA,eACEzL,OAAA,CAACjC,IAAI;cAACiO,EAAE,EAAC,WAAW;cAACR,SAAS,EAAEpL,QAAQ,CAAC6L,QAAQ,KAAK,WAAW,GAAG,QAAQ,GAAG,EAAG;cAAAR,QAAA,gBAChFzL,OAAA,CAAC9B,eAAe;gBAACgO,IAAI,EAAE9N,OAAQ;gBAAC+N,KAAK,EAAE;kBAACC,WAAW,EAAC;gBAAC;cAAE;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,6BAC5D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACL/L,OAAA;YAAAyL,QAAA,eACEzL,OAAA,CAACjC,IAAI;cAACiO,EAAE,EAAC,eAAe;cAACR,SAAS,EAAEpL,QAAQ,CAAC6L,QAAQ,KAAK,eAAe,GAAG,QAAQ,GAAG,EAAG;cAAAR,QAAA,gBACxFzL,OAAA,CAAC9B,eAAe;gBAACgO,IAAI,EAAE7N,aAAc;gBAAC8N,KAAK,EAAE;kBAACC,WAAW,EAAC;gBAAC;cAAE;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,2BAClE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACL/L,OAAA;YAAAyL,QAAA,eACEzL,OAAA,CAACjC,IAAI;cAACiO,EAAE,EAAC,eAAe;cAACR,SAAS,EAAEpL,QAAQ,CAAC6L,QAAQ,KAAK,eAAe,GAAG,QAAQ,GAAG,EAAG;cAAAR,QAAA,gBACxFzL,OAAA,CAAC9B,eAAe;gBAACgO,IAAI,EAAE5N,OAAQ;gBAAC6N,KAAK,EAAE;kBAACC,WAAW,EAAC;gBAAC;cAAE;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,8BAC5D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACN/L,OAAA;QAAKwL,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BzL,OAAA,CAACjC,IAAI;UAACiO,EAAE,EAAC,SAAS;UAAAP,QAAA,gBAChBzL,OAAA,CAAC9B,eAAe;YAACgO,IAAI,EAAE3N,SAAU;YAAC4N,KAAK,EAAE;cAACC,WAAW,EAAC;YAAC;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,+BAC9D;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN/L,OAAA;QAAKmM,KAAK,EAAE;UAAEE,SAAS,EAAE,MAAM;UAAEC,OAAO,EAAE,YAAY;UAAEC,SAAS,EAAE;QAAS,CAAE;QAAAd,QAAA,eAC5EzL,OAAA;UACEwL,SAAS,EAAC,YAAY;UACtBgB,OAAO,EAAEA,CAAA,KAAM;YACb1J,YAAY,CAACC,UAAU,CAAC,WAAW,CAAC;YACpCwE,MAAM,CAACnH,QAAQ,CAACqM,IAAI,GAAG,GAAG;UAC5B,CAAE;UAAAhB,QAAA,gBAEFzL,OAAA,CAAC9B,eAAe;YAACgO,IAAI,EAAE1N,YAAa;YAAC2N,KAAK,EAAE;cAACC,WAAW,EAAC;YAAC;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,mBACjE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN/L,OAAA;MAAKwL,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAElCzL,OAAA;QAAKwL,SAAS,EAAC,sBAAsB;QAACW,KAAK,EAAE;UAAElB,OAAO,EAAE,MAAM;UAAEyB,UAAU,EAAE,QAAQ;UAAEC,cAAc,EAAE,eAAe;UAAEC,YAAY,EAAE;QAAG,CAAE;QAAAnB,QAAA,gBACxIzL,OAAA;UAAKwL,SAAS,EAAC,YAAY;UAACW,KAAK,EAAE;YAAEU,IAAI,EAAE,CAAC;YAAEC,QAAQ,EAAE,GAAG;YAAEV,WAAW,EAAE,EAAE;YAAErB,QAAQ,EAAE;UAAW,CAAE;UAAAU,QAAA,gBACnGzL,OAAA,CAAC9B,eAAe;YAACgO,IAAI,EAAEzN,QAAS;YAAC0N,KAAK,EAAE;cAAEpB,QAAQ,EAAE,UAAU;cAAEgC,IAAI,EAAE,EAAE;cAAEC,GAAG,EAAE,KAAK;cAAEC,SAAS,EAAE,kBAAkB;cAAEC,KAAK,EAAE,SAAS;cAAE7D,QAAQ,EAAE,QAAQ;cAAE8D,OAAO,EAAE;YAAI;UAAE;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7K/L,OAAA;YACE8B,IAAI,EAAC,MAAM;YACXsL,WAAW,EAAC,0BAAuB;YACnCzH,KAAK,EAAEtE,UAAW;YAClBgM,QAAQ,EAAE5F,kBAAmB;YAC7B0E,KAAK,EAAE;cAAEmB,KAAK,EAAE,MAAM;cAAEhB,OAAO,EAAE,qBAAqB;cAAEiB,YAAY,EAAE,CAAC;cAAEC,MAAM,EAAE,mBAAmB;cAAEnE,QAAQ,EAAE,MAAM;cAAEoE,SAAS,EAAE;YAAkC;UAAE;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN/L,OAAA;UAAKwL,SAAS,EAAC,eAAe;UAACW,KAAK,EAAE;YAAElB,OAAO,EAAE,MAAM;YAAEyB,UAAU,EAAE,QAAQ;YAAEgB,GAAG,EAAE;UAAG,CAAE;UAAAjC,QAAA,gBACvFzL,OAAA;YAAKwL,SAAS,EAAC,mBAAmB;YAACW,KAAK,EAAE;cAAEpB,QAAQ,EAAE,UAAU;cAAEqB,WAAW,EAAE,CAAC;cAAEuB,MAAM,EAAE;YAAU,CAAE;YAAAlC,QAAA,gBACpGzL,OAAA,CAAC9B,eAAe;cAACgO,IAAI,EAAExN,MAAO;cAACyN,KAAK,EAAE;gBAAE9C,QAAQ,EAAE,QAAQ;gBAAE6D,KAAK,EAAE;cAAU;YAAE;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClF/L,OAAA;cAAMmM,KAAK,EAAE;gBAAEpB,QAAQ,EAAE,UAAU;gBAAEiC,GAAG,EAAE,CAAC,CAAC;gBAAEY,KAAK,EAAE,CAAC,CAAC;gBAAEC,UAAU,EAAE,SAAS;gBAAEX,KAAK,EAAE,MAAM;gBAAEK,YAAY,EAAE,KAAK;gBAAElE,QAAQ,EAAE,QAAQ;gBAAEiD,OAAO,EAAE,SAAS;gBAAEwB,UAAU,EAAE;cAAI,CAAE;cAAArC,QAAA,EAAC;YAAC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpL,CAAC,eACN/L,OAAA;YAAMmM,KAAK,EAAE;cAAE2B,UAAU,EAAE,GAAG;cAAEZ,KAAK,EAAE,SAAS;cAAE7D,QAAQ,EAAE,MAAM;cAAE+C,WAAW,EAAE;YAAE,CAAE;YAAAX,QAAA,EAAC;UAAc;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3G/L,OAAA,CAAC9B,eAAe;YAACgO,IAAI,EAAE3N,SAAU;YAAC4N,KAAK,EAAE;cAAE9C,QAAQ,EAAE,QAAQ;cAAE6D,KAAK,EAAE,SAAS;cAAEW,UAAU,EAAE,MAAM;cAAEN,YAAY,EAAE,KAAK;cAAEjB,OAAO,EAAE,CAAC;cAAEmB,SAAS,EAAE;YAAkC;UAAE;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN/L,OAAA;QAAIwL,SAAS,EAAC,mBAAmB;QAAAC,QAAA,GAAC,4BACT,EAAClL,mBAAmB,CAAC0E,MAAM,EAAC,GACrD;MAAA;QAAA2G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAGJpL,KAAK,iBACJX,OAAA;QAAKmM,KAAK,EAAE;UACV0B,UAAU,EAAE,SAAS;UACrBL,MAAM,EAAE,mBAAmB;UAC3BD,YAAY,EAAE,CAAC;UACfjB,OAAO,EAAE,EAAE;UACXM,YAAY,EAAE,EAAE;UAChBM,KAAK,EAAE,SAAS;UAChBjC,OAAO,EAAE,MAAM;UACf0B,cAAc,EAAE,eAAe;UAC/BD,UAAU,EAAE;QACd,CAAE;QAAAjB,QAAA,eACAzL,OAAA;UAAAyL,QAAA,gBACEzL,OAAA;YAAAyL,QAAA,EAAQ;UAAc;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACpL,KAAK;QAAA;UAAAiL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEH,CACN,eAED/L,OAAA;QAAKmM,KAAK,EAAE;UAAElB,OAAO,EAAE,MAAM;UAAEyC,GAAG,EAAE,EAAE;UAAEd,YAAY,EAAE;QAAG,CAAE;QAAAnB,QAAA,gBACzDzL,OAAA;UACEwL,SAAS,EAAC,iBAAiB;UAC3BW,KAAK,EAAE;YAAE2B,UAAU,EAAE,GAAG;YAAEzE,QAAQ,EAAE,MAAM;YAAEkE,YAAY,EAAE,EAAE;YAAEM,UAAU,EAAE,kDAAkD;YAAEX,KAAK,EAAE,MAAM;YAAEO,SAAS,EAAE;UAAmC,CAAE;UAC7LjB,OAAO,EAAEA,CAAA,KAAM;YACbhL,YAAY,CAAC,IAAI,CAAC;YAClBV,YAAY,CAAC,IAAI,CAAC;UACpB,CAAE;UAAA2K,QAAA,gBAEFzL,OAAA,CAAC9B,eAAe;YAACgO,IAAI,EAAEtN,MAAO;YAACuN,KAAK,EAAE;cAACC,WAAW,EAAE;YAAC;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,4BAE5D;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT/L,OAAA;UAAQwL,SAAS,EAAC,iBAAiB;UAACW,KAAK,EAAE;YAAE2B,UAAU,EAAE,GAAG;YAAEzE,QAAQ,EAAE,MAAM;YAAEkE,YAAY,EAAE,EAAE;YAAEM,UAAU,EAAE,kDAAkD;YAAEX,KAAK,EAAE,MAAM;YAAEO,SAAS,EAAE;UAAkC,CAAE;UAACjB,OAAO,EAAEvE,iBAAkB;UAAAwD,QAAA,EAAC;QAE7P;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT/L,OAAA;UAAQwL,SAAS,EAAC,iBAAiB;UAACW,KAAK,EAAE;YAAE2B,UAAU,EAAE,GAAG;YAAEzE,QAAQ,EAAE,MAAM;YAAEkE,YAAY,EAAE,EAAE;YAAEM,UAAU,EAAE,kDAAkD;YAAEX,KAAK,EAAE,MAAM;YAAEO,SAAS,EAAE;UAAkC,CAAE;UAACjB,OAAO,EAAE1D,eAAgB;UAAA2C,QAAA,EAAC;QAE3P;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT/L,OAAA;UAAQwL,SAAS,EAAC,iBAAiB;UAACW,KAAK,EAAE;YAAE2B,UAAU,EAAE,GAAG;YAAEzE,QAAQ,EAAE,MAAM;YAAEkE,YAAY,EAAE,EAAE;YAAEM,UAAU,EAAE,kDAAkD;YAAEX,KAAK,EAAE,MAAM;YAAEO,SAAS,EAAE;UAAkC,CAAE;UAACjB,OAAO,EAAEjD,WAAY;UAAAkC,QAAA,EAAC;QAEvP;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT/L,OAAA;UACEwL,SAAS,EAAC,iBAAiB;UAC3BW,KAAK,EAAE;YAAE2B,UAAU,EAAE,GAAG;YAAEzE,QAAQ,EAAE,MAAM;YAAEkE,YAAY,EAAE,EAAE;YAAEM,UAAU,EAAE,kDAAkD;YAAEX,KAAK,EAAE,MAAM;YAAEO,SAAS,EAAE;UAAmC,CAAE;UAC7LjB,OAAO,EAAEA,CAAA,KAAM9K,YAAY,CAAC,CAACD,SAAS,CAAE;UAAAgK,QAAA,GAEvChK,SAAS,GAAG,SAAS,GAAG,UAAU,EAAC,mBACtC;QAAA;UAAAmK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGLtK,SAAS,iBACRzB,OAAA;QAAKmM,KAAK,EAAE;UACV0B,UAAU,EAAE,MAAM;UAClBN,YAAY,EAAE,EAAE;UAChBjB,OAAO,EAAE,EAAE;UACXM,YAAY,EAAE,EAAE;UAChBa,SAAS,EAAE,4BAA4B;UACvCD,MAAM,EAAE;QACV,CAAE;QAAA/B,QAAA,gBACAzL,OAAA;UAAImM,KAAK,EAAE;YACTe,KAAK,EAAE,SAAS;YAChBN,YAAY,EAAE,EAAE;YAChBvD,QAAQ,EAAE,QAAQ;YAClByE,UAAU,EAAE,GAAG;YACfvB,SAAS,EAAE;UACb,CAAE;UAAAd,QAAA,EAAC;QAEH;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEL/L,OAAA;UAAKmM,KAAK,EAAE;YAAElB,OAAO,EAAE,MAAM;YAAE8C,mBAAmB,EAAE,SAAS;YAAEL,GAAG,EAAE,EAAE;YAAEhB,UAAU,EAAE;UAAS,CAAE;UAAAjB,QAAA,gBAE7FzL,OAAA;YAAKmM,KAAK,EAAE;cACV0B,UAAU,EAAE,SAAS;cACrBvB,OAAO,EAAE,EAAE;cACXiB,YAAY,EAAE,CAAC;cACfC,MAAM,EAAE,mBAAmB;cAC3BQ,MAAM,EAAE;YACV,CAAE;YAAAvC,QAAA,eACAzL,OAAA,CAACH,GAAG;cAACmF,IAAI,EAAEgF,eAAgB;cAACiE,OAAO,EAAEtD;YAAa;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eAGN/L,OAAA;YAAKmM,KAAK,EAAE;cACV0B,UAAU,EAAE,SAAS;cACrBvB,OAAO,EAAE,EAAE;cACXiB,YAAY,EAAE,CAAC;cACfC,MAAM,EAAE,mBAAmB;cAC3BQ,MAAM,EAAE;YACV,CAAE;YAAAvC,QAAA,eACAzL,OAAA,CAACF,QAAQ;cAACkF,IAAI,EAAE0F,aAAc;cAACuD,OAAO,EAAE1C;YAAgB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN/L,OAAA;UAAKmM,KAAK,EAAE;YACVE,SAAS,EAAE,EAAE;YACbC,OAAO,EAAE,EAAE;YACXuB,UAAU,EAAE,SAAS;YACrBN,YAAY,EAAE,CAAC;YACfC,MAAM,EAAE;UACV,CAAE;UAAA/B,QAAA,gBACAzL,OAAA;YAAImM,KAAK,EAAE;cAAEe,KAAK,EAAE,SAAS;cAAEN,YAAY,EAAE,EAAE;cAAEvD,QAAQ,EAAE;YAAS,CAAE;YAAAoC,QAAA,EAAC;UAAS;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrF/L,OAAA;YAAKmM,KAAK,EAAE;cAAElB,OAAO,EAAE,MAAM;cAAE8C,mBAAmB,EAAE,sCAAsC;cAAEL,GAAG,EAAE;YAAG,CAAE;YAAAjC,QAAA,gBACpGzL,OAAA;cAAKmM,KAAK,EAAE;gBAAEI,SAAS,EAAE,QAAQ;gBAAED,OAAO,EAAE,EAAE;gBAAEuB,UAAU,EAAE,MAAM;gBAAEN,YAAY,EAAE,CAAC;gBAAEC,MAAM,EAAE;cAAoB,CAAE;cAAA/B,QAAA,gBACjHzL,OAAA;gBAAKmM,KAAK,EAAE;kBAAE9C,QAAQ,EAAE,MAAM;kBAAEyE,UAAU,EAAE,MAAM;kBAAEZ,KAAK,EAAE;gBAAU,CAAE;gBAAAzB,QAAA,EACpElL,mBAAmB,CAAC0E;cAAM;gBAAA2G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC,eACN/L,OAAA;gBAAKmM,KAAK,EAAE;kBAAEe,KAAK,EAAE,SAAS;kBAAE7D,QAAQ,EAAE;gBAAS,CAAE;gBAAAoC,QAAA,EAAC;cAAiB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1E,CAAC,eACN/L,OAAA;cAAKmM,KAAK,EAAE;gBAAEI,SAAS,EAAE,QAAQ;gBAAED,OAAO,EAAE,EAAE;gBAAEuB,UAAU,EAAE,MAAM;gBAAEN,YAAY,EAAE,CAAC;gBAAEC,MAAM,EAAE;cAAoB,CAAE;cAAA/B,QAAA,gBACjHzL,OAAA;gBAAKmM,KAAK,EAAE;kBAAE9C,QAAQ,EAAE,MAAM;kBAAEyE,UAAU,EAAE,MAAM;kBAAEZ,KAAK,EAAE;gBAAU,CAAE;gBAAAzB,QAAA,EACpE3B,WAAW,CAAC,YAAY,CAAC,IAAI;cAAC;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,eACN/L,OAAA;gBAAKmM,KAAK,EAAE;kBAAEe,KAAK,EAAE,SAAS;kBAAE7D,QAAQ,EAAE;gBAAS,CAAE;gBAAAoC,QAAA,EAAC;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpE,CAAC,eACN/L,OAAA;cAAKmM,KAAK,EAAE;gBAAEI,SAAS,EAAE,QAAQ;gBAAED,OAAO,EAAE,EAAE;gBAAEuB,UAAU,EAAE,MAAM;gBAAEN,YAAY,EAAE,CAAC;gBAAEC,MAAM,EAAE;cAAoB,CAAE;cAAA/B,QAAA,gBACjHzL,OAAA;gBAAKmM,KAAK,EAAE;kBAAE9C,QAAQ,EAAE,MAAM;kBAAEyE,UAAU,EAAE,MAAM;kBAAEZ,KAAK,EAAE;gBAAU,CAAE;gBAAAzB,QAAA,EACpE3B,WAAW,CAAC,gBAAgB,CAAC,IAAI;cAAC;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,eACN/L,OAAA;gBAAKmM,KAAK,EAAE;kBAAEe,KAAK,EAAE,SAAS;kBAAE7D,QAAQ,EAAE;gBAAS,CAAE;gBAAAoC,QAAA,EAAC;cAAc;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvE,CAAC,eACN/L,OAAA;cAAKmM,KAAK,EAAE;gBAAEI,SAAS,EAAE,QAAQ;gBAAED,OAAO,EAAE,EAAE;gBAAEuB,UAAU,EAAE,MAAM;gBAAEN,YAAY,EAAE,CAAC;gBAAEC,MAAM,EAAE;cAAoB,CAAE;cAAA/B,QAAA,gBACjHzL,OAAA;gBAAKmM,KAAK,EAAE;kBAAE9C,QAAQ,EAAE,MAAM;kBAAEyE,UAAU,EAAE,MAAM;kBAAEZ,KAAK,EAAE;gBAAU,CAAE;gBAAAzB,QAAA,EACpE3B,WAAW,CAAC,cAAc,CAAC,IAAI;cAAC;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC,eACN/L,OAAA;gBAAKmM,KAAK,EAAE;kBAAEe,KAAK,EAAE,SAAS;kBAAE7D,QAAQ,EAAE;gBAAS,CAAE;gBAAAoC,QAAA,EAAC;cAAY;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAlL,SAAS,iBACRb,OAAA;QAAKmM,KAAK,EAAE;UACVpB,QAAQ,EAAE,OAAO;UACjBiC,GAAG,EAAE,CAAC;UACND,IAAI,EAAE,CAAC;UACPa,KAAK,EAAE,CAAC;UACRM,MAAM,EAAE,CAAC;UACT3D,eAAe,EAAE,oBAAoB;UACrCU,OAAO,EAAE,MAAM;UACfyB,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBwB,MAAM,EAAE;QACV,CAAE;QAAA1C,QAAA,eACAzL,OAAA;UAAKmM,KAAK,EAAE;YACV5B,eAAe,EAAE,OAAO;YACxBgD,YAAY,EAAE,EAAE;YAChBjB,OAAO,EAAE,EAAE;YACXQ,QAAQ,EAAE,GAAG;YACbQ,KAAK,EAAE,KAAK;YACZc,SAAS,EAAE,MAAM;YACjBC,SAAS,EAAE,MAAM;YACjBZ,SAAS,EAAE;UACb,CAAE;UAAAhC,QAAA,gBACAzL,OAAA;YAAKmM,KAAK,EAAE;cAAElB,OAAO,EAAE,MAAM;cAAE0B,cAAc,EAAE,eAAe;cAAED,UAAU,EAAE,QAAQ;cAAEE,YAAY,EAAE;YAAG,CAAE;YAAAnB,QAAA,gBACvGzL,OAAA;cAAImM,KAAK,EAAE;gBAAEmC,MAAM,EAAE,CAAC;gBAAEpB,KAAK,EAAE,SAAS;gBAAE7D,QAAQ,EAAE,QAAQ;gBAAEyE,UAAU,EAAE;cAAI,CAAE;cAAArC,QAAA,EAC7ElK,SAAS,GAAG,wBAAwB,GAAG;YAA8B;cAAAqK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpE,CAAC,eACL/L,OAAA;cACEwM,OAAO,EAAEA,CAAA,KAAM;gBACb1L,YAAY,CAAC,KAAK,CAAC;gBACnBU,YAAY,CAAC,IAAI,CAAC;cACpB,CAAE;cACF2K,KAAK,EAAE;gBACL0B,UAAU,EAAE,MAAM;gBAClBL,MAAM,EAAE,MAAM;gBACdnE,QAAQ,EAAE,QAAQ;gBAClB6D,KAAK,EAAE,MAAM;gBACbS,MAAM,EAAE,SAAS;gBACjBrB,OAAO,EAAE,CAAC;gBACViB,YAAY,EAAE;cAChB,CAAE;cAAA9B,QAAA,eAEFzL,OAAA,CAAC9B,eAAe;gBAACgO,IAAI,EAAEvN;cAAQ;gBAAAiN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN/L,OAAA;YAAMuO,QAAQ,EAAExI,YAAa;YAAA0F,QAAA,gBAC3BzL,OAAA;cAAKmM,KAAK,EAAE;gBAAElB,OAAO,EAAE,MAAM;gBAAE8C,mBAAmB,EAAE,SAAS;gBAAEL,GAAG,EAAE,EAAE;gBAAEd,YAAY,EAAE;cAAG,CAAE;cAAAnB,QAAA,gBACzFzL,OAAA;gBAAAyL,QAAA,gBACEzL,OAAA;kBAAOmM,KAAK,EAAE;oBAAElB,OAAO,EAAE,OAAO;oBAAE2B,YAAY,EAAE,CAAC;oBAAEkB,UAAU,EAAE,GAAG;oBAAEZ,KAAK,EAAE;kBAAO,CAAE;kBAAAzB,QAAA,EAAC;gBAErF;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR/L,OAAA;kBACE8B,IAAI,EAAC,MAAM;kBACX4D,IAAI,EAAC,KAAK;kBACVC,KAAK,EAAEhE,QAAQ,CAACE,GAAI;kBACpBwL,QAAQ,EAAE7H,iBAAkB;kBAC5BgJ,QAAQ;kBACRrC,KAAK,EAAE;oBACLmB,KAAK,EAAE,MAAM;oBACbhB,OAAO,EAAE,MAAM;oBACfkB,MAAM,EAAE,mBAAmB;oBAC3BD,YAAY,EAAE,CAAC;oBACflE,QAAQ,EAAE,MAAM;oBAChBoF,UAAU,EAAE;kBACd,CAAE;kBACFC,OAAO,EAAGjJ,CAAC,IAAKA,CAAC,CAACI,MAAM,CAACsG,KAAK,CAAC3B,WAAW,GAAG,SAAU;kBACvDmE,MAAM,EAAGlJ,CAAC,IAAKA,CAAC,CAACI,MAAM,CAACsG,KAAK,CAAC3B,WAAW,GAAG;gBAAU;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN/L,OAAA;gBAAAyL,QAAA,gBACEzL,OAAA;kBAAOmM,KAAK,EAAE;oBAAElB,OAAO,EAAE,OAAO;oBAAE2B,YAAY,EAAE,CAAC;oBAAEkB,UAAU,EAAE,GAAG;oBAAEZ,KAAK,EAAE;kBAAO,CAAE;kBAAAzB,QAAA,EAAC;gBAErF;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR/L,OAAA;kBACE0F,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAEhE,QAAQ,CAACG,IAAK;kBACrBuL,QAAQ,EAAE7H,iBAAkB;kBAC5BgJ,QAAQ;kBACRrC,KAAK,EAAE;oBACLmB,KAAK,EAAE,MAAM;oBACbhB,OAAO,EAAE,MAAM;oBACfkB,MAAM,EAAE,mBAAmB;oBAC3BD,YAAY,EAAE,CAAC;oBACflE,QAAQ,EAAE;kBACZ,CAAE;kBAAAoC,QAAA,gBAEFzL,OAAA;oBAAQ2F,KAAK,EAAC,EAAE;oBAAA8F,QAAA,EAAC;kBAAoB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EAC7CpJ,WAAW,CAAC2D,GAAG,CAACxE,IAAI,iBACnB9B,OAAA;oBAAmB2F,KAAK,EAAE7D,IAAK;oBAAA2J,QAAA,EAAE3J;kBAAI,GAAxBA,IAAI;oBAAA8J,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAA6B,CAC/C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN/L,OAAA;cAAKmM,KAAK,EAAE;gBAAElB,OAAO,EAAE,MAAM;gBAAE8C,mBAAmB,EAAE,SAAS;gBAAEL,GAAG,EAAE,EAAE;gBAAEd,YAAY,EAAE;cAAG,CAAE;cAAAnB,QAAA,gBACzFzL,OAAA;gBAAAyL,QAAA,gBACEzL,OAAA;kBAAOmM,KAAK,EAAE;oBAAElB,OAAO,EAAE,OAAO;oBAAE2B,YAAY,EAAE,CAAC;oBAAEkB,UAAU,EAAE,GAAG;oBAAEZ,KAAK,EAAE;kBAAO,CAAE;kBAAAzB,QAAA,EAAC;gBAErF;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR/L,OAAA;kBACE8B,IAAI,EAAC,MAAM;kBACX4D,IAAI,EAAC,cAAc;kBACnBC,KAAK,EAAEhE,QAAQ,CAACI,YAAa;kBAC7BsL,QAAQ,EAAE7H,iBAAkB;kBAC5B2G,KAAK,EAAE;oBACLmB,KAAK,EAAE,MAAM;oBACbhB,OAAO,EAAE,MAAM;oBACfkB,MAAM,EAAE,mBAAmB;oBAC3BD,YAAY,EAAE,CAAC;oBACflE,QAAQ,EAAE;kBACZ;gBAAE;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN/L,OAAA;gBAAAyL,QAAA,gBACEzL,OAAA;kBAAOmM,KAAK,EAAE;oBAAElB,OAAO,EAAE,OAAO;oBAAE2B,YAAY,EAAE,CAAC;oBAAEkB,UAAU,EAAE,GAAG;oBAAEZ,KAAK,EAAE;kBAAO,CAAE;kBAAAzB,QAAA,EAAC;gBAErF;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR/L,OAAA;kBACE0F,IAAI,EAAC,QAAQ;kBACbC,KAAK,EAAEhE,QAAQ,CAACK,MAAO;kBACvBqL,QAAQ,EAAE7H,iBAAkB;kBAC5BgJ,QAAQ;kBACRrC,KAAK,EAAE;oBACLmB,KAAK,EAAE,MAAM;oBACbhB,OAAO,EAAE,MAAM;oBACfkB,MAAM,EAAE,mBAAmB;oBAC3BD,YAAY,EAAE,CAAC;oBACflE,QAAQ,EAAE;kBACZ,CAAE;kBAAAoC,QAAA,EAED/I,aAAa,CAAC4D,GAAG,CAACtE,MAAM,iBACvBhC,OAAA;oBAAqB2F,KAAK,EAAE3D,MAAO;oBAAAyJ,QAAA,EAAEzJ;kBAAM,GAA9BA,MAAM;oBAAA4J,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAiC,CACrD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN/L,OAAA;cAAKmM,KAAK,EAAE;gBAAElB,OAAO,EAAE,MAAM;gBAAE8C,mBAAmB,EAAE,SAAS;gBAAEL,GAAG,EAAE,EAAE;gBAAEd,YAAY,EAAE;cAAG,CAAE;cAAAnB,QAAA,gBACzFzL,OAAA;gBAAAyL,QAAA,gBACEzL,OAAA;kBAAOmM,KAAK,EAAE;oBAAElB,OAAO,EAAE,OAAO;oBAAE2B,YAAY,EAAE,CAAC;oBAAEkB,UAAU,EAAE,GAAG;oBAAEZ,KAAK,EAAE;kBAAO,CAAE;kBAAAzB,QAAA,EAAC;gBAErF;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR/L,OAAA;kBACE8B,IAAI,EAAC,MAAM;kBACX4D,IAAI,EAAC,YAAY;kBACjBC,KAAK,EAAEhE,QAAQ,CAACM,UAAW;kBAC3BoL,QAAQ,EAAE7H,iBAAkB;kBAC5B2G,KAAK,EAAE;oBACLmB,KAAK,EAAE,MAAM;oBACbhB,OAAO,EAAE,MAAM;oBACfkB,MAAM,EAAE,mBAAmB;oBAC3BD,YAAY,EAAE,CAAC;oBACflE,QAAQ,EAAE;kBACZ;gBAAE;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN/L,OAAA;gBAAAyL,QAAA,gBACEzL,OAAA;kBAAOmM,KAAK,EAAE;oBAAElB,OAAO,EAAE,OAAO;oBAAE2B,YAAY,EAAE,CAAC;oBAAEkB,UAAU,EAAE,GAAG;oBAAEZ,KAAK,EAAE;kBAAO,CAAE;kBAAAzB,QAAA,EAAC;gBAErF;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR/L,OAAA;kBACE0F,IAAI,EAAC,gBAAgB;kBACrBC,KAAK,EAAEhE,QAAQ,CAACO,cAAe;kBAC/BmL,QAAQ,EAAE7H,iBAAkB;kBAC5B2G,KAAK,EAAE;oBACLmB,KAAK,EAAE,MAAM;oBACbhB,OAAO,EAAE,MAAM;oBACfkB,MAAM,EAAE,mBAAmB;oBAC3BD,YAAY,EAAE,CAAC;oBACflE,QAAQ,EAAE;kBACZ,CAAE;kBAAAoC,QAAA,gBAEFzL,OAAA;oBAAQ2F,KAAK,EAAC,EAAE;oBAAA8F,QAAA,EAAC;kBAA2B;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACpDhL,YAAY,CAACuF,GAAG,CAAClC,WAAW,iBAC3BpE,OAAA;oBAA6B2F,KAAK,EAAEvB,WAAW,CAACP,EAAG;oBAAA4H,QAAA,EAChDrH,WAAW,CAACvC;kBAAG,GADLuC,WAAW,CAACP,EAAE;oBAAA+H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEnB,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN/L,OAAA;cAAKmM,KAAK,EAAE;gBAAElB,OAAO,EAAE,MAAM;gBAAE8C,mBAAmB,EAAE,SAAS;gBAAEL,GAAG,EAAE,EAAE;gBAAEd,YAAY,EAAE;cAAG,CAAE;cAAAnB,QAAA,gBACzFzL,OAAA;gBAAAyL,QAAA,gBACEzL,OAAA;kBAAOmM,KAAK,EAAE;oBAAElB,OAAO,EAAE,OAAO;oBAAE2B,YAAY,EAAE,CAAC;oBAAEkB,UAAU,EAAE,GAAG;oBAAEZ,KAAK,EAAE;kBAAO,CAAE;kBAAAzB,QAAA,EAAC;gBAErF;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR/L,OAAA;kBACE8B,IAAI,EAAC,MAAM;kBACX4D,IAAI,EAAC,qBAAqB;kBAC1BC,KAAK,EAAEhE,QAAQ,CAACQ,mBAAoB;kBACpCkL,QAAQ,EAAE7H,iBAAkB;kBAC5B2G,KAAK,EAAE;oBACLmB,KAAK,EAAE,MAAM;oBACbhB,OAAO,EAAE,MAAM;oBACfkB,MAAM,EAAE,mBAAmB;oBAC3BD,YAAY,EAAE,CAAC;oBACflE,QAAQ,EAAE;kBACZ;gBAAE;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN/L,OAAA;gBAAAyL,QAAA,gBACEzL,OAAA;kBAAOmM,KAAK,EAAE;oBAAElB,OAAO,EAAE,OAAO;oBAAE2B,YAAY,EAAE,CAAC;oBAAEkB,UAAU,EAAE,GAAG;oBAAEZ,KAAK,EAAE;kBAAO,CAAE;kBAAAzB,QAAA,EAAC;gBAErF;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR/L,OAAA;kBACE8B,IAAI,EAAC,MAAM;kBACX4D,IAAI,EAAC,mBAAmB;kBACxBC,KAAK,EAAEhE,QAAQ,CAACS,iBAAkB;kBAClCiL,QAAQ,EAAE7H,iBAAkB;kBAC5B2G,KAAK,EAAE;oBACLmB,KAAK,EAAE,MAAM;oBACbhB,OAAO,EAAE,MAAM;oBACfkB,MAAM,EAAE,mBAAmB;oBAC3BD,YAAY,EAAE,CAAC;oBACflE,QAAQ,EAAE;kBACZ;gBAAE;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN/L,OAAA;cAAKmM,KAAK,EAAE;gBAAElB,OAAO,EAAE,MAAM;gBAAE8C,mBAAmB,EAAE,aAAa;gBAAEL,GAAG,EAAE,EAAE;gBAAEd,YAAY,EAAE;cAAG,CAAE;cAAAnB,QAAA,gBAC7FzL,OAAA;gBAAAyL,QAAA,gBACEzL,OAAA;kBAAOmM,KAAK,EAAE;oBAAElB,OAAO,EAAE,OAAO;oBAAE2B,YAAY,EAAE,CAAC;oBAAEkB,UAAU,EAAE,GAAG;oBAAEZ,KAAK,EAAE;kBAAO,CAAE;kBAAAzB,QAAA,EAAC;gBAErF;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR/L,OAAA;kBACE8B,IAAI,EAAC,QAAQ;kBACb4D,IAAI,EAAC,cAAc;kBACnBC,KAAK,EAAEhE,QAAQ,CAACW,YAAa;kBAC7B+K,QAAQ,EAAE7H,iBAAkB;kBAC5BoJ,GAAG,EAAC,GAAG;kBACPzC,KAAK,EAAE;oBACLmB,KAAK,EAAE,MAAM;oBACbhB,OAAO,EAAE,MAAM;oBACfkB,MAAM,EAAE,mBAAmB;oBAC3BD,YAAY,EAAE,CAAC;oBACflE,QAAQ,EAAE;kBACZ;gBAAE;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN/L,OAAA;gBAAAyL,QAAA,gBACEzL,OAAA;kBAAOmM,KAAK,EAAE;oBAAElB,OAAO,EAAE,OAAO;oBAAE2B,YAAY,EAAE,CAAC;oBAAEkB,UAAU,EAAE,GAAG;oBAAEZ,KAAK,EAAE;kBAAO,CAAE;kBAAAzB,QAAA,EAAC;gBAErF;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR/L,OAAA;kBACE8B,IAAI,EAAC,QAAQ;kBACb4D,IAAI,EAAC,WAAW;kBAChBC,KAAK,EAAEhE,QAAQ,CAACY,SAAU;kBAC1B8K,QAAQ,EAAE7H,iBAAkB;kBAC5BoJ,GAAG,EAAC,GAAG;kBACPzC,KAAK,EAAE;oBACLmB,KAAK,EAAE,MAAM;oBACbhB,OAAO,EAAE,MAAM;oBACfkB,MAAM,EAAE,mBAAmB;oBAC3BD,YAAY,EAAE,CAAC;oBACflE,QAAQ,EAAE;kBACZ;gBAAE;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN/L,OAAA;gBAAAyL,QAAA,gBACEzL,OAAA;kBAAOmM,KAAK,EAAE;oBAAElB,OAAO,EAAE,OAAO;oBAAE2B,YAAY,EAAE,CAAC;oBAAEkB,UAAU,EAAE,GAAG;oBAAEZ,KAAK,EAAE;kBAAO,CAAE;kBAAAzB,QAAA,EAAC;gBAErF;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR/L,OAAA;kBACE8B,IAAI,EAAC,QAAQ;kBACb4D,IAAI,EAAC,WAAW;kBAChBC,KAAK,EAAEhE,QAAQ,CAACa,SAAU;kBAC1B6K,QAAQ,EAAE7H,iBAAkB;kBAC5BoJ,GAAG,EAAC,GAAG;kBACPzC,KAAK,EAAE;oBACLmB,KAAK,EAAE,MAAM;oBACbhB,OAAO,EAAE,MAAM;oBACfkB,MAAM,EAAE,mBAAmB;oBAC3BD,YAAY,EAAE,CAAC;oBACflE,QAAQ,EAAE;kBACZ;gBAAE;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN/L,OAAA;cAAKmM,KAAK,EAAE;gBAAES,YAAY,EAAE;cAAG,CAAE;cAAAnB,QAAA,eAC/BzL,OAAA;gBAAOmM,KAAK,EAAE;kBAAElB,OAAO,EAAE,MAAM;kBAAEyB,UAAU,EAAE,QAAQ;kBAAEoB,UAAU,EAAE,GAAG;kBAAEZ,KAAK,EAAE,MAAM;kBAAES,MAAM,EAAE;gBAAU,CAAE;gBAAAlC,QAAA,gBACzGzL,OAAA;kBACE8B,IAAI,EAAC,UAAU;kBACf4D,IAAI,EAAC,UAAU;kBACfE,OAAO,EAAEjE,QAAQ,CAACU,QAAS;kBAC3BgL,QAAQ,EAAE7H,iBAAkB;kBAC5B2G,KAAK,EAAE;oBAAEC,WAAW,EAAE,CAAC;oBAAEa,SAAS,EAAE;kBAAa;gBAAE;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC,0BAEJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAEN/L,OAAA;cAAKmM,KAAK,EAAE;gBAAElB,OAAO,EAAE,MAAM;gBAAEyC,GAAG,EAAE,EAAE;gBAAEf,cAAc,EAAE;cAAW,CAAE;cAAAlB,QAAA,gBACnEzL,OAAA;gBACE8B,IAAI,EAAC,QAAQ;gBACb0K,OAAO,EAAEA,CAAA,KAAM;kBACb1L,YAAY,CAAC,KAAK,CAAC;kBACnBU,YAAY,CAAC,IAAI,CAAC;gBACpB,CAAE;gBACF2K,KAAK,EAAE;kBACLG,OAAO,EAAE,WAAW;kBACpBkB,MAAM,EAAE,mBAAmB;kBAC3BD,YAAY,EAAE,CAAC;kBACfM,UAAU,EAAE,OAAO;kBACnBX,KAAK,EAAE,MAAM;kBACb7D,QAAQ,EAAE,MAAM;kBAChByE,UAAU,EAAE,GAAG;kBACfH,MAAM,EAAE;gBACV,CAAE;gBAAAlC,QAAA,EACH;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT/L,OAAA;gBACE8B,IAAI,EAAC,QAAQ;gBACb+M,QAAQ,EAAE1N,UAAW;gBACrBgL,KAAK,EAAE;kBACLG,OAAO,EAAE,WAAW;kBACpBkB,MAAM,EAAE,MAAM;kBACdD,YAAY,EAAE,CAAC;kBACfM,UAAU,EAAE1M,UAAU,GAAG,MAAM,GAAG,kDAAkD;kBACpF+L,KAAK,EAAE,OAAO;kBACd7D,QAAQ,EAAE,MAAM;kBAChByE,UAAU,EAAE,GAAG;kBACfH,MAAM,EAAExM,UAAU,GAAG,aAAa,GAAG,SAAS;kBAC9CsM,SAAS,EAAE;gBACb,CAAE;gBAAAhC,QAAA,EAEDtK,UAAU,GACNI,SAAS,GAAG,0BAA0B,GAAG,mBAAmB,GAC5DA,SAAS,GAAG,wBAAwB,GAAG;cAAwB;gBAAAqK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAtL,OAAO,gBACNT,OAAA;QAAKmM,KAAK,EAAE;UAAEI,SAAS,EAAE,QAAQ;UAAED,OAAO,EAAE;QAAO,CAAE;QAAAb,QAAA,eACnDzL,OAAA;UAAAyL,QAAA,EAAG;QAA6B;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,GACJpL,KAAK,gBACPX,OAAA;QAAKmM,KAAK,EAAE;UAAEI,SAAS,EAAE,QAAQ;UAAED,OAAO,EAAE,MAAM;UAAEY,KAAK,EAAE;QAAU,CAAE;QAAAzB,QAAA,gBACrEzL,OAAA;UAAAyL,QAAA,GAAG,8BAA4B,EAAC9K,KAAK;QAAA;UAAAiL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1C/L,OAAA;UACEwM,OAAO,EAAEA,CAAA,KAAMjF,MAAM,CAACnH,QAAQ,CAAC0O,MAAM,CAAC,CAAE;UACxC3C,KAAK,EAAE;YAAEE,SAAS,EAAE,MAAM;YAAEC,OAAO,EAAE,UAAU;YAAEuB,UAAU,EAAE,SAAS;YAAEX,KAAK,EAAE,OAAO;YAAEM,MAAM,EAAE,MAAM;YAAED,YAAY,EAAE,KAAK;YAAEI,MAAM,EAAE;UAAU,CAAE;UAAAlC,QAAA,EAClJ;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,GACJ,CAAC7G,KAAK,CAACC,OAAO,CAAC5E,mBAAmB,CAAC,IAAIA,mBAAmB,CAAC0E,MAAM,KAAK,CAAC,gBACzEjF,OAAA;QAAKmM,KAAK,EAAE;UAAEI,SAAS,EAAE,QAAQ;UAAED,OAAO,EAAE;QAAO,CAAE;QAAAb,QAAA,gBACnDzL,OAAA;UAAAyL,QAAA,EAAIpK,UAAU,GAAG,uBAAuB,GAAG;QAAyB;UAAAuK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EACxEnJ,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAE;UAC3DtC,mBAAmB;UACnB4E,OAAO,EAAED,KAAK,CAACC,OAAO,CAAC5E,mBAAmB,CAAC;UAC3C0E,MAAM,EAAE1E,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAE0E,MAAM;UACnC5D;QACF,CAAC,CAAC;MAAA;QAAAuK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,gBAEN/L,OAAA;QAAOwL,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAClCzL,OAAA;UAAAyL,QAAA,eACEzL,OAAA;YAAAyL,QAAA,gBACEzL,OAAA;cAAAyL,QAAA,EAAI;YAAG;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACZ/L,OAAA;cAAAyL,QAAA,EAAI;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACb/L,OAAA;cAAAyL,QAAA,EAAI;YAAe;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxB/L,OAAA;cAAAyL,QAAA,EAAI;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACf/L,OAAA;cAAAyL,QAAA,EAAI;YAAY;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrB/L,OAAA;cAAAyL,QAAA,EAAI;YAAiB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1B/L,OAAA;cAAAyL,QAAA,EAAI;YAAW;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpB/L,OAAA;cAAAyL,QAAA,EAAI;YAAO;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACR/L,OAAA;UAAAyL,QAAA,EACGlL,mBAAmB,CAAC+F,GAAG,CAAC,CAAC3C,EAAE,EAAEoL,KAAK,KAAK;YAAA,IAAAC,gBAAA;YACtCpM,OAAO,CAACC,GAAG,CAAC,uBAAuBkM,KAAK,GAAG,EAAEpL,EAAE,CAAC;YAChD,MAAMyD,WAAW,GAAGzD,EAAE,CAAC5B,YAAY,IAAI4B,EAAE,CAACyD,WAAW,IAAI,KAAK;YAC9D,MAAMiB,SAAS,GAAGX,UAAU,CAAC/D,EAAE,CAAC0E,SAAS,IAAI1E,EAAE,CAAC1B,UAAU,CAAC;YAC3D,MAAMqG,eAAe,GAAGZ,UAAU,CAAC/D,EAAE,CAAC2E,eAAe,IAAI3E,EAAE,CAACvB,iBAAiB,CAAC;YAC9E,MAAMgC,WAAW,GAAG,EAAA4K,gBAAA,GAAArL,EAAE,CAACS,WAAW,cAAA4K,gBAAA,uBAAdA,gBAAA,CAAgBnN,GAAG,KAAI8B,EAAE,CAACzB,cAAc,IAAI,cAAc;YAE9EU,OAAO,CAACC,GAAG,CAAC,6BAA6Bc,EAAE,CAAC9B,GAAG,GAAG,EAAE;cAClDA,GAAG,EAAE8B,EAAE,CAAC9B,GAAG;cACXC,IAAI,EAAE6B,EAAE,CAAC7B,IAAI;cACbsF,WAAW;cACXpF,MAAM,EAAE2B,EAAE,CAAC3B,MAAM;cACjBqG,SAAS;cACTC,eAAe;cACflE;YACF,CAAC,CAAC;YAEF,oBACEpE,OAAA;cAAAyL,QAAA,gBACEzL,OAAA;gBAAAyL,QAAA,EAAK9H,EAAE,CAAC9B,GAAG,IAAI;cAAK;gBAAA+J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC1B/L,OAAA;gBAAAyL,QAAA,EAAK9H,EAAE,CAAC7B,IAAI,IAAI;cAAK;gBAAA8J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC3B/L,OAAA;gBAAAyL,QAAA,EAAKrE;cAAW;gBAAAwE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtB/L,OAAA;gBAAAyL,QAAA,EAAK9H,EAAE,CAAC3B,MAAM,IAAI;cAAK;gBAAA4J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC7B/L,OAAA;gBAAAyL,QAAA,EAAKpD;cAAS;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACpB/L,OAAA;gBAAAyL,QAAA,EAAKnD;cAAe;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC1B/L,OAAA;gBAAAyL,QAAA,EAAKrH;cAAW;gBAAAwH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtB/L,OAAA;gBAAAyL,QAAA,eACEzL,OAAA;kBAAKmM,KAAK,EAAE;oBAAElB,OAAO,EAAE,MAAM;oBAAEyC,GAAG,EAAE;kBAAM,CAAE;kBAAAjC,QAAA,gBAC1CzL,OAAA;oBACEwM,OAAO,EAAEA,CAAA,KAAMvF,UAAU,CAACtD,EAAE,CAAE;oBAC9BwI,KAAK,EAAE;sBACL0B,UAAU,EAAE,SAAS;sBACrBX,KAAK,EAAE,OAAO;sBACdM,MAAM,EAAE,MAAM;sBACdD,YAAY,EAAE,KAAK;sBACnBjB,OAAO,EAAE,UAAU;sBACnBqB,MAAM,EAAE,SAAS;sBACjB1C,OAAO,EAAE,MAAM;sBACfyB,UAAU,EAAE,QAAQ;sBACpBgB,GAAG,EAAE;oBACP,CAAE;oBAAAjC,QAAA,gBAEFzL,OAAA,CAAC9B,eAAe;sBAACgO,IAAI,EAAErN;oBAAO;sBAAA+M,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACjC/L,OAAA;sBAAAyL,QAAA,EAAM;oBAAQ;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,eACT/L,OAAA;oBACEwM,OAAO,EAAEA,CAAA,KAAMlF,YAAY,CAAC3D,EAAE,CAACE,EAAE,CAAE;oBACnCsI,KAAK,EAAE;sBACL0B,UAAU,EAAE,SAAS;sBACrBX,KAAK,EAAE,OAAO;sBACdM,MAAM,EAAE,MAAM;sBACdD,YAAY,EAAE,KAAK;sBACnBjB,OAAO,EAAE,UAAU;sBACnBqB,MAAM,EAAE,SAAS;sBACjB1C,OAAO,EAAE,MAAM;sBACfyB,UAAU,EAAE,QAAQ;sBACpBgB,GAAG,EAAE;oBACP,CAAE;oBAAAjC,QAAA,gBAEFzL,OAAA,CAAC9B,eAAe;sBAACgO,IAAI,EAAEpN;oBAAQ;sBAAA8M,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAClC/L,OAAA;sBAAAyL,QAAA,EAAM;oBAAS;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GA7CEpI,EAAE,CAACE,EAAE,IAAIkL,KAAK;cAAAnD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA8CnB,CAAC;UAET,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC5L,EAAA,CAjwCuBD,WAAW;EAAA,QAChBlC,WAAW;AAAA;AAAAiR,EAAA,GADN/O,WAAW;AAAA,IAAA+O,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}