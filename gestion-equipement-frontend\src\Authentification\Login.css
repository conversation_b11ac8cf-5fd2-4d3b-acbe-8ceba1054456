.login-container {
  background-size: cover;
  background-position: center;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
}
 
.login-box {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  padding: 2.5rem 2rem;
  border-radius: 18px;
  width: 100%;
  max-width: 400px;
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.25), 0 1.5px 6px rgba(0,0,0,0.08);
  border: 1.5px solid rgba(255,255,255,0.4);
  transition: box-shadow 0.3s, transform 0.3s;
}

.login-box:hover {
  box-shadow: 0 16px 40px 0 rgba(31, 38, 135, 0.35), 0 3px 12px rgba(0,0,0,0.12);
  transform: translateY(-4px) scale(1.02);
}
/* Style pour le titre */
.login-box h2 {
  font-family: 'Segoe UI', '<PERSON>o', Arial, sans-serif;
  font-weight: 700;
  color: #1565c0;
  margin-bottom: 1.5rem;
  letter-spacing: 1px;
}

/* Style pour le bouton de connexion */
.login-box button[type="submit"], .login-box .btn-primary {
  background: linear-gradient(90deg, #1976d2 0%, #42a5f5 100%);
  border: none;
  color: #fff;
  font-weight: 700;
  font-size: 1.15rem;
  border-radius: 10px;
  box-shadow: 0 4px 16px rgba(21,101,192,0.18);
  letter-spacing: 1px;
  padding: 12px 0;
  margin-top: 10px;
  margin-bottom: 10px;
  transition: background 0.2s, transform 0.2s, box-shadow 0.2s;
}
.login-box button[type="submit"]:hover, .login-box .btn-primary:hover {
  background: linear-gradient(90deg, #42a5f5 0%, #1976d2 100%);
  transform: scale(1.04);
  box-shadow: 0 8px 24px rgba(21,101,192,0.22);
}

/* Style pour le lien "Sign up" */
a {
  color: #1565c0;
  font-weight: 500;
  transition: color 0.2s;
}
a:hover {
  text-decoration: underline;
  color: #003c8f;
}
/* Labels couleur mauve */
.login-box label {
  color: #2d3a4b;
  font-weight: 600;
}