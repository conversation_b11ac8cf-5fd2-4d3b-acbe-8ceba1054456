{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\gestion-equipement-frontend\\\\src\\\\pages\\\\Equipements.js\",\n  _s = $RefreshSig$();\nimport './Equipements.css';\nimport { Link, useLocation } from 'react-router-dom';\nimport logo from '../assets/images/asment logo.jpg';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { faLaptop, faUsers, faExchangeAlt, faTruck, faUserCog, faSignOutAlt, faSearch, faBell, faTimes, faPlus, faEdit, faTrash, faSave } from '@fortawesome/free-solid-svg-icons';\nimport { useEffect, useState } from \"react\";\nimport jsPDF from \"jspdf\";\nimport \"jspdf-autotable\";\nimport * as XLSX from \"xlsx\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function Equipements() {\n  _s();\n  const location = useLocation();\n  const [equipements, setEquipements] = useState([]);\n  const [filteredEquipements, setFilteredEquipements] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [showModal, setShowModal] = useState(false);\n  const [currentEquipement, setCurrentEquipement] = useState(null);\n  const [fournisseurs, setFournisseurs] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [submitting, setSubmitting] = useState(false);\n  const [searchTerm, setSearchTerm] = useState('');\n\n  // État du formulaire\n  const [formData, setFormData] = useState({\n    nom: '',\n    type: '',\n    numero_serie: '',\n    statut: 'DISPONIBLE',\n    date_achat: '',\n    fournisseur_id: '',\n    date_debut_garantie: '',\n    date_fin_garantie: '',\n    en_stock: true,\n    stock_actuel: 1,\n    stock_max: 1,\n    stock_min: 1,\n    categorie_id: ''\n  });\n\n  // Options pour les statuts et types\n  const statutOptions = ['DISPONIBLE', 'EN_MAINTENANCE', 'OCCUPE', 'HORS_SERVICE'];\n  const typeOptions = ['ORDINATEUR', 'IMPRIMANTE', 'SCANNER', 'TELEPHONE', 'TABLETTE', 'SERVEUR', 'AUTRE'];\n  useEffect(() => {\n    loadEquipements();\n    loadFournisseurs();\n    loadCategories();\n  }, []);\n  useEffect(() => {\n    if (searchTerm === '') {\n      setFilteredEquipements(equipements);\n    } else {\n      const filtered = equipements.filter(equipement => {\n        var _equipement$fournisse;\n        return equipement.nom.toLowerCase().includes(searchTerm.toLowerCase()) || equipement.type.toLowerCase().includes(searchTerm.toLowerCase()) || equipement.numero_serie && equipement.numero_serie.toLowerCase().includes(searchTerm.toLowerCase()) || equipement.statut.toLowerCase().includes(searchTerm.toLowerCase()) || ((_equipement$fournisse = equipement.fournisseur) === null || _equipement$fournisse === void 0 ? void 0 : _equipement$fournisse.nom) && equipement.fournisseur.nom.toLowerCase().includes(searchTerm.toLowerCase());\n      });\n      setFilteredEquipements(filtered);\n    }\n  }, [searchTerm, equipements]);\n  const loadEquipements = () => {\n    setLoading(true);\n    const token = localStorage.getItem('authToken');\n    const headers = {\n      'Content-Type': 'application/json',\n      ...(token && {\n        'Authorization': `Bearer ${token}`\n      })\n    };\n    fetch(\"http://localhost:8081/api/equipements\", {\n      method: 'GET',\n      headers\n    }).then(res => {\n      if (!res.ok) throw new Error(`Erreur HTTP: ${res.status}`);\n      return res.json();\n    }).then(data => {\n      if (Array.isArray(data)) {\n        setEquipements(data);\n        setFilteredEquipements(data);\n        setError(null);\n      } else {\n        throw new Error(\"Format de données incorrect\");\n      }\n    }).catch(err => {\n      setError(err.message);\n      setEquipements([]);\n      setFilteredEquipements([]);\n    }).finally(() => setLoading(false));\n  };\n  const loadFournisseurs = () => {\n    const token = localStorage.getItem('authToken');\n    fetch(\"http://localhost:8081/api/fournisseurs\", {\n      headers: {\n        'Content-Type': 'application/json',\n        ...(token && {\n          'Authorization': `Bearer ${token}`\n        })\n      }\n    }).then(res => res.ok ? res.json() : []).then(data => setFournisseurs(Array.isArray(data) ? data : [])).catch(err => {\n      console.error(\"Erreur fournisseurs:\", err);\n      setFournisseurs([]);\n    });\n  };\n  const loadCategories = () => {\n    const token = localStorage.getItem('authToken');\n    fetch(\"http://localhost:8081/api/categories\", {\n      headers: {\n        'Content-Type': 'application/json',\n        ...(token && {\n          'Authorization': `Bearer ${token}`\n        })\n      }\n    }).then(res => res.ok ? res.json() : []).then(data => setCategories(Array.isArray(data) ? data : [])).catch(err => {\n      console.error(\"Erreur catégories:\", err);\n      setCategories([]);\n    });\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value,\n      type,\n      checked\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setSubmitting(true);\n    try {\n      const token = localStorage.getItem('authToken');\n      const method = currentEquipement ? 'PUT' : 'POST';\n      const url = currentEquipement ? `http://localhost:8081/api/equipements/${currentEquipement.id}` : \"http://localhost:8081/api/equipements\";\n      const response = await fetch(url, {\n        method,\n        headers: {\n          'Content-Type': 'application/json',\n          ...(token && {\n            'Authorization': `Bearer ${token}`\n          })\n        },\n        body: JSON.stringify({\n          ...formData,\n          fournisseur_id: formData.fournisseur_id || null\n        })\n      });\n      if (!response.ok) throw new Error(`Erreur HTTP: ${response.status}`);\n      const result = await response.json();\n      if (currentEquipement) {\n        setEquipements(prev => prev.map(eq => eq.id === currentEquipement.id ? result : eq));\n        alert('Équipement modifié avec succès !');\n      } else {\n        setEquipements(prev => [...prev, result]);\n        alert('Équipement ajouté avec succès !');\n      }\n      resetForm();\n    } catch (err) {\n      console.error(\"Erreur:\", err);\n      alert(`Erreur: ${err.message}`);\n    } finally {\n      setSubmitting(false);\n    }\n  };\n  const handleEdit = equipement => {\n    var _equipement$date_acha, _equipement$fournisse2, _equipement$date_debu, _equipement$date_fin_;\n    setCurrentEquipement(equipement);\n    setFormData({\n      nom: equipement.nom,\n      type: equipement.type,\n      numero_serie: equipement.numero_serie,\n      statut: equipement.statut,\n      date_achat: ((_equipement$date_acha = equipement.date_achat) === null || _equipement$date_acha === void 0 ? void 0 : _equipement$date_acha.split('T')[0]) || '',\n      fournisseur_id: ((_equipement$fournisse2 = equipement.fournisseur) === null || _equipement$fournisse2 === void 0 ? void 0 : _equipement$fournisse2.id) || '',\n      date_debut_garantie: ((_equipement$date_debu = equipement.date_debut_garantie) === null || _equipement$date_debu === void 0 ? void 0 : _equipement$date_debu.split('T')[0]) || '',\n      date_fin_garantie: ((_equipement$date_fin_ = equipement.date_fin_garantie) === null || _equipement$date_fin_ === void 0 ? void 0 : _equipement$date_fin_.split('T')[0]) || '',\n      en_stock: equipement.en_stock,\n      stock_actuel: equipement.stock_actuel,\n      stock_max: equipement.stock_max,\n      stock_min: equipement.stock_min,\n      categorie_id: equipement.categorie_id\n    });\n    setShowModal(true);\n  };\n  const handleDelete = async id => {\n    if (!window.confirm(\"Êtes-vous sûr de vouloir supprimer cet équipement ?\")) return;\n    try {\n      const token = localStorage.getItem('authToken');\n      const response = await fetch(`http://localhost:8081/api/equipements/${id}`, {\n        method: 'DELETE',\n        headers: {\n          'Content-Type': 'application/json',\n          ...(token && {\n            'Authorization': `Bearer ${token}`\n          })\n        }\n      });\n      if (!response.ok) throw new Error(`Erreur HTTP: ${response.status}`);\n      setEquipements(prev => prev.filter(eq => eq.id !== id));\n      alert('Équipement supprimé avec succès !');\n    } catch (err) {\n      console.error(\"Erreur:\", err);\n      alert(`Erreur lors de la suppression: ${err.message}`);\n    }\n  };\n  const resetForm = () => {\n    setFormData({\n      nom: '',\n      type: '',\n      numero_serie: '',\n      statut: 'DISPONIBLE',\n      date_achat: '',\n      fournisseur_id: '',\n      date_debut_garantie: '',\n      date_fin_garantie: '',\n      en_stock: true,\n      stock_actuel: 1,\n      stock_max: 1,\n      stock_min: 1,\n      categorie_id: ''\n    });\n    setCurrentEquipement(null);\n    setShowModal(false);\n  };\n  const handleExportExcel = () => {\n    if (filteredEquipements.length === 0) {\n      alert(\"Aucune donnée à exporter\");\n      return;\n    }\n    const worksheet = XLSX.utils.json_to_sheet(filteredEquipements.map(eq => {\n      var _eq$fournisseur;\n      return {\n        Nom: eq.nom,\n        Type: eq.type,\n        'Numéro de série': eq.numero_serie,\n        Statut: eq.statut,\n        'Date achat': eq.date_achat,\n        Fournisseur: (_eq$fournisseur = eq.fournisseur) === null || _eq$fournisseur === void 0 ? void 0 : _eq$fournisseur.nom\n      };\n    }));\n    const workbook = XLSX.utils.book_new();\n    XLSX.utils.book_append_sheet(workbook, worksheet, \"Equipements\");\n    XLSX.writeFile(workbook, \"equipements.xlsx\");\n  };\n  const handleExportPDF = () => {\n    if (filteredEquipements.length === 0) {\n      alert(\"Aucune donnée à exporter\");\n      return;\n    }\n    const doc = new jsPDF();\n    doc.text(\"Liste des équipements\", 14, 16);\n    doc.autoTable({\n      head: [['Nom', 'Type', 'Numéro série', 'Statut', 'Date achat', 'Fournisseur']],\n      body: filteredEquipements.map(eq => {\n        var _eq$fournisseur2;\n        return [eq.nom || '', eq.type || '', eq.numero_serie || '', eq.statut || '', eq.date_achat || '', ((_eq$fournisseur2 = eq.fournisseur) === null || _eq$fournisseur2 === void 0 ? void 0 : _eq$fournisseur2.nom) || ''];\n      }),\n      startY: 22,\n      styles: {\n        fontSize: 10\n      }\n    });\n    doc.save(\"equipements.pdf\");\n  };\n  const handlePrint = () => {\n    window.print();\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"equipements-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sidebar\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"logo-section\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: logo,\n          alt: \"Logo Entreprise\",\n          className: \"company-logo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 311,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"main-menu\",\n        children: /*#__PURE__*/_jsxDEV(\"ul\", {\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/\",\n              className: location.pathname === '/' ? 'home-active active' : '',\n              children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: faLaptop,\n                style: {\n                  marginRight: 8\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 17\n              }, this), \" Accueil\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/equipements\",\n              className: location.pathname === '/equipements' ? 'active' : '',\n              children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: faLaptop,\n                style: {\n                  marginRight: 8\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 17\n              }, this), \" G\\xE9rer les \\xE9quipements\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/employes\",\n              className: location.pathname === '/employes' ? 'active' : '',\n              children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: faUsers,\n                style: {\n                  marginRight: 8\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 17\n              }, this), \" G\\xE9rer les employ\\xE9s\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/affectations\",\n              className: location.pathname === '/affectations' ? 'active' : '',\n              children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: faExchangeAlt,\n                style: {\n                  marginRight: 8\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 17\n              }, this), \" Suivi des affectations\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/fournisseurs\",\n              className: location.pathname === '/fournisseurs' ? 'active' : '',\n              children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: faTruck,\n                style: {\n                  marginRight: 8\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 17\n              }, this), \" G\\xE9rer les fournisseurs\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"secondary-links\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/profil\",\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faUserCog,\n            style: {\n              marginRight: 8\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 13\n          }, this), \" Mon profil & param\\xE8tres\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 343,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: 'auto',\n          padding: '20px 0 0 0',\n          textAlign: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"logout-btn\",\n          onClick: () => {\n            localStorage.removeItem('authToken');\n            window.location.href = '/';\n          },\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faSignOutAlt,\n            style: {\n              marginRight: 8\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 13\n          }, this), \" D\\xE9connexion\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 348,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 310,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"equipements-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-header-row\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-bar\",\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faSearch,\n            className: \"search-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Recherche par mot cl\\xE9\",\n            value: searchTerm,\n            onChange: e => setSearchTerm(e.target.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 364,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"profile-block\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"notification-icon\",\n            children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n              icon: faBell\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Responsable IT\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faUserCog\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 373,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 363,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"equipements-title\",\n        children: [\"Liste des \\xE9quipements (\", filteredEquipements.length, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 383,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"action-buttons\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          onClick: () => setShowModal(true),\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faPlus,\n            style: {\n              marginRight: 8\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 13\n          }, this), \"Ajouter un \\xE9quipement\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          onClick: handleExportExcel,\n          children: \"Exporter Excel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 392,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          onClick: handleExportPDF,\n          children: \"Exporter PDF\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 395,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          onClick: handlePrint,\n          children: \"Imprimer\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 398,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 387,\n        columnNumber: 9\n      }, this), showModal && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-overlay\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: currentEquipement ? 'Modifier équipement' : 'Ajouter un équipement'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: resetForm,\n              children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: faTimes\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 412,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSubmit,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-grid\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Nom de l'\\xE9quipement *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"nom\",\n                  value: formData.nom,\n                  onChange: handleInputChange,\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 420,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Type *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 430,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"type\",\n                  value: formData.type,\n                  onChange: handleInputChange,\n                  required: true,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"S\\xE9lectionner un type\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 437,\n                    columnNumber: 23\n                  }, this), typeOptions.map(type => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: type,\n                    children: type\n                  }, type, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 439,\n                    columnNumber: 25\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 431,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 429,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Num\\xE9ro de s\\xE9rie\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 445,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"numero_serie\",\n                  value: formData.numero_serie,\n                  onChange: handleInputChange\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 446,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Statut *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 455,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"statut\",\n                  value: formData.statut,\n                  onChange: handleInputChange,\n                  required: true,\n                  children: statutOptions.map(statut => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: statut,\n                    children: statut\n                  }, statut, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 463,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 456,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 454,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Date d'achat\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 469,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  name: \"date_achat\",\n                  value: formData.date_achat,\n                  onChange: handleInputChange\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 470,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 468,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Fournisseur\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 479,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"fournisseur_id\",\n                  value: formData.fournisseur_id,\n                  onChange: handleInputChange,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"S\\xE9lectionner un fournisseur\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 485,\n                    columnNumber: 23\n                  }, this), fournisseurs.map(fournisseur => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: fournisseur.id,\n                    children: fournisseur.nom\n                  }, fournisseur.id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 487,\n                    columnNumber: 25\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 480,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 478,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"D\\xE9but de garantie\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 495,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  name: \"date_debut_garantie\",\n                  value: formData.date_debut_garantie,\n                  onChange: handleInputChange\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 496,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 494,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Fin de garantie\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 505,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  name: \"date_fin_garantie\",\n                  value: formData.date_fin_garantie,\n                  onChange: handleInputChange\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 506,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 504,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Stock actuel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 515,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  name: \"stock_actuel\",\n                  value: formData.stock_actuel,\n                  onChange: handleInputChange,\n                  min: \"0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 516,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 514,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Stock maximum\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 526,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  name: \"stock_max\",\n                  value: formData.stock_max,\n                  onChange: handleInputChange,\n                  min: \"1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 527,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 525,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Stock minimum\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 537,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  name: \"stock_min\",\n                  value: formData.stock_min,\n                  onChange: handleInputChange,\n                  min: \"0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 538,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 536,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-checkbox\",\n              children: /*#__PURE__*/_jsxDEV(\"label\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  name: \"en_stock\",\n                  checked: formData.en_stock,\n                  onChange: handleInputChange\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 550,\n                  columnNumber: 21\n                }, this), \"\\xC9quipement en stock\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 549,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 548,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-actions\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: resetForm,\n                children: \"Annuler\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 561,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                disabled: submitting,\n                children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                  icon: currentEquipement ? faSave : faPlus,\n                  style: {\n                    marginRight: 8\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 565,\n                  columnNumber: 21\n                }, this), submitting ? 'En cours...' : currentEquipement ? 'Enregistrer' : 'Ajouter']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 564,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 560,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 416,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 406,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 405,\n        columnNumber: 11\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-message\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Chargement des \\xE9quipements...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 577,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 576,\n        columnNumber: 11\n      }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-message\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"Erreur lors du chargement : \", error]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 581,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.location.reload(),\n          children: \"Actualiser la page\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 582,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 580,\n        columnNumber: 11\n      }, this) : filteredEquipements.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"empty-message\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: searchTerm ? 'Aucun résultat trouvé' : 'Aucun équipement trouvé'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 588,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 587,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"table\", {\n        className: \"equipements-table\",\n        children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Nom\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 594,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Type\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 595,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Num\\xE9ro de s\\xE9rie\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 596,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Statut\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 597,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Date d'achat\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 598,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Fournisseur\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 599,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Actions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 600,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 593,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 592,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n          children: filteredEquipements.map(eq => {\n            var _eq$date_achat, _eq$fournisseur3;\n            return /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                children: eq.nom || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 606,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: eq.type || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 607,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: eq.numero_serie || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 608,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: eq.statut || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 609,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: ((_eq$date_achat = eq.date_achat) === null || _eq$date_achat === void 0 ? void 0 : _eq$date_achat.split('T')[0]) || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 610,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: ((_eq$fournisseur3 = eq.fournisseur) === null || _eq$fournisseur3 === void 0 ? void 0 : _eq$fournisseur3.nom) || 'Non spécifié'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 611,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"actions-cell\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleEdit(eq),\n                  className: \"action-btn edit-btn\",\n                  title: \"Modifier\",\n                  children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                    icon: faEdit\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 618,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 613,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleDelete(eq.id),\n                  className: \"action-btn delete-btn\",\n                  title: \"Supprimer\",\n                  children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                    icon: faTrash\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 625,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 620,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 612,\n                columnNumber: 19\n              }, this)]\n            }, eq.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 605,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 603,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 591,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 362,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 308,\n    columnNumber: 5\n  }, this);\n}\n_s(Equipements, \"eJiLtHr6zN4yRdSIXtWM3cjXfpI=\", false, function () {\n  return [useLocation];\n});\n_c = Equipements;\nvar _c;\n$RefreshReg$(_c, \"Equipements\");", "map": {"version": 3, "names": ["Link", "useLocation", "logo", "FontAwesomeIcon", "faLaptop", "faUsers", "faExchangeAlt", "faTruck", "faUserCog", "faSignOutAlt", "faSearch", "faBell", "faTimes", "faPlus", "faEdit", "faTrash", "faSave", "useEffect", "useState", "jsPDF", "XLSX", "jsxDEV", "_jsxDEV", "Equipements", "_s", "location", "equipements", "setEquipements", "filteredEquipements", "setFilteredEquipements", "loading", "setLoading", "error", "setError", "showModal", "setShowModal", "currentEquipement", "setCurrentEquipement", "fournisseurs", "setFournisseurs", "categories", "setCategories", "submitting", "setSubmitting", "searchTerm", "setSearchTerm", "formData", "setFormData", "nom", "type", "numero_serie", "statut", "date_achat", "fournisseur_id", "date_debut_garantie", "date_fin_garantie", "en_stock", "stock_actuel", "stock_max", "stock_min", "categorie_id", "statutOptions", "typeOptions", "loadEquipements", "loadFournisseurs", "loadCategories", "filtered", "filter", "equipement", "_equipement$fournisse", "toLowerCase", "includes", "<PERSON><PERSON><PERSON><PERSON>", "token", "localStorage", "getItem", "headers", "fetch", "method", "then", "res", "ok", "Error", "status", "json", "data", "Array", "isArray", "catch", "err", "message", "finally", "console", "handleInputChange", "e", "name", "value", "checked", "target", "prev", "handleSubmit", "preventDefault", "url", "id", "response", "body", "JSON", "stringify", "result", "map", "eq", "alert", "resetForm", "handleEdit", "_equipement$date_acha", "_equipement$fournisse2", "_equipement$date_debu", "_equipement$date_fin_", "split", "handleDelete", "window", "confirm", "handleExportExcel", "length", "worksheet", "utils", "json_to_sheet", "_eq$fournisseur", "Nom", "Type", "Statut", "Fournisseur", "workbook", "book_new", "book_append_sheet", "writeFile", "handleExportPDF", "doc", "text", "autoTable", "head", "_eq$fournisseur2", "startY", "styles", "fontSize", "save", "handlePrint", "print", "className", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "pathname", "icon", "style", "marginRight", "marginTop", "padding", "textAlign", "onClick", "removeItem", "href", "placeholder", "onChange", "onSubmit", "required", "min", "disabled", "reload", "_eq$date_achat", "_eq$fournisseur3", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/gestion-equipement-frontend/src/pages/Equipements.js"], "sourcesContent": ["import './Equipements.css';\nimport { Link, useLocation } from 'react-router-dom';\nimport logo from '../assets/images/asment logo.jpg';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { \n  faLaptop, \n  faUsers, \n  faExchangeAlt, \n  faTruck, \n  faUserCog, \n  faSignOutAlt, \n  faSearch, \n  faBell, \n  faTimes, \n  faPlus,\n  faEdit,\n  faTrash,\n  faSave\n} from '@fortawesome/free-solid-svg-icons';\nimport { useEffect, useState } from \"react\";\nimport jsPDF from \"jspdf\";\nimport \"jspdf-autotable\";\nimport * as XLSX from \"xlsx\";\n\nexport default function Equipements() {\n  const location = useLocation();\n  const [equipements, setEquipements] = useState([]);\n  const [filteredEquipements, setFilteredEquipements] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [showModal, setShowModal] = useState(false);\n  const [currentEquipement, setCurrentEquipement] = useState(null);\n  const [fournisseurs, setFournisseurs] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [submitting, setSubmitting] = useState(false);\n  const [searchTerm, setSearchTerm] = useState('');\n\n  // État du formulaire\n  const [formData, setFormData] = useState({\n    nom: '',\n    type: '',\n    numero_serie: '',\n    statut: 'DISPONIBLE',\n    date_achat: '',\n    fournisseur_id: '',\n    date_debut_garantie: '',\n    date_fin_garantie: '',\n    en_stock: true,\n    stock_actuel: 1,\n    stock_max: 1,\n    stock_min: 1,\n    categorie_id: ''\n  });\n\n  // Options pour les statuts et types\n  const statutOptions = ['DISPONIBLE', 'EN_MAINTENANCE', 'OCCUPE', 'HORS_SERVICE'];\n  const typeOptions = ['ORDINATEUR', 'IMPRIMANTE', 'SCANNER', 'TELEPHONE', 'TABLETTE', 'SERVEUR', 'AUTRE'];\n\n  useEffect(() => {\n    loadEquipements();\n    loadFournisseurs();\n    loadCategories();\n  }, []);\n\n  useEffect(() => {\n    if (searchTerm === '') {\n      setFilteredEquipements(equipements);\n    } else {\n      const filtered = equipements.filter(equipement =>\n        equipement.nom.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        equipement.type.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        (equipement.numero_serie && equipement.numero_serie.toLowerCase().includes(searchTerm.toLowerCase())) ||\n        equipement.statut.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        (equipement.fournisseur?.nom && equipement.fournisseur.nom.toLowerCase().includes(searchTerm.toLowerCase()))\n      );\n      setFilteredEquipements(filtered);\n    }\n  }, [searchTerm, equipements]);\n\n  const loadEquipements = () => {\n    setLoading(true);\n    const token = localStorage.getItem('authToken');\n    const headers = {\n      'Content-Type': 'application/json',\n      ...(token && { 'Authorization': `Bearer ${token}` })\n    };\n\n    fetch(\"http://localhost:8081/api/equipements\", { method: 'GET', headers })\n      .then(res => {\n        if (!res.ok) throw new Error(`Erreur HTTP: ${res.status}`);\n        return res.json();\n      })\n      .then(data => {\n        if (Array.isArray(data)) {\n          setEquipements(data);\n          setFilteredEquipements(data);\n          setError(null);\n        } else {\n          throw new Error(\"Format de données incorrect\");\n        }\n      })\n      .catch(err => {\n        setError(err.message);\n        setEquipements([]);\n        setFilteredEquipements([]);\n      })\n      .finally(() => setLoading(false));\n  };\n\n  const loadFournisseurs = () => {\n    const token = localStorage.getItem('authToken');\n    fetch(\"http://localhost:8081/api/fournisseurs\", {\n      headers: { \n        'Content-Type': 'application/json',\n        ...(token && { 'Authorization': `Bearer ${token}` })\n      }\n    })\n      .then(res => res.ok ? res.json() : [])\n      .then(data => setFournisseurs(Array.isArray(data) ? data : []))\n      .catch(err => {\n        console.error(\"Erreur fournisseurs:\", err);\n        setFournisseurs([]);\n      });\n  };\n\n  const loadCategories = () => {\n    const token = localStorage.getItem('authToken');\n    fetch(\"http://localhost:8081/api/categories\", {\n      headers: { \n        'Content-Type': 'application/json',\n        ...(token && { 'Authorization': `Bearer ${token}` })\n      }\n    })\n      .then(res => res.ok ? res.json() : [])\n      .then(data => setCategories(Array.isArray(data) ? data : []))\n      .catch(err => {\n        console.error(\"Erreur catégories:\", err);\n        setCategories([]);\n      });\n  };\n\n  const handleInputChange = (e) => {\n    const { name, value, type, checked } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setSubmitting(true);\n\n    try {\n      const token = localStorage.getItem('authToken');\n      const method = currentEquipement ? 'PUT' : 'POST';\n      const url = currentEquipement \n        ? `http://localhost:8081/api/equipements/${currentEquipement.id}`\n        : \"http://localhost:8081/api/equipements\";\n\n      const response = await fetch(url, {\n        method,\n        headers: {\n          'Content-Type': 'application/json',\n          ...(token && { 'Authorization': `Bearer ${token}` })\n        },\n        body: JSON.stringify({\n          ...formData,\n          fournisseur_id: formData.fournisseur_id || null\n        })\n      });\n\n      if (!response.ok) throw new Error(`Erreur HTTP: ${response.status}`);\n\n      const result = await response.json();\n      \n      if (currentEquipement) {\n        setEquipements(prev => \n          prev.map(eq => eq.id === currentEquipement.id ? result : eq)\n        );\n        alert('Équipement modifié avec succès !');\n      } else {\n        setEquipements(prev => [...prev, result]);\n        alert('Équipement ajouté avec succès !');\n      }\n\n      resetForm();\n    } catch (err) {\n      console.error(\"Erreur:\", err);\n      alert(`Erreur: ${err.message}`);\n    } finally {\n      setSubmitting(false);\n    }\n  };\n\n  const handleEdit = (equipement) => {\n    setCurrentEquipement(equipement);\n    setFormData({\n      nom: equipement.nom,\n      type: equipement.type,\n      numero_serie: equipement.numero_serie,\n      statut: equipement.statut,\n      date_achat: equipement.date_achat?.split('T')[0] || '',\n      fournisseur_id: equipement.fournisseur?.id || '',\n      date_debut_garantie: equipement.date_debut_garantie?.split('T')[0] || '',\n      date_fin_garantie: equipement.date_fin_garantie?.split('T')[0] || '',\n      en_stock: equipement.en_stock,\n      stock_actuel: equipement.stock_actuel,\n      stock_max: equipement.stock_max,\n      stock_min: equipement.stock_min,\n      categorie_id: equipement.categorie_id\n    });\n    setShowModal(true);\n  };\n\n  const handleDelete = async (id) => {\n    if (!window.confirm(\"Êtes-vous sûr de vouloir supprimer cet équipement ?\")) return;\n\n    try {\n      const token = localStorage.getItem('authToken');\n      const response = await fetch(`http://localhost:8081/api/equipements/${id}`, {\n        method: 'DELETE',\n        headers: {\n          'Content-Type': 'application/json',\n          ...(token && { 'Authorization': `Bearer ${token}` })\n        }\n      });\n\n      if (!response.ok) throw new Error(`Erreur HTTP: ${response.status}`);\n\n      setEquipements(prev => prev.filter(eq => eq.id !== id));\n      alert('Équipement supprimé avec succès !');\n    } catch (err) {\n      console.error(\"Erreur:\", err);\n      alert(`Erreur lors de la suppression: ${err.message}`);\n    }\n  };\n\n  const resetForm = () => {\n    setFormData({\n      nom: '',\n      type: '',\n      numero_serie: '',\n      statut: 'DISPONIBLE',\n      date_achat: '',\n      fournisseur_id: '',\n      date_debut_garantie: '',\n      date_fin_garantie: '',\n      en_stock: true,\n      stock_actuel: 1,\n      stock_max: 1,\n      stock_min: 1,\n      categorie_id: ''\n    });\n    setCurrentEquipement(null);\n    setShowModal(false);\n  };\n\n  const handleExportExcel = () => {\n    if (filteredEquipements.length === 0) {\n      alert(\"Aucune donnée à exporter\");\n      return;\n    }\n    \n    const worksheet = XLSX.utils.json_to_sheet(filteredEquipements.map(eq => ({\n      Nom: eq.nom,\n      Type: eq.type,\n      'Numéro de série': eq.numero_serie,\n      Statut: eq.statut,\n      'Date achat': eq.date_achat,\n      Fournisseur: eq.fournisseur?.nom\n    })));\n    \n    const workbook = XLSX.utils.book_new();\n    XLSX.utils.book_append_sheet(workbook, worksheet, \"Equipements\");\n    XLSX.writeFile(workbook, \"equipements.xlsx\");\n  };\n\n  const handleExportPDF = () => {\n    if (filteredEquipements.length === 0) {\n      alert(\"Aucune donnée à exporter\");\n      return;\n    }\n    \n    const doc = new jsPDF();\n    doc.text(\"Liste des équipements\", 14, 16);\n    doc.autoTable({\n      head: [['Nom', 'Type', 'Numéro série', 'Statut', 'Date achat', 'Fournisseur']],\n      body: filteredEquipements.map(eq => [\n        eq.nom || '',\n        eq.type || '',\n        eq.numero_serie || '',\n        eq.statut || '',\n        eq.date_achat || '',\n        eq.fournisseur?.nom || ''\n      ]),\n      startY: 22,\n      styles: { fontSize: 10 }\n    });\n    doc.save(\"equipements.pdf\");\n  };\n\n  const handlePrint = () => {\n    window.print();\n  };\n\n  return (\n    <div className=\"equipements-container\">\n      {/* Sidebar */}\n      <div className=\"sidebar\">\n        <div className=\"logo-section\">\n          <img src={logo} alt=\"Logo Entreprise\" className=\"company-logo\" />\n        </div>\n        <nav className=\"main-menu\">\n          <ul>\n            <li>\n              <Link to=\"/\" className={location.pathname === '/' ? 'home-active active' : ''}>\n                <FontAwesomeIcon icon={faLaptop} style={{marginRight:8}} /> Accueil\n              </Link>\n            </li>\n            <li>\n              <Link to=\"/equipements\" className={location.pathname === '/equipements' ? 'active' : ''}>\n                <FontAwesomeIcon icon={faLaptop} style={{marginRight:8}} /> Gérer les équipements\n              </Link>\n            </li>\n            <li>\n              <Link to=\"/employes\" className={location.pathname === '/employes' ? 'active' : ''}>\n                <FontAwesomeIcon icon={faUsers} style={{marginRight:8}} /> Gérer les employés\n              </Link>\n            </li>\n            <li>\n              <Link to=\"/affectations\" className={location.pathname === '/affectations' ? 'active' : ''}>\n                <FontAwesomeIcon icon={faExchangeAlt} style={{marginRight:8}} /> Suivi des affectations\n              </Link>\n            </li>\n            <li>\n              <Link to=\"/fournisseurs\" className={location.pathname === '/fournisseurs' ? 'active' : ''}>\n                <FontAwesomeIcon icon={faTruck} style={{marginRight:8}} /> Gérer les fournisseurs\n              </Link>\n            </li>\n          </ul>\n        </nav>\n        <div className=\"secondary-links\">\n          <Link to=\"/profil\">\n            <FontAwesomeIcon icon={faUserCog} style={{marginRight:8}} /> Mon profil & paramètres\n          </Link>\n        </div>\n        <div style={{ marginTop: 'auto', padding: '20px 0 0 0', textAlign: 'center' }}>\n          <button\n            className=\"logout-btn\"\n            onClick={() => {\n              localStorage.removeItem('authToken');\n              window.location.href = '/';\n            }}\n          >\n            <FontAwesomeIcon icon={faSignOutAlt} style={{marginRight:8}} /> Déconnexion\n          </button>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"equipements-content\">\n        <div className=\"dashboard-header-row\">\n          <div className=\"search-bar\">\n            <FontAwesomeIcon icon={faSearch} className=\"search-icon\" />\n            <input\n              type=\"text\"\n              placeholder=\"Recherche par mot clé\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n            />\n          </div>\n          <div className=\"profile-block\">\n            <div className=\"notification-icon\">\n              <FontAwesomeIcon icon={faBell} />\n              <span>3</span>\n            </div>\n            <span>Responsable IT</span>\n            <FontAwesomeIcon icon={faUserCog} />\n          </div>\n        </div>\n\n        <h2 className=\"equipements-title\">\n          Liste des équipements ({filteredEquipements.length})\n        </h2>\n        \n        <div className=\"action-buttons\">\n          <button className=\"btn btn-primary\" onClick={() => setShowModal(true)}>\n            <FontAwesomeIcon icon={faPlus} style={{marginRight: 8}} />\n            Ajouter un équipement\n          </button>\n          <button className=\"btn btn-primary\" onClick={handleExportExcel}>\n            Exporter Excel\n          </button>\n          <button className=\"btn btn-primary\" onClick={handleExportPDF}>\n            Exporter PDF\n          </button>\n          <button className=\"btn btn-primary\" onClick={handlePrint}>\n            Imprimer\n          </button>\n        </div>\n\n        {/* Modal pour ajout/modification */}\n        {showModal && (\n          <div className=\"modal-overlay\">\n            <div className=\"modal-content\">\n              <div className=\"modal-header\">\n                <h3>\n                  {currentEquipement ? 'Modifier équipement' : 'Ajouter un équipement'}\n                </h3>\n                <button onClick={resetForm}>\n                  <FontAwesomeIcon icon={faTimes} />\n                </button>\n              </div>\n\n              <form onSubmit={handleSubmit}>\n                <div className=\"form-grid\">\n                  <div className=\"form-group\">\n                    <label>Nom de l'équipement *</label>\n                    <input\n                      type=\"text\"\n                      name=\"nom\"\n                      value={formData.nom}\n                      onChange={handleInputChange}\n                      required\n                    />\n                  </div>\n\n                  <div className=\"form-group\">\n                    <label>Type *</label>\n                    <select\n                      name=\"type\"\n                      value={formData.type}\n                      onChange={handleInputChange}\n                      required\n                    >\n                      <option value=\"\">Sélectionner un type</option>\n                      {typeOptions.map(type => (\n                        <option key={type} value={type}>{type}</option>\n                      ))}\n                    </select>\n                  </div>\n\n                  <div className=\"form-group\">\n                    <label>Numéro de série</label>\n                    <input\n                      type=\"text\"\n                      name=\"numero_serie\"\n                      value={formData.numero_serie}\n                      onChange={handleInputChange}\n                    />\n                  </div>\n\n                  <div className=\"form-group\">\n                    <label>Statut *</label>\n                    <select\n                      name=\"statut\"\n                      value={formData.statut}\n                      onChange={handleInputChange}\n                      required\n                    >\n                      {statutOptions.map(statut => (\n                        <option key={statut} value={statut}>{statut}</option>\n                      ))}\n                    </select>\n                  </div>\n\n                  <div className=\"form-group\">\n                    <label>Date d'achat</label>\n                    <input\n                      type=\"date\"\n                      name=\"date_achat\"\n                      value={formData.date_achat}\n                      onChange={handleInputChange}\n                    />\n                  </div>\n\n                  <div className=\"form-group\">\n                    <label>Fournisseur</label>\n                    <select\n                      name=\"fournisseur_id\"\n                      value={formData.fournisseur_id}\n                      onChange={handleInputChange}\n                    >\n                      <option value=\"\">Sélectionner un fournisseur</option>\n                      {fournisseurs.map(fournisseur => (\n                        <option key={fournisseur.id} value={fournisseur.id}>\n                          {fournisseur.nom}\n                        </option>\n                      ))}\n                    </select>\n                  </div>\n\n                  <div className=\"form-group\">\n                    <label>Début de garantie</label>\n                    <input\n                      type=\"date\"\n                      name=\"date_debut_garantie\"\n                      value={formData.date_debut_garantie}\n                      onChange={handleInputChange}\n                    />\n                  </div>\n\n                  <div className=\"form-group\">\n                    <label>Fin de garantie</label>\n                    <input\n                      type=\"date\"\n                      name=\"date_fin_garantie\"\n                      value={formData.date_fin_garantie}\n                      onChange={handleInputChange}\n                    />\n                  </div>\n\n                  <div className=\"form-group\">\n                    <label>Stock actuel</label>\n                    <input\n                      type=\"number\"\n                      name=\"stock_actuel\"\n                      value={formData.stock_actuel}\n                      onChange={handleInputChange}\n                      min=\"0\"\n                    />\n                  </div>\n\n                  <div className=\"form-group\">\n                    <label>Stock maximum</label>\n                    <input\n                      type=\"number\"\n                      name=\"stock_max\"\n                      value={formData.stock_max}\n                      onChange={handleInputChange}\n                      min=\"1\"\n                    />\n                  </div>\n\n                  <div className=\"form-group\">\n                    <label>Stock minimum</label>\n                    <input\n                      type=\"number\"\n                      name=\"stock_min\"\n                      value={formData.stock_min}\n                      onChange={handleInputChange}\n                      min=\"0\"\n                    />\n                  </div>\n                </div>\n\n                <div className=\"form-checkbox\">\n                  <label>\n                    <input\n                      type=\"checkbox\"\n                      name=\"en_stock\"\n                      checked={formData.en_stock}\n                      onChange={handleInputChange}\n                    />\n                    Équipement en stock\n                  </label>\n                </div>\n\n                <div className=\"form-actions\">\n                  <button type=\"button\" onClick={resetForm}>\n                    Annuler\n                  </button>\n                  <button type=\"submit\" disabled={submitting}>\n                    <FontAwesomeIcon icon={currentEquipement ? faSave : faPlus} style={{marginRight: 8}} />\n                    {submitting ? 'En cours...' : currentEquipement ? 'Enregistrer' : 'Ajouter'}\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        )}\n\n        {/* Contenu principal */}\n        {loading ? (\n          <div className=\"loading-message\">\n            <p>Chargement des équipements...</p>\n          </div>\n        ) : error ? (\n          <div className=\"error-message\">\n            <p>Erreur lors du chargement : {error}</p>\n            <button onClick={() => window.location.reload()}>\n              Actualiser la page\n            </button>\n          </div>\n        ) : filteredEquipements.length === 0 ? (\n          <div className=\"empty-message\">\n            <p>{searchTerm ? 'Aucun résultat trouvé' : 'Aucun équipement trouvé'}</p>\n          </div>\n        ) : (\n          <table className=\"equipements-table\">\n            <thead>\n              <tr>\n                <th>Nom</th>\n                <th>Type</th>\n                <th>Numéro de série</th>\n                <th>Statut</th>\n                <th>Date d'achat</th>\n                <th>Fournisseur</th>\n                <th>Actions</th>\n              </tr>\n            </thead>\n            <tbody>\n              {filteredEquipements.map((eq) => (\n                <tr key={eq.id}>\n                  <td>{eq.nom || 'N/A'}</td>\n                  <td>{eq.type || 'N/A'}</td>\n                  <td>{eq.numero_serie || 'N/A'}</td>\n                  <td>{eq.statut || 'N/A'}</td>\n                  <td>{eq.date_achat?.split('T')[0] || 'N/A'}</td>\n                  <td>{eq.fournisseur?.nom || 'Non spécifié'}</td>\n                  <td className=\"actions-cell\">\n                    <button \n                      onClick={() => handleEdit(eq)}\n                      className=\"action-btn edit-btn\"\n                      title=\"Modifier\"\n                    >\n                      <FontAwesomeIcon icon={faEdit} />\n                    </button>\n                    <button \n                      onClick={() => handleDelete(eq.id)}\n                      className=\"action-btn delete-btn\"\n                      title=\"Supprimer\"\n                    >\n                      <FontAwesomeIcon icon={faTrash} />\n                    </button>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        )}\n      </div>\n    </div>\n  );\n}"], "mappings": ";;AAAA,OAAO,mBAAmB;AAC1B,SAASA,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,OAAOC,IAAI,MAAM,kCAAkC;AACnD,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SACEC,QAAQ,EACRC,OAAO,EACPC,aAAa,EACbC,OAAO,EACPC,SAAS,EACTC,YAAY,EACZC,QAAQ,EACRC,MAAM,EACNC,OAAO,EACPC,MAAM,EACNC,MAAM,EACNC,OAAO,EACPC,MAAM,QACD,mCAAmC;AAC1C,SAASC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,iBAAiB;AACxB,OAAO,KAAKC,IAAI,MAAM,MAAM;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,eAAe,SAASC,WAAWA,CAAA,EAAG;EAAAC,EAAA;EACpC,MAAMC,QAAQ,GAAGxB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACyB,WAAW,EAAEC,cAAc,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACU,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAACY,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACc,KAAK,EAAEC,QAAQ,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACgB,SAAS,EAAEC,YAAY,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACkB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACoB,YAAY,EAAEC,eAAe,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACsB,UAAU,EAAEC,aAAa,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACwB,UAAU,EAAEC,aAAa,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC0B,UAAU,EAAEC,aAAa,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;;EAEhD;EACA,MAAM,CAAC4B,QAAQ,EAAEC,WAAW,CAAC,GAAG7B,QAAQ,CAAC;IACvC8B,GAAG,EAAE,EAAE;IACPC,IAAI,EAAE,EAAE;IACRC,YAAY,EAAE,EAAE;IAChBC,MAAM,EAAE,YAAY;IACpBC,UAAU,EAAE,EAAE;IACdC,cAAc,EAAE,EAAE;IAClBC,mBAAmB,EAAE,EAAE;IACvBC,iBAAiB,EAAE,EAAE;IACrBC,QAAQ,EAAE,IAAI;IACdC,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE,CAAC;IACZC,SAAS,EAAE,CAAC;IACZC,YAAY,EAAE;EAChB,CAAC,CAAC;;EAEF;EACA,MAAMC,aAAa,GAAG,CAAC,YAAY,EAAE,gBAAgB,EAAE,QAAQ,EAAE,cAAc,CAAC;EAChF,MAAMC,WAAW,GAAG,CAAC,YAAY,EAAE,YAAY,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,SAAS,EAAE,OAAO,CAAC;EAExG7C,SAAS,CAAC,MAAM;IACd8C,eAAe,CAAC,CAAC;IACjBC,gBAAgB,CAAC,CAAC;IAClBC,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAENhD,SAAS,CAAC,MAAM;IACd,IAAI2B,UAAU,KAAK,EAAE,EAAE;MACrBf,sBAAsB,CAACH,WAAW,CAAC;IACrC,CAAC,MAAM;MACL,MAAMwC,QAAQ,GAAGxC,WAAW,CAACyC,MAAM,CAACC,UAAU;QAAA,IAAAC,qBAAA;QAAA,OAC5CD,UAAU,CAACpB,GAAG,CAACsB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC3B,UAAU,CAAC0B,WAAW,CAAC,CAAC,CAAC,IAC/DF,UAAU,CAACnB,IAAI,CAACqB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC3B,UAAU,CAAC0B,WAAW,CAAC,CAAC,CAAC,IAC/DF,UAAU,CAAClB,YAAY,IAAIkB,UAAU,CAAClB,YAAY,CAACoB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC3B,UAAU,CAAC0B,WAAW,CAAC,CAAC,CAAE,IACrGF,UAAU,CAACjB,MAAM,CAACmB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC3B,UAAU,CAAC0B,WAAW,CAAC,CAAC,CAAC,IACjE,EAAAD,qBAAA,GAAAD,UAAU,CAACI,WAAW,cAAAH,qBAAA,uBAAtBA,qBAAA,CAAwBrB,GAAG,KAAIoB,UAAU,CAACI,WAAW,CAACxB,GAAG,CAACsB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC3B,UAAU,CAAC0B,WAAW,CAAC,CAAC,CAAE;MAAA,CAC9G,CAAC;MACDzC,sBAAsB,CAACqC,QAAQ,CAAC;IAClC;EACF,CAAC,EAAE,CAACtB,UAAU,EAAElB,WAAW,CAAC,CAAC;EAE7B,MAAMqC,eAAe,GAAGA,CAAA,KAAM;IAC5BhC,UAAU,CAAC,IAAI,CAAC;IAChB,MAAM0C,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IAC/C,MAAMC,OAAO,GAAG;MACd,cAAc,EAAE,kBAAkB;MAClC,IAAIH,KAAK,IAAI;QAAE,eAAe,EAAE,UAAUA,KAAK;MAAG,CAAC;IACrD,CAAC;IAEDI,KAAK,CAAC,uCAAuC,EAAE;MAAEC,MAAM,EAAE,KAAK;MAAEF;IAAQ,CAAC,CAAC,CACvEG,IAAI,CAACC,GAAG,IAAI;MACX,IAAI,CAACA,GAAG,CAACC,EAAE,EAAE,MAAM,IAAIC,KAAK,CAAC,gBAAgBF,GAAG,CAACG,MAAM,EAAE,CAAC;MAC1D,OAAOH,GAAG,CAACI,IAAI,CAAC,CAAC;IACnB,CAAC,CAAC,CACDL,IAAI,CAACM,IAAI,IAAI;MACZ,IAAIC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,EAAE;QACvB1D,cAAc,CAAC0D,IAAI,CAAC;QACpBxD,sBAAsB,CAACwD,IAAI,CAAC;QAC5BpD,QAAQ,CAAC,IAAI,CAAC;MAChB,CAAC,MAAM;QACL,MAAM,IAAIiD,KAAK,CAAC,6BAA6B,CAAC;MAChD;IACF,CAAC,CAAC,CACDM,KAAK,CAACC,GAAG,IAAI;MACZxD,QAAQ,CAACwD,GAAG,CAACC,OAAO,CAAC;MACrB/D,cAAc,CAAC,EAAE,CAAC;MAClBE,sBAAsB,CAAC,EAAE,CAAC;IAC5B,CAAC,CAAC,CACD8D,OAAO,CAAC,MAAM5D,UAAU,CAAC,KAAK,CAAC,CAAC;EACrC,CAAC;EAED,MAAMiC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMS,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IAC/CE,KAAK,CAAC,wCAAwC,EAAE;MAC9CD,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClC,IAAIH,KAAK,IAAI;UAAE,eAAe,EAAE,UAAUA,KAAK;QAAG,CAAC;MACrD;IACF,CAAC,CAAC,CACCM,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,EAAE,GAAGD,GAAG,CAACI,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,CACrCL,IAAI,CAACM,IAAI,IAAI9C,eAAe,CAAC+C,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,GAAGA,IAAI,GAAG,EAAE,CAAC,CAAC,CAC9DG,KAAK,CAACC,GAAG,IAAI;MACZG,OAAO,CAAC5D,KAAK,CAAC,sBAAsB,EAAEyD,GAAG,CAAC;MAC1ClD,eAAe,CAAC,EAAE,CAAC;IACrB,CAAC,CAAC;EACN,CAAC;EAED,MAAM0B,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMQ,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IAC/CE,KAAK,CAAC,sCAAsC,EAAE;MAC5CD,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClC,IAAIH,KAAK,IAAI;UAAE,eAAe,EAAE,UAAUA,KAAK;QAAG,CAAC;MACrD;IACF,CAAC,CAAC,CACCM,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,EAAE,GAAGD,GAAG,CAACI,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,CACrCL,IAAI,CAACM,IAAI,IAAI5C,aAAa,CAAC6C,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,GAAGA,IAAI,GAAG,EAAE,CAAC,CAAC,CAC5DG,KAAK,CAACC,GAAG,IAAI;MACZG,OAAO,CAAC5D,KAAK,CAAC,oBAAoB,EAAEyD,GAAG,CAAC;MACxChD,aAAa,CAAC,EAAE,CAAC;IACnB,CAAC,CAAC;EACN,CAAC;EAED,MAAMoD,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC,KAAK;MAAE/C,IAAI;MAAEgD;IAAQ,CAAC,GAAGH,CAAC,CAACI,MAAM;IAC/CnD,WAAW,CAACoD,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACJ,IAAI,GAAG9C,IAAI,KAAK,UAAU,GAAGgD,OAAO,GAAGD;IAC1C,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMI,YAAY,GAAG,MAAON,CAAC,IAAK;IAChCA,CAAC,CAACO,cAAc,CAAC,CAAC;IAClB1D,aAAa,CAAC,IAAI,CAAC;IAEnB,IAAI;MACF,MAAM8B,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;MAC/C,MAAMG,MAAM,GAAG1C,iBAAiB,GAAG,KAAK,GAAG,MAAM;MACjD,MAAMkE,GAAG,GAAGlE,iBAAiB,GACzB,yCAAyCA,iBAAiB,CAACmE,EAAE,EAAE,GAC/D,uCAAuC;MAE3C,MAAMC,QAAQ,GAAG,MAAM3B,KAAK,CAACyB,GAAG,EAAE;QAChCxB,MAAM;QACNF,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,IAAIH,KAAK,IAAI;YAAE,eAAe,EAAE,UAAUA,KAAK;UAAG,CAAC;QACrD,CAAC;QACDgC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnB,GAAG7D,QAAQ;UACXO,cAAc,EAAEP,QAAQ,CAACO,cAAc,IAAI;QAC7C,CAAC;MACH,CAAC,CAAC;MAEF,IAAI,CAACmD,QAAQ,CAACvB,EAAE,EAAE,MAAM,IAAIC,KAAK,CAAC,gBAAgBsB,QAAQ,CAACrB,MAAM,EAAE,CAAC;MAEpE,MAAMyB,MAAM,GAAG,MAAMJ,QAAQ,CAACpB,IAAI,CAAC,CAAC;MAEpC,IAAIhD,iBAAiB,EAAE;QACrBT,cAAc,CAACwE,IAAI,IACjBA,IAAI,CAACU,GAAG,CAACC,EAAE,IAAIA,EAAE,CAACP,EAAE,KAAKnE,iBAAiB,CAACmE,EAAE,GAAGK,MAAM,GAAGE,EAAE,CAC7D,CAAC;QACDC,KAAK,CAAC,kCAAkC,CAAC;MAC3C,CAAC,MAAM;QACLpF,cAAc,CAACwE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAES,MAAM,CAAC,CAAC;QACzCG,KAAK,CAAC,iCAAiC,CAAC;MAC1C;MAEAC,SAAS,CAAC,CAAC;IACb,CAAC,CAAC,OAAOvB,GAAG,EAAE;MACZG,OAAO,CAAC5D,KAAK,CAAC,SAAS,EAAEyD,GAAG,CAAC;MAC7BsB,KAAK,CAAC,WAAWtB,GAAG,CAACC,OAAO,EAAE,CAAC;IACjC,CAAC,SAAS;MACR/C,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMsE,UAAU,GAAI7C,UAAU,IAAK;IAAA,IAAA8C,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA;IACjChF,oBAAoB,CAAC+B,UAAU,CAAC;IAChCrB,WAAW,CAAC;MACVC,GAAG,EAAEoB,UAAU,CAACpB,GAAG;MACnBC,IAAI,EAAEmB,UAAU,CAACnB,IAAI;MACrBC,YAAY,EAAEkB,UAAU,CAAClB,YAAY;MACrCC,MAAM,EAAEiB,UAAU,CAACjB,MAAM;MACzBC,UAAU,EAAE,EAAA8D,qBAAA,GAAA9C,UAAU,CAAChB,UAAU,cAAA8D,qBAAA,uBAArBA,qBAAA,CAAuBI,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAI,EAAE;MACtDjE,cAAc,EAAE,EAAA8D,sBAAA,GAAA/C,UAAU,CAACI,WAAW,cAAA2C,sBAAA,uBAAtBA,sBAAA,CAAwBZ,EAAE,KAAI,EAAE;MAChDjD,mBAAmB,EAAE,EAAA8D,qBAAA,GAAAhD,UAAU,CAACd,mBAAmB,cAAA8D,qBAAA,uBAA9BA,qBAAA,CAAgCE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAI,EAAE;MACxE/D,iBAAiB,EAAE,EAAA8D,qBAAA,GAAAjD,UAAU,CAACb,iBAAiB,cAAA8D,qBAAA,uBAA5BA,qBAAA,CAA8BC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAI,EAAE;MACpE9D,QAAQ,EAAEY,UAAU,CAACZ,QAAQ;MAC7BC,YAAY,EAAEW,UAAU,CAACX,YAAY;MACrCC,SAAS,EAAEU,UAAU,CAACV,SAAS;MAC/BC,SAAS,EAAES,UAAU,CAACT,SAAS;MAC/BC,YAAY,EAAEQ,UAAU,CAACR;IAC3B,CAAC,CAAC;IACFzB,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMoF,YAAY,GAAG,MAAOhB,EAAE,IAAK;IACjC,IAAI,CAACiB,MAAM,CAACC,OAAO,CAAC,qDAAqD,CAAC,EAAE;IAE5E,IAAI;MACF,MAAMhD,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;MAC/C,MAAM6B,QAAQ,GAAG,MAAM3B,KAAK,CAAC,yCAAyC0B,EAAE,EAAE,EAAE;QAC1EzB,MAAM,EAAE,QAAQ;QAChBF,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,IAAIH,KAAK,IAAI;YAAE,eAAe,EAAE,UAAUA,KAAK;UAAG,CAAC;QACrD;MACF,CAAC,CAAC;MAEF,IAAI,CAAC+B,QAAQ,CAACvB,EAAE,EAAE,MAAM,IAAIC,KAAK,CAAC,gBAAgBsB,QAAQ,CAACrB,MAAM,EAAE,CAAC;MAEpExD,cAAc,CAACwE,IAAI,IAAIA,IAAI,CAAChC,MAAM,CAAC2C,EAAE,IAAIA,EAAE,CAACP,EAAE,KAAKA,EAAE,CAAC,CAAC;MACvDQ,KAAK,CAAC,mCAAmC,CAAC;IAC5C,CAAC,CAAC,OAAOtB,GAAG,EAAE;MACZG,OAAO,CAAC5D,KAAK,CAAC,SAAS,EAAEyD,GAAG,CAAC;MAC7BsB,KAAK,CAAC,kCAAkCtB,GAAG,CAACC,OAAO,EAAE,CAAC;IACxD;EACF,CAAC;EAED,MAAMsB,SAAS,GAAGA,CAAA,KAAM;IACtBjE,WAAW,CAAC;MACVC,GAAG,EAAE,EAAE;MACPC,IAAI,EAAE,EAAE;MACRC,YAAY,EAAE,EAAE;MAChBC,MAAM,EAAE,YAAY;MACpBC,UAAU,EAAE,EAAE;MACdC,cAAc,EAAE,EAAE;MAClBC,mBAAmB,EAAE,EAAE;MACvBC,iBAAiB,EAAE,EAAE;MACrBC,QAAQ,EAAE,IAAI;MACdC,YAAY,EAAE,CAAC;MACfC,SAAS,EAAE,CAAC;MACZC,SAAS,EAAE,CAAC;MACZC,YAAY,EAAE;IAChB,CAAC,CAAC;IACFvB,oBAAoB,CAAC,IAAI,CAAC;IAC1BF,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,MAAMuF,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI9F,mBAAmB,CAAC+F,MAAM,KAAK,CAAC,EAAE;MACpCZ,KAAK,CAAC,0BAA0B,CAAC;MACjC;IACF;IAEA,MAAMa,SAAS,GAAGxG,IAAI,CAACyG,KAAK,CAACC,aAAa,CAAClG,mBAAmB,CAACiF,GAAG,CAACC,EAAE;MAAA,IAAAiB,eAAA;MAAA,OAAK;QACxEC,GAAG,EAAElB,EAAE,CAAC9D,GAAG;QACXiF,IAAI,EAAEnB,EAAE,CAAC7D,IAAI;QACb,iBAAiB,EAAE6D,EAAE,CAAC5D,YAAY;QAClCgF,MAAM,EAAEpB,EAAE,CAAC3D,MAAM;QACjB,YAAY,EAAE2D,EAAE,CAAC1D,UAAU;QAC3B+E,WAAW,GAAAJ,eAAA,GAAEjB,EAAE,CAACtC,WAAW,cAAAuD,eAAA,uBAAdA,eAAA,CAAgB/E;MAC/B,CAAC;IAAA,CAAC,CAAC,CAAC;IAEJ,MAAMoF,QAAQ,GAAGhH,IAAI,CAACyG,KAAK,CAACQ,QAAQ,CAAC,CAAC;IACtCjH,IAAI,CAACyG,KAAK,CAACS,iBAAiB,CAACF,QAAQ,EAAER,SAAS,EAAE,aAAa,CAAC;IAChExG,IAAI,CAACmH,SAAS,CAACH,QAAQ,EAAE,kBAAkB,CAAC;EAC9C,CAAC;EAED,MAAMI,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI5G,mBAAmB,CAAC+F,MAAM,KAAK,CAAC,EAAE;MACpCZ,KAAK,CAAC,0BAA0B,CAAC;MACjC;IACF;IAEA,MAAM0B,GAAG,GAAG,IAAItH,KAAK,CAAC,CAAC;IACvBsH,GAAG,CAACC,IAAI,CAAC,uBAAuB,EAAE,EAAE,EAAE,EAAE,CAAC;IACzCD,GAAG,CAACE,SAAS,CAAC;MACZC,IAAI,EAAE,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE,cAAc,EAAE,QAAQ,EAAE,YAAY,EAAE,aAAa,CAAC,CAAC;MAC9EnC,IAAI,EAAE7E,mBAAmB,CAACiF,GAAG,CAACC,EAAE;QAAA,IAAA+B,gBAAA;QAAA,OAAI,CAClC/B,EAAE,CAAC9D,GAAG,IAAI,EAAE,EACZ8D,EAAE,CAAC7D,IAAI,IAAI,EAAE,EACb6D,EAAE,CAAC5D,YAAY,IAAI,EAAE,EACrB4D,EAAE,CAAC3D,MAAM,IAAI,EAAE,EACf2D,EAAE,CAAC1D,UAAU,IAAI,EAAE,EACnB,EAAAyF,gBAAA,GAAA/B,EAAE,CAACtC,WAAW,cAAAqE,gBAAA,uBAAdA,gBAAA,CAAgB7F,GAAG,KAAI,EAAE,CAC1B;MAAA,EAAC;MACF8F,MAAM,EAAE,EAAE;MACVC,MAAM,EAAE;QAAEC,QAAQ,EAAE;MAAG;IACzB,CAAC,CAAC;IACFP,GAAG,CAACQ,IAAI,CAAC,iBAAiB,CAAC;EAC7B,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxB1B,MAAM,CAAC2B,KAAK,CAAC,CAAC;EAChB,CAAC;EAED,oBACE7H,OAAA;IAAK8H,SAAS,EAAC,uBAAuB;IAAAC,QAAA,gBAEpC/H,OAAA;MAAK8H,SAAS,EAAC,SAAS;MAAAC,QAAA,gBACtB/H,OAAA;QAAK8H,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3B/H,OAAA;UAAKgI,GAAG,EAAEpJ,IAAK;UAACqJ,GAAG,EAAC,iBAAiB;UAACH,SAAS,EAAC;QAAc;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC,eACNrI,OAAA;QAAK8H,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxB/H,OAAA;UAAA+H,QAAA,gBACE/H,OAAA;YAAA+H,QAAA,eACE/H,OAAA,CAACtB,IAAI;cAAC4J,EAAE,EAAC,GAAG;cAACR,SAAS,EAAE3H,QAAQ,CAACoI,QAAQ,KAAK,GAAG,GAAG,oBAAoB,GAAG,EAAG;cAAAR,QAAA,gBAC5E/H,OAAA,CAACnB,eAAe;gBAAC2J,IAAI,EAAE1J,QAAS;gBAAC2J,KAAK,EAAE;kBAACC,WAAW,EAAC;gBAAC;cAAE;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,YAC7D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACLrI,OAAA;YAAA+H,QAAA,eACE/H,OAAA,CAACtB,IAAI;cAAC4J,EAAE,EAAC,cAAc;cAACR,SAAS,EAAE3H,QAAQ,CAACoI,QAAQ,KAAK,cAAc,GAAG,QAAQ,GAAG,EAAG;cAAAR,QAAA,gBACtF/H,OAAA,CAACnB,eAAe;gBAAC2J,IAAI,EAAE1J,QAAS;gBAAC2J,KAAK,EAAE;kBAACC,WAAW,EAAC;gBAAC;cAAE;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gCAC7D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACLrI,OAAA;YAAA+H,QAAA,eACE/H,OAAA,CAACtB,IAAI;cAAC4J,EAAE,EAAC,WAAW;cAACR,SAAS,EAAE3H,QAAQ,CAACoI,QAAQ,KAAK,WAAW,GAAG,QAAQ,GAAG,EAAG;cAAAR,QAAA,gBAChF/H,OAAA,CAACnB,eAAe;gBAAC2J,IAAI,EAAEzJ,OAAQ;gBAAC0J,KAAK,EAAE;kBAACC,WAAW,EAAC;gBAAC;cAAE;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,6BAC5D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACLrI,OAAA;YAAA+H,QAAA,eACE/H,OAAA,CAACtB,IAAI;cAAC4J,EAAE,EAAC,eAAe;cAACR,SAAS,EAAE3H,QAAQ,CAACoI,QAAQ,KAAK,eAAe,GAAG,QAAQ,GAAG,EAAG;cAAAR,QAAA,gBACxF/H,OAAA,CAACnB,eAAe;gBAAC2J,IAAI,EAAExJ,aAAc;gBAACyJ,KAAK,EAAE;kBAACC,WAAW,EAAC;gBAAC;cAAE;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,2BAClE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACLrI,OAAA;YAAA+H,QAAA,eACE/H,OAAA,CAACtB,IAAI;cAAC4J,EAAE,EAAC,eAAe;cAACR,SAAS,EAAE3H,QAAQ,CAACoI,QAAQ,KAAK,eAAe,GAAG,QAAQ,GAAG,EAAG;cAAAR,QAAA,gBACxF/H,OAAA,CAACnB,eAAe;gBAAC2J,IAAI,EAAEvJ,OAAQ;gBAACwJ,KAAK,EAAE;kBAACC,WAAW,EAAC;gBAAC;cAAE;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,8BAC5D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACNrI,OAAA;QAAK8H,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9B/H,OAAA,CAACtB,IAAI;UAAC4J,EAAE,EAAC,SAAS;UAAAP,QAAA,gBAChB/H,OAAA,CAACnB,eAAe;YAAC2J,IAAI,EAAEtJ,SAAU;YAACuJ,KAAK,EAAE;cAACC,WAAW,EAAC;YAAC;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,+BAC9D;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNrI,OAAA;QAAKyI,KAAK,EAAE;UAAEE,SAAS,EAAE,MAAM;UAAEC,OAAO,EAAE,YAAY;UAAEC,SAAS,EAAE;QAAS,CAAE;QAAAd,QAAA,eAC5E/H,OAAA;UACE8H,SAAS,EAAC,YAAY;UACtBgB,OAAO,EAAEA,CAAA,KAAM;YACb1F,YAAY,CAAC2F,UAAU,CAAC,WAAW,CAAC;YACpC7C,MAAM,CAAC/F,QAAQ,CAAC6I,IAAI,GAAG,GAAG;UAC5B,CAAE;UAAAjB,QAAA,gBAEF/H,OAAA,CAACnB,eAAe;YAAC2J,IAAI,EAAErJ,YAAa;YAACsJ,KAAK,EAAE;cAACC,WAAW,EAAC;YAAC;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,mBACjE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrI,OAAA;MAAK8H,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClC/H,OAAA;QAAK8H,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACnC/H,OAAA;UAAK8H,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB/H,OAAA,CAACnB,eAAe;YAAC2J,IAAI,EAAEpJ,QAAS;YAAC0I,SAAS,EAAC;UAAa;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3DrI,OAAA;YACE2B,IAAI,EAAC,MAAM;YACXsH,WAAW,EAAC,0BAAuB;YACnCvE,KAAK,EAAEpD,UAAW;YAClB4H,QAAQ,EAAG1E,CAAC,IAAKjD,aAAa,CAACiD,CAAC,CAACI,MAAM,CAACF,KAAK;UAAE;YAAAwD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNrI,OAAA;UAAK8H,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B/H,OAAA;YAAK8H,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChC/H,OAAA,CAACnB,eAAe;cAAC2J,IAAI,EAAEnJ;YAAO;cAAA6I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjCrI,OAAA;cAAA+H,QAAA,EAAM;YAAC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eACNrI,OAAA;YAAA+H,QAAA,EAAM;UAAc;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3BrI,OAAA,CAACnB,eAAe;YAAC2J,IAAI,EAAEtJ;UAAU;YAAAgJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENrI,OAAA;QAAI8H,SAAS,EAAC,mBAAmB;QAAAC,QAAA,GAAC,4BACT,EAACzH,mBAAmB,CAAC+F,MAAM,EAAC,GACrD;MAAA;QAAA6B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELrI,OAAA;QAAK8H,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B/H,OAAA;UAAQ8H,SAAS,EAAC,iBAAiB;UAACgB,OAAO,EAAEA,CAAA,KAAMjI,YAAY,CAAC,IAAI,CAAE;UAAAkH,QAAA,gBACpE/H,OAAA,CAACnB,eAAe;YAAC2J,IAAI,EAAEjJ,MAAO;YAACkJ,KAAK,EAAE;cAACC,WAAW,EAAE;YAAC;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,4BAE5D;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTrI,OAAA;UAAQ8H,SAAS,EAAC,iBAAiB;UAACgB,OAAO,EAAE1C,iBAAkB;UAAA2B,QAAA,EAAC;QAEhE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTrI,OAAA;UAAQ8H,SAAS,EAAC,iBAAiB;UAACgB,OAAO,EAAE5B,eAAgB;UAAAa,QAAA,EAAC;QAE9D;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTrI,OAAA;UAAQ8H,SAAS,EAAC,iBAAiB;UAACgB,OAAO,EAAElB,WAAY;UAAAG,QAAA,EAAC;QAE1D;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGLzH,SAAS,iBACRZ,OAAA;QAAK8H,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC5B/H,OAAA;UAAK8H,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B/H,OAAA;YAAK8H,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B/H,OAAA;cAAA+H,QAAA,EACGjH,iBAAiB,GAAG,qBAAqB,GAAG;YAAuB;cAAAoH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CAAC,eACLrI,OAAA;cAAQ8I,OAAO,EAAEpD,SAAU;cAAAqC,QAAA,eACzB/H,OAAA,CAACnB,eAAe;gBAAC2J,IAAI,EAAElJ;cAAQ;gBAAA4I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENrI,OAAA;YAAMmJ,QAAQ,EAAErE,YAAa;YAAAiD,QAAA,gBAC3B/H,OAAA;cAAK8H,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB/H,OAAA;gBAAK8H,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB/H,OAAA;kBAAA+H,QAAA,EAAO;gBAAqB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACpCrI,OAAA;kBACE2B,IAAI,EAAC,MAAM;kBACX8C,IAAI,EAAC,KAAK;kBACVC,KAAK,EAAElD,QAAQ,CAACE,GAAI;kBACpBwH,QAAQ,EAAE3E,iBAAkB;kBAC5B6E,QAAQ;gBAAA;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENrI,OAAA;gBAAK8H,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB/H,OAAA;kBAAA+H,QAAA,EAAO;gBAAM;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrBrI,OAAA;kBACEyE,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAElD,QAAQ,CAACG,IAAK;kBACrBuH,QAAQ,EAAE3E,iBAAkB;kBAC5B6E,QAAQ;kBAAArB,QAAA,gBAER/H,OAAA;oBAAQ0E,KAAK,EAAC,EAAE;oBAAAqD,QAAA,EAAC;kBAAoB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EAC7C7F,WAAW,CAAC+C,GAAG,CAAC5D,IAAI,iBACnB3B,OAAA;oBAAmB0E,KAAK,EAAE/C,IAAK;oBAAAoG,QAAA,EAAEpG;kBAAI,GAAxBA,IAAI;oBAAAuG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAA6B,CAC/C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAENrI,OAAA;gBAAK8H,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB/H,OAAA;kBAAA+H,QAAA,EAAO;gBAAe;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9BrI,OAAA;kBACE2B,IAAI,EAAC,MAAM;kBACX8C,IAAI,EAAC,cAAc;kBACnBC,KAAK,EAAElD,QAAQ,CAACI,YAAa;kBAC7BsH,QAAQ,EAAE3E;gBAAkB;kBAAA2D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENrI,OAAA;gBAAK8H,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB/H,OAAA;kBAAA+H,QAAA,EAAO;gBAAQ;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACvBrI,OAAA;kBACEyE,IAAI,EAAC,QAAQ;kBACbC,KAAK,EAAElD,QAAQ,CAACK,MAAO;kBACvBqH,QAAQ,EAAE3E,iBAAkB;kBAC5B6E,QAAQ;kBAAArB,QAAA,EAEPxF,aAAa,CAACgD,GAAG,CAAC1D,MAAM,iBACvB7B,OAAA;oBAAqB0E,KAAK,EAAE7C,MAAO;oBAAAkG,QAAA,EAAElG;kBAAM,GAA9BA,MAAM;oBAAAqG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAiC,CACrD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAENrI,OAAA;gBAAK8H,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB/H,OAAA;kBAAA+H,QAAA,EAAO;gBAAY;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3BrI,OAAA;kBACE2B,IAAI,EAAC,MAAM;kBACX8C,IAAI,EAAC,YAAY;kBACjBC,KAAK,EAAElD,QAAQ,CAACM,UAAW;kBAC3BoH,QAAQ,EAAE3E;gBAAkB;kBAAA2D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENrI,OAAA;gBAAK8H,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB/H,OAAA;kBAAA+H,QAAA,EAAO;gBAAW;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1BrI,OAAA;kBACEyE,IAAI,EAAC,gBAAgB;kBACrBC,KAAK,EAAElD,QAAQ,CAACO,cAAe;kBAC/BmH,QAAQ,EAAE3E,iBAAkB;kBAAAwD,QAAA,gBAE5B/H,OAAA;oBAAQ0E,KAAK,EAAC,EAAE;oBAAAqD,QAAA,EAAC;kBAA2B;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACpDrH,YAAY,CAACuE,GAAG,CAACrC,WAAW,iBAC3BlD,OAAA;oBAA6B0E,KAAK,EAAExB,WAAW,CAAC+B,EAAG;oBAAA8C,QAAA,EAChD7E,WAAW,CAACxB;kBAAG,GADLwB,WAAW,CAAC+B,EAAE;oBAAAiD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEnB,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAENrI,OAAA;gBAAK8H,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB/H,OAAA;kBAAA+H,QAAA,EAAO;gBAAiB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAChCrI,OAAA;kBACE2B,IAAI,EAAC,MAAM;kBACX8C,IAAI,EAAC,qBAAqB;kBAC1BC,KAAK,EAAElD,QAAQ,CAACQ,mBAAoB;kBACpCkH,QAAQ,EAAE3E;gBAAkB;kBAAA2D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENrI,OAAA;gBAAK8H,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB/H,OAAA;kBAAA+H,QAAA,EAAO;gBAAe;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9BrI,OAAA;kBACE2B,IAAI,EAAC,MAAM;kBACX8C,IAAI,EAAC,mBAAmB;kBACxBC,KAAK,EAAElD,QAAQ,CAACS,iBAAkB;kBAClCiH,QAAQ,EAAE3E;gBAAkB;kBAAA2D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENrI,OAAA;gBAAK8H,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB/H,OAAA;kBAAA+H,QAAA,EAAO;gBAAY;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3BrI,OAAA;kBACE2B,IAAI,EAAC,QAAQ;kBACb8C,IAAI,EAAC,cAAc;kBACnBC,KAAK,EAAElD,QAAQ,CAACW,YAAa;kBAC7B+G,QAAQ,EAAE3E,iBAAkB;kBAC5B8E,GAAG,EAAC;gBAAG;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENrI,OAAA;gBAAK8H,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB/H,OAAA;kBAAA+H,QAAA,EAAO;gBAAa;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC5BrI,OAAA;kBACE2B,IAAI,EAAC,QAAQ;kBACb8C,IAAI,EAAC,WAAW;kBAChBC,KAAK,EAAElD,QAAQ,CAACY,SAAU;kBAC1B8G,QAAQ,EAAE3E,iBAAkB;kBAC5B8E,GAAG,EAAC;gBAAG;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENrI,OAAA;gBAAK8H,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB/H,OAAA;kBAAA+H,QAAA,EAAO;gBAAa;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC5BrI,OAAA;kBACE2B,IAAI,EAAC,QAAQ;kBACb8C,IAAI,EAAC,WAAW;kBAChBC,KAAK,EAAElD,QAAQ,CAACa,SAAU;kBAC1B6G,QAAQ,EAAE3E,iBAAkB;kBAC5B8E,GAAG,EAAC;gBAAG;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENrI,OAAA;cAAK8H,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5B/H,OAAA;gBAAA+H,QAAA,gBACE/H,OAAA;kBACE2B,IAAI,EAAC,UAAU;kBACf8C,IAAI,EAAC,UAAU;kBACfE,OAAO,EAAEnD,QAAQ,CAACU,QAAS;kBAC3BgH,QAAQ,EAAE3E;gBAAkB;kBAAA2D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC,0BAEJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAENrI,OAAA;cAAK8H,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B/H,OAAA;gBAAQ2B,IAAI,EAAC,QAAQ;gBAACmH,OAAO,EAAEpD,SAAU;gBAAAqC,QAAA,EAAC;cAE1C;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTrI,OAAA;gBAAQ2B,IAAI,EAAC,QAAQ;gBAAC2H,QAAQ,EAAElI,UAAW;gBAAA2G,QAAA,gBACzC/H,OAAA,CAACnB,eAAe;kBAAC2J,IAAI,EAAE1H,iBAAiB,GAAGpB,MAAM,GAAGH,MAAO;kBAACkJ,KAAK,EAAE;oBAACC,WAAW,EAAE;kBAAC;gBAAE;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACtFjH,UAAU,GAAG,aAAa,GAAGN,iBAAiB,GAAG,aAAa,GAAG,SAAS;cAAA;gBAAAoH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA7H,OAAO,gBACNR,OAAA;QAAK8H,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9B/H,OAAA;UAAA+H,QAAA,EAAG;QAA6B;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,GACJ3H,KAAK,gBACPV,OAAA;QAAK8H,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5B/H,OAAA;UAAA+H,QAAA,GAAG,8BAA4B,EAACrH,KAAK;QAAA;UAAAwH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1CrI,OAAA;UAAQ8I,OAAO,EAAEA,CAAA,KAAM5C,MAAM,CAAC/F,QAAQ,CAACoJ,MAAM,CAAC,CAAE;UAAAxB,QAAA,EAAC;QAEjD;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,GACJ/H,mBAAmB,CAAC+F,MAAM,KAAK,CAAC,gBAClCrG,OAAA;QAAK8H,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC5B/H,OAAA;UAAA+H,QAAA,EAAIzG,UAAU,GAAG,uBAAuB,GAAG;QAAyB;UAAA4G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtE,CAAC,gBAENrI,OAAA;QAAO8H,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAClC/H,OAAA;UAAA+H,QAAA,eACE/H,OAAA;YAAA+H,QAAA,gBACE/H,OAAA;cAAA+H,QAAA,EAAI;YAAG;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACZrI,OAAA;cAAA+H,QAAA,EAAI;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACbrI,OAAA;cAAA+H,QAAA,EAAI;YAAe;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxBrI,OAAA;cAAA+H,QAAA,EAAI;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACfrI,OAAA;cAAA+H,QAAA,EAAI;YAAY;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrBrI,OAAA;cAAA+H,QAAA,EAAI;YAAW;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpBrI,OAAA;cAAA+H,QAAA,EAAI;YAAO;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACRrI,OAAA;UAAA+H,QAAA,EACGzH,mBAAmB,CAACiF,GAAG,CAAEC,EAAE;YAAA,IAAAgE,cAAA,EAAAC,gBAAA;YAAA,oBAC1BzJ,OAAA;cAAA+H,QAAA,gBACE/H,OAAA;gBAAA+H,QAAA,EAAKvC,EAAE,CAAC9D,GAAG,IAAI;cAAK;gBAAAwG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC1BrI,OAAA;gBAAA+H,QAAA,EAAKvC,EAAE,CAAC7D,IAAI,IAAI;cAAK;gBAAAuG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC3BrI,OAAA;gBAAA+H,QAAA,EAAKvC,EAAE,CAAC5D,YAAY,IAAI;cAAK;gBAAAsG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACnCrI,OAAA;gBAAA+H,QAAA,EAAKvC,EAAE,CAAC3D,MAAM,IAAI;cAAK;gBAAAqG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC7BrI,OAAA;gBAAA+H,QAAA,EAAK,EAAAyB,cAAA,GAAAhE,EAAE,CAAC1D,UAAU,cAAA0H,cAAA,uBAAbA,cAAA,CAAexD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAI;cAAK;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAChDrI,OAAA;gBAAA+H,QAAA,EAAK,EAAA0B,gBAAA,GAAAjE,EAAE,CAACtC,WAAW,cAAAuG,gBAAA,uBAAdA,gBAAA,CAAgB/H,GAAG,KAAI;cAAc;gBAAAwG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAChDrI,OAAA;gBAAI8H,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC1B/H,OAAA;kBACE8I,OAAO,EAAEA,CAAA,KAAMnD,UAAU,CAACH,EAAE,CAAE;kBAC9BsC,SAAS,EAAC,qBAAqB;kBAC/B4B,KAAK,EAAC,UAAU;kBAAA3B,QAAA,eAEhB/H,OAAA,CAACnB,eAAe;oBAAC2J,IAAI,EAAEhJ;kBAAO;oBAAA0I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,eACTrI,OAAA;kBACE8I,OAAO,EAAEA,CAAA,KAAM7C,YAAY,CAACT,EAAE,CAACP,EAAE,CAAE;kBACnC6C,SAAS,EAAC,uBAAuB;kBACjC4B,KAAK,EAAC,WAAW;kBAAA3B,QAAA,eAEjB/H,OAAA,CAACnB,eAAe;oBAAC2J,IAAI,EAAE/I;kBAAQ;oBAAAyI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC;YAAA,GAtBE7C,EAAE,CAACP,EAAE;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAuBV,CAAC;UAAA,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACnI,EAAA,CAnmBuBD,WAAW;EAAA,QAChBtB,WAAW;AAAA;AAAAgL,EAAA,GADN1J,WAAW;AAAA,IAAA0J,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}