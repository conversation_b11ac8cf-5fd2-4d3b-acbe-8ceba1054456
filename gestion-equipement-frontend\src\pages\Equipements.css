/* Structure principale */
.equipements-container {
  display: flex;
  min-height: 100vh;
  background-color: #f8f9fc;
}

.sidebar {
  width: 250px;
  background: linear-gradient(135deg, #1565c0 0%, #1e88e5 100%);
  color: #fff;
  display: flex;
  flex-direction: column;
  padding: 20px 0;
  box-shadow: 2px 0 16px rgba(21,101,192,0.12);
}

.logo-section {
  padding: 32px 20px 24px;
  text-align: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.company-logo {
  width: 160px;
  height: 150px;
  object-fit: contain;
  background: #fff;
  padding: 8px;
  object-fit: cover;
  border-radius: 50%;
  box-shadow: 0 4px 24px rgba(21,101,192,0.15);
  margin: 0 auto 16px auto;
}

.main-menu {
  flex-grow: 1;
  margin-top: 20px;
}

.main-menu ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.main-menu li a {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: rgba(255, 255, 255, 0.92);
  text-decoration: none;
  transition: all 0.3s;
}

.main-menu li a.active {
  background: #1976d2;
  color: #fff !important;
  font-weight: bold;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(21,101,192,0.10);
  border-left: 6px solid #fff;
  transition: background 0.2s, color 0.2s;
}

.main-menu li a:hover {
  background: rgba(255,255,255,0.18);
  color: #fff;
}

.main-menu li a i {
  margin-right: 10px;
  width: 20px;
  text-align: center;
}

.secondary-links {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 15px 20px 0;
}

.secondary-links a {
  display: block;
  color: #e3f2fd;
  text-decoration: none;
  padding: 8px 0;
  font-size: 0.9rem;
}

.secondary-links a:hover {
  color: #bbdefb;
  background: linear-gradient(90deg, #1976d2 0%, #64b5f6 100%);
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 10px 28px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  margin-top: 24px;
  box-shadow: 0 2px 8px rgba(21,101,192,0.10);
  transition: background 0.2s, transform 0.2s;
}
.logout-btn:hover {
  background: linear-gradient(90deg, #64b5f6 0%, #1976d2 100%);
  transform: scale(1.04);
}

.secondary-links a i {
  margin-right: 10px;
}

.equipements-content {
  flex-grow: 1;
  padding: 32px 40px;
}

.equipements-table {
  width: 100%;
  border-collapse: collapse;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(21,101,192,0.10);
  overflow: hidden;
}
.equipements-table th {
  background: #f5f7fa;
  padding: 12px;
  border: 1px solid #e0e0e0;
  color: #1976d2;
  font-weight: 700;
  font-size: 1rem;
}
.equipements-table td {
  padding: 10px;
  border: 1px solid #e0e0e0;
  font-size: 0.98rem;
  color: #2e3a4e;
}
.equipements-table tr:hover {
  background: #e3f2fd;
  transition: background 0.2s;
}
.equipements-title {
  color: #1565c0;
  font-size: 1.6rem;
  font-weight: 700;
  margin-bottom: 24px;
}
