import { useState } from "react";
import "./Login.css";

export default function ForgotPassword() {
  const [email, setEmail] = useState("");
  const [message, setMessage] = useState("");

  const handleSubmit = async (e) => {
    e.preventDefault();
    setMessage("");
    const response = await fetch("http://localhost:8080/api/auth/forgot-password", {
      method: "POST",
      headers: { "Content-Type": "application/x-www-form-urlencoded" },
      body: new URLSearchParams({ email }),
    });
    const text = await response.text();
    setMessage(text);
  };

  return (
    <div className="login-container">
      <div className="login-box">
        <h2 className="text-center mb-4">Mot de passe oublié</h2>
        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <input
              type="email"
              placeholder="Votre email"
              value={email}
              onChange={e => setEmail(e.target.value)}
              required
              className="form-control"
            />
          </div>
          <button type="submit" className="btn btn-primary w-100 mb-3">Envoyer le lien de réinitialisation</button>
        </form>
        {message && <div className="reset-message text-center mt-3">{message}</div>}
      </div>
    </div>
  );
}