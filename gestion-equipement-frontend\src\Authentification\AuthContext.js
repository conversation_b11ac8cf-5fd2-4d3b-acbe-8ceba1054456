import { createContext, useState, useCallback } from "react";

export const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Fonction de login utilisant HTTP Basic Auth
  const login = useCallback(async (email, password) => {
    try {
      const response = await fetch("http://localhost:8081/api/test/ping", {
        method: "GET",
        headers: {
          // Construit l'en-tête d'authentification Basic
          "Authorization": "Basic " + btoa(`${email}:${password}`),
        }
      });

      if (!response.ok) {
        // Log l'erreur pour le débogage
        console.error("Login request failed with status:", response.status);
        throw new Error("Login failed");
      }

      // Si la réponse est OK, l'authentification a réussi
      localStorage.setItem("authBasic", btoa(`${email}:${password}`));
      setIsAuthenticated(true);
      return { success: true };

    } catch (error) {
      console.error("Login error:", error.message);
      return { success: false, error: error.message };
    }
  }, []);

  // Fonction pour demander la réinitialisation du mot de passe
  const requestPasswordReset = useCallback(async (email) => {
    try {
      const response = await fetch("http://localhost:8081/api/auth/forgot-password", {
        method: "POST",
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
        body: new URLSearchParams({ email }),
      });

      const data = await response.text();
      return { success: response.ok, message: data };
    } catch (error) {
      console.error("Reset request error:", error);
      return { success: false, message: "Network error" };
    }
  }, []);

  // Fonction pour soumettre le nouveau mot de passe
  const resetPassword = useCallback(async (token, newPassword) => {
    try {
      const response = await fetch("http://localhost:8081/api/auth/reset-password", {
        method: "POST",
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
        body: new URLSearchParams({ token, newPassword }),
      });

      const data = await response.text();
      return { success: response.ok, message: data };
    } catch (error) {
      console.error("Reset error:", error);
      return { success: false, message: "Network error" };
    }
  }, []);

  const logout = useCallback(() => {
    localStorage.removeItem("authBasic");
    setIsAuthenticated(false);
  }, []);

  return (
    <AuthContext.Provider
      value={{
        isAuthenticated,
        login,
        logout,
        requestPasswordReset,
        resetPassword
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};