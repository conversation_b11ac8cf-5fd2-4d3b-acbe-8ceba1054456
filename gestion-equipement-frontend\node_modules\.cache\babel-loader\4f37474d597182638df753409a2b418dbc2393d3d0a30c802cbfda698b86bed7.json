{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\gestion-equipement-frontend\\\\src\\\\pages\\\\Equipements.js\",\n  _s = $RefreshSig$();\nimport { Link, useLocation } from 'react-router-dom';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { faLaptop, faUsers, faExchangeAlt, faTruck, faUserCog, faSignOutAlt, faSearch, faBell, faTimes, faPlus, faEdit, faTrash, faSave } from '@fortawesome/free-solid-svg-icons';\nimport { useEffect, useState } from \"react\";\nimport jsPDF from \"jspdf\";\nimport \"jspdf-autotable\";\nimport * as XLSX from \"xlsx\";\n\n// Styles CSS intégrés\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst styles = `\n.equipements-container {\n  display: flex;\n  min-height: 100vh;\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n}\n\n/* Sidebar Styles */\n.sidebar {\n  width: 250px;\n  background-color: #2c3e50;\n  color: white;\n  display: flex;\n  flex-direction: column;\n  padding: 20px 0;\n}\n\n.logo-section {\n  padding: 0 20px 20px;\n  border-bottom: 1px solid #34495e;\n  margin-bottom: 20px;\n}\n\n.company-logo {\n  max-width: 100%;\n  height: auto;\n}\n\n.main-menu ul {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n\n.main-menu li a {\n  color: #ecf0f1;\n  text-decoration: none;\n  padding: 12px 20px;\n  display: block;\n  transition: background-color 0.3s;\n}\n\n.main-menu li a:hover {\n  background-color: #34495e;\n}\n\n.main-menu li a.active {\n  background-color: #3498db;\n  border-left: 4px solid #2980b9;\n}\n\n.secondary-links {\n  padding: 20px;\n  margin-top: auto;\n}\n\n.secondary-links a {\n  color: #ecf0f1;\n  text-decoration: none;\n  display: block;\n  padding: 8px 0;\n}\n\n.logout-btn {\n  background-color: #e74c3c;\n  color: white;\n  border: none;\n  padding: 10px 15px;\n  border-radius: 4px;\n  cursor: pointer;\n  width: 80%;\n  font-weight: 600;\n}\n\n.logout-btn:hover {\n  background-color: #c0392b;\n}\n\n/* Main Content Styles */\n.equipements-content {\n  flex: 1;\n  padding: 30px;\n  background-color: #f5f7fa;\n}\n\n.dashboard-header-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 30px;\n}\n\n.search-bar {\n  position: relative;\n  flex: 1;\n  max-width: 500px;\n}\n\n.search-bar input {\n  width: 100%;\n  padding: 10px 15px 10px 40px;\n  border: 1px solid #ddd;\n  border-radius: 6px;\n  font-size: 14px;\n}\n\n.search-icon {\n  position: absolute;\n  left: 15px;\n  top: 50%;\n  transform: translateY(-50%);\n  color: #3498db;\n}\n\n.profile-block {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n.notification-icon {\n  position: relative;\n  cursor: pointer;\n}\n\n.notification-icon span {\n  position: absolute;\n  top: -5px;\n  right: -5px;\n  background-color: #e74c3c;\n  color: white;\n  border-radius: 50%;\n  padding: 2px 6px;\n  font-size: 12px;\n}\n\n.equipements-title {\n  color: #2c3e50;\n  margin-bottom: 25px;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 15px;\n  margin-bottom: 25px;\n  flex-wrap: wrap;\n}\n\n.btn {\n  padding: 10px 20px;\n  border-radius: 6px;\n  font-weight: 600;\n  cursor: pointer;\n  border: none;\n  display: flex;\n  align-items: center;\n  font-size: 14px;\n}\n\n.btn-primary {\n  background-color: #3498db;\n  color: white;\n}\n\n.btn-primary:hover {\n  background-color: #2980b9;\n}\n\n/* Table Styles */\n.equipements-table {\n  width: 100%;\n  border-collapse: collapse;\n  background-color: white;\n  box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n}\n\n.equipements-table th {\n  background-color: #3498db;\n  color: white;\n  padding: 12px 15px;\n  text-align: left;\n}\n\n.equipements-table td {\n  padding: 12px 15px;\n  border-bottom: 1px solid #eee;\n}\n\n.equipements-table tr:hover {\n  background-color: #f8f9fa;\n}\n\n.actions-cell {\n  display: flex;\n  gap: 10px;\n}\n\n.action-btn {\n  background: none;\n  border: none;\n  cursor: pointer;\n  padding: 5px;\n  border-radius: 4px;\n  width: 30px;\n  height: 30px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.edit-btn {\n  color: #3498db;\n}\n\n.edit-btn:hover {\n  background-color: #ebf5fb;\n}\n\n.delete-btn {\n  color: #e74c3c;\n}\n\n.delete-btn:hover {\n  background-color: #fdedec;\n}\n\n/* Modal Styles */\n.modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0,0,0,0.5);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n}\n\n.modal-content {\n  background-color: white;\n  border-radius: 8px;\n  padding: 30px;\n  width: 90%;\n  max-width: 700px;\n  max-height: 90vh;\n  overflow-y: auto;\n  box-shadow: 0 5px 20px rgba(0,0,0,0.2);\n}\n\n.modal-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.modal-header h3 {\n  margin: 0;\n  color: #2c3e50;\n}\n\n.modal-header button {\n  background: none;\n  border: none;\n  font-size: 20px;\n  cursor: pointer;\n  color: #7f8c8d;\n}\n\n.form-grid {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 20px;\n  margin-bottom: 20px;\n}\n\n.form-group {\n  margin-bottom: 15px;\n}\n\n.form-group label {\n  display: block;\n  margin-bottom: 8px;\n  font-weight: 600;\n  color: #2c3e50;\n}\n\n.form-group input,\n.form-group select {\n  width: 100%;\n  padding: 10px;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  font-size: 14px;\n}\n\n.form-checkbox {\n  margin-bottom: 20px;\n}\n\n.form-checkbox label {\n  display: flex;\n  align-items: center;\n  cursor: pointer;\n}\n\n.form-checkbox input {\n  margin-right: 8px;\n}\n\n.form-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 15px;\n}\n\n.form-actions button {\n  padding: 10px 20px;\n  border-radius: 4px;\n  cursor: pointer;\n  font-weight: 600;\n}\n\n.form-actions button[type=\"button\"] {\n  background-color: #ecf0f1;\n  border: 1px solid #bdc3c7;\n}\n\n.form-actions button[type=\"submit\"] {\n  background-color: #2ecc71;\n  color: white;\n  border: none;\n}\n\n.form-actions button[type=\"submit\"]:disabled {\n  background-color: #95a5a6;\n  cursor: not-allowed;\n}\n\n/* Messages Styles */\n.loading-message,\n.error-message,\n.empty-message {\n  text-align: center;\n  padding: 40px;\n  background-color: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n}\n\n.error-message {\n  color: #e74c3c;\n}\n\n.error-message button {\n  margin-top: 15px;\n  padding: 8px 16px;\n  background-color: #3498db;\n  color: white;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n}\n\n.home-active {\n  background-color: #3498db !important;\n}\n`;\nexport default function Equipements() {\n  _s();\n  const location = useLocation();\n  const [equipements, setEquipements] = useState([]);\n  const [filteredEquipements, setFilteredEquipements] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [showModal, setShowModal] = useState(false);\n  const [currentEquipement, setCurrentEquipement] = useState(null);\n  const [fournisseurs, setFournisseurs] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [submitting, setSubmitting] = useState(false);\n  const [searchTerm, setSearchTerm] = useState('');\n\n  // État du formulaire\n  const [formData, setFormData] = useState({\n    nom: '',\n    type: '',\n    numero_serie: '',\n    statut: 'DISPONIBLE',\n    date_achat: '',\n    fournisseur_id: '',\n    date_debut_garantie: '',\n    date_fin_garantie: '',\n    en_stock: true,\n    stock_actuel: 1,\n    stock_max: 1,\n    stock_min: 1,\n    categorie_id: ''\n  });\n\n  // Options pour les statuts et types\n  const statutOptions = ['DISPONIBLE', 'EN_MAINTENANCE', 'OCCUPE', 'HORS_SERVICE'];\n  const typeOptions = ['ORDINATEUR', 'IMPRIMANTE', 'SCANNER', 'TELEPHONE', 'TABLETTE', 'SERVEUR', 'AUTRE'];\n  useEffect(() => {\n    // Injecter les styles dans le head\n    const styleElement = document.createElement('style');\n    styleElement.innerHTML = styles;\n    document.head.appendChild(styleElement);\n    loadEquipements();\n    loadFournisseurs();\n    loadCategories();\n    return () => {\n      document.head.removeChild(styleElement);\n    };\n  }, []);\n  useEffect(() => {\n    if (searchTerm === '') {\n      setFilteredEquipements(equipements);\n    } else {\n      const filtered = equipements.filter(equipement => Object.values(equipement).some(value => value && value.toString().toLowerCase().includes(searchTerm.toLowerCase())));\n      setFilteredEquipements(filtered);\n    }\n  }, [searchTerm, equipements]);\n  const loadEquipements = async () => {\n    setLoading(true);\n    try {\n      const token = localStorage.getItem('authToken');\n      const response = await fetch(\"http://localhost:8081/api/equipements\", {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (!response.ok) throw new Error('Erreur de chargement');\n      const data = await response.json();\n      setEquipements(data);\n      setFilteredEquipements(data);\n      setError(null);\n    } catch (err) {\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const loadFournisseurs = async () => {\n    try {\n      const token = localStorage.getItem('authToken');\n      const response = await fetch(\"http://localhost:8081/api/fournisseurs\", {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      const data = await response.json();\n      setFournisseurs(Array.isArray(data) ? data : []);\n    } catch (err) {\n      console.error('Erreur chargement fournisseurs:', err);\n    }\n  };\n  const loadCategories = async () => {\n    try {\n      const token = localStorage.getItem('authToken');\n      const response = await fetch(\"http://localhost:8081/api/categories\", {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      const data = await response.json();\n      setCategories(Array.isArray(data) ? data : []);\n    } catch (err) {\n      console.error('Erreur chargement catégories:', err);\n    }\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value,\n      type,\n      checked\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setSubmitting(true);\n    try {\n      const token = localStorage.getItem('authToken');\n      const method = currentEquipement ? 'PUT' : 'POST';\n      const url = currentEquipement ? `http://localhost:8081/api/equipements/${currentEquipement.id}` : \"http://localhost:8081/api/equipements\";\n      const response = await fetch(url, {\n        method,\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`\n        },\n        body: JSON.stringify(formData)\n      });\n      if (!response.ok) throw new Error('Erreur lors de la requête');\n      const result = await response.json();\n      if (currentEquipement) {\n        setEquipements(prev => prev.map(eq => eq.id === currentEquipement.id ? result : eq));\n        alert('Équipement modifié avec succès!');\n      } else {\n        setEquipements(prev => [...prev, result]);\n        alert('Équipement ajouté avec succès!');\n      }\n      resetForm();\n      loadEquipements(); // Recharger les données\n    } catch (err) {\n      console.error('Erreur:', err);\n      alert(`Erreur: ${err.message}`);\n    } finally {\n      setSubmitting(false);\n    }\n  };\n  const handleEdit = equipement => {\n    var _equipement$date_acha, _equipement$fournisse, _equipement$date_debu, _equipement$date_fin_;\n    setCurrentEquipement(equipement);\n    setFormData({\n      nom: equipement.nom,\n      type: equipement.type,\n      numero_serie: equipement.numero_serie,\n      statut: equipement.statut,\n      date_achat: ((_equipement$date_acha = equipement.date_achat) === null || _equipement$date_acha === void 0 ? void 0 : _equipement$date_acha.split('T')[0]) || '',\n      fournisseur_id: ((_equipement$fournisse = equipement.fournisseur) === null || _equipement$fournisse === void 0 ? void 0 : _equipement$fournisse.id) || '',\n      date_debut_garantie: ((_equipement$date_debu = equipement.date_debut_garantie) === null || _equipement$date_debu === void 0 ? void 0 : _equipement$date_debu.split('T')[0]) || '',\n      date_fin_garantie: ((_equipement$date_fin_ = equipement.date_fin_garantie) === null || _equipement$date_fin_ === void 0 ? void 0 : _equipement$date_fin_.split('T')[0]) || '',\n      en_stock: equipement.en_stock,\n      stock_actuel: equipement.stock_actuel,\n      stock_max: equipement.stock_max,\n      stock_min: equipement.stock_min,\n      categorie_id: equipement.categorie_id\n    });\n    setShowModal(true);\n  };\n  const handleDelete = async id => {\n    if (!window.confirm(\"Êtes-vous sûr de vouloir supprimer cet équipement?\")) return;\n    try {\n      const token = localStorage.getItem('authToken');\n      const response = await fetch(`http://localhost:8081/api/equipements/${id}`, {\n        method: 'DELETE',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (!response.ok) throw new Error('Erreur lors de la suppression');\n      setEquipements(prev => prev.filter(eq => eq.id !== id));\n      alert('Équipement supprimé avec succès!');\n    } catch (err) {\n      console.error('Erreur:', err);\n      alert(`Erreur lors de la suppression: ${err.message}`);\n    }\n  };\n  const resetForm = () => {\n    setFormData({\n      nom: '',\n      type: '',\n      numero_serie: '',\n      statut: 'DISPONIBLE',\n      date_achat: '',\n      fournisseur_id: '',\n      date_debut_garantie: '',\n      date_fin_garantie: '',\n      en_stock: true,\n      stock_actuel: 1,\n      stock_max: 1,\n      stock_min: 1,\n      categorie_id: ''\n    });\n    setCurrentEquipement(null);\n    setShowModal(false);\n  };\n  const handleExportExcel = () => {\n    if (filteredEquipements.length === 0) {\n      alert(\"Aucune donnée à exporter\");\n      return;\n    }\n    const worksheet = XLSX.utils.json_to_sheet(filteredEquipements);\n    const workbook = XLSX.utils.book_new();\n    XLSX.utils.book_append_sheet(workbook, worksheet, \"Equipements\");\n    XLSX.writeFile(workbook, \"equipements.xlsx\");\n  };\n  const handleExportPDF = () => {\n    if (filteredEquipements.length === 0) {\n      alert(\"Aucune donnée à exporter\");\n      return;\n    }\n    const doc = new jsPDF();\n    doc.text(\"Liste des équipements\", 14, 16);\n    doc.autoTable({\n      head: [['Nom', 'Type', 'Numéro série', 'Statut', 'Date achat', 'Fournisseur']],\n      body: filteredEquipements.map(eq => {\n        var _eq$date_achat, _eq$fournisseur;\n        return [eq.nom || '', eq.type || '', eq.numero_serie || '', eq.statut || '', ((_eq$date_achat = eq.date_achat) === null || _eq$date_achat === void 0 ? void 0 : _eq$date_achat.split('T')[0]) || '', ((_eq$fournisseur = eq.fournisseur) === null || _eq$fournisseur === void 0 ? void 0 : _eq$fournisseur.nom) || ''];\n      }),\n      startY: 20\n    });\n    doc.save(\"equipements.pdf\");\n  };\n  const handlePrint = () => {\n    window.print();\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"equipements-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sidebar\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"logo-section\",\n        children: /*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Gestion IT\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 665,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 664,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"main-menu\",\n        children: /*#__PURE__*/_jsxDEV(\"ul\", {\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/\",\n              className: location.pathname === '/' ? 'home-active active' : '',\n              children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: faLaptop,\n                style: {\n                  marginRight: 8\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 671,\n                columnNumber: 17\n              }, this), \" Accueil\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 670,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 669,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/equipements\",\n              className: location.pathname === '/equipements' ? 'active' : '',\n              children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: faLaptop,\n                style: {\n                  marginRight: 8\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 676,\n                columnNumber: 17\n              }, this), \" \\xC9quipements\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 675,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 674,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/employes\",\n              children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: faUsers,\n                style: {\n                  marginRight: 8\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 681,\n                columnNumber: 17\n              }, this), \" Employ\\xE9s\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 680,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 679,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/affectations\",\n              children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: faExchangeAlt,\n                style: {\n                  marginRight: 8\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 686,\n                columnNumber: 17\n              }, this), \" Affectations\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 685,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 684,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/fournisseurs\",\n              children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: faTruck,\n                style: {\n                  marginRight: 8\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 691,\n                columnNumber: 17\n              }, this), \" Fournisseurs\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 690,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 689,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 668,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 667,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"secondary-links\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/profil\",\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faUserCog,\n            style: {\n              marginRight: 8\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 698,\n            columnNumber: 13\n          }, this), \" Profil\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 697,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 696,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: 'auto',\n          padding: '20px',\n          textAlign: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"logout-btn\",\n          onClick: () => {\n            localStorage.removeItem('authToken');\n            window.location.href = '/login';\n          },\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faSignOutAlt,\n            style: {\n              marginRight: 8\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 706,\n            columnNumber: 13\n          }, this), \" D\\xE9connexion\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 702,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 701,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 663,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"equipements-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-header-row\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-bar\",\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faSearch,\n            className: \"search-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 715,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Rechercher...\",\n            value: searchTerm,\n            onChange: e => setSearchTerm(e.target.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 716,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 714,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"profile-block\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"notification-icon\",\n            children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n              icon: faBell\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 725,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 726,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 724,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Admin IT\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 728,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faUserCog\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 729,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 723,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 713,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"equipements-title\",\n        children: [\"Gestion des \\xE9quipements (\", filteredEquipements.length, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 733,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"action-buttons\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          onClick: () => setShowModal(true),\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faPlus,\n            style: {\n              marginRight: 8\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 739,\n            columnNumber: 13\n          }, this), \" Ajouter\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 738,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          onClick: handleExportExcel,\n          children: \"Export Excel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 741,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          onClick: handleExportPDF,\n          children: \"Export PDF\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 744,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          onClick: handlePrint,\n          children: \"Imprimer\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 747,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 737,\n        columnNumber: 9\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-message\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Chargement en cours...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 755,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 754,\n        columnNumber: 11\n      }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-message\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 759,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: loadEquipements,\n          children: \"R\\xE9essayer\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 760,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 758,\n        columnNumber: 11\n      }, this) : filteredEquipements.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"empty-message\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: searchTerm ? \"Aucun résultat trouvé\" : \"Aucun équipement enregistré\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 764,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 763,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"table\", {\n        className: \"equipements-table\",\n        children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Nom\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 770,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Type\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 771,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Num\\xE9ro s\\xE9rie\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 772,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Statut\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 773,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Date achat\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 774,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Fournisseur\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 775,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Actions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 776,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 769,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 768,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n          children: filteredEquipements.map(equipement => {\n            var _equipement$date_acha2, _equipement$fournisse2;\n            return /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                children: equipement.nom\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 782,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: equipement.type\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 783,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: equipement.numero_serie || '-'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 784,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: equipement.statut\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 785,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: ((_equipement$date_acha2 = equipement.date_achat) === null || _equipement$date_acha2 === void 0 ? void 0 : _equipement$date_acha2.split('T')[0]) || '-'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 786,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: ((_equipement$fournisse2 = equipement.fournisseur) === null || _equipement$fournisse2 === void 0 ? void 0 : _equipement$fournisse2.nom) || '-'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 787,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"actions-cell\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"action-btn edit-btn\",\n                  onClick: () => handleEdit(equipement),\n                  title: \"Modifier\",\n                  children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                    icon: faEdit\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 794,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 789,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"action-btn delete-btn\",\n                  onClick: () => handleDelete(equipement.id),\n                  title: \"Supprimer\",\n                  children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                    icon: faTrash\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 801,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 796,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 788,\n                columnNumber: 19\n              }, this)]\n            }, equipement.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 781,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 779,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 767,\n        columnNumber: 11\n      }, this), showModal && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-overlay\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: currentEquipement ? 'Modifier équipement' : 'Ajouter un équipement'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 815,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: resetForm,\n              children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: faTimes\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 817,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 816,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 814,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSubmit,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-grid\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Nom *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 824,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"nom\",\n                  value: formData.nom,\n                  onChange: handleInputChange,\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 825,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 823,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Type *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 835,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"type\",\n                  value: formData.type,\n                  onChange: handleInputChange,\n                  required: true,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"S\\xE9lectionner...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 842,\n                    columnNumber: 23\n                  }, this), typeOptions.map(type => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: type,\n                    children: type\n                  }, type, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 844,\n                    columnNumber: 25\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 836,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 834,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Num\\xE9ro de s\\xE9rie\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 850,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"numero_serie\",\n                  value: formData.numero_serie,\n                  onChange: handleInputChange\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 851,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 849,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Statut *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 860,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"statut\",\n                  value: formData.statut,\n                  onChange: handleInputChange,\n                  required: true,\n                  children: statutOptions.map(statut => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: statut,\n                    children: statut\n                  }, statut, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 868,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 861,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 859,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Date d'achat\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 874,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  name: \"date_achat\",\n                  value: formData.date_achat,\n                  onChange: handleInputChange\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 875,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 873,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Fournisseur\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 884,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"fournisseur_id\",\n                  value: formData.fournisseur_id,\n                  onChange: handleInputChange,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"S\\xE9lectionner...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 890,\n                    columnNumber: 23\n                  }, this), fournisseurs.map(fournisseur => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: fournisseur.id,\n                    children: fournisseur.nom\n                  }, fournisseur.id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 892,\n                    columnNumber: 25\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 885,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 883,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"D\\xE9but garantie\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 900,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  name: \"date_debut_garantie\",\n                  value: formData.date_debut_garantie,\n                  onChange: handleInputChange\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 901,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 899,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Fin garantie\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 910,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  name: \"date_fin_garantie\",\n                  value: formData.date_fin_garantie,\n                  onChange: handleInputChange\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 911,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 909,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Stock actuel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 920,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  name: \"stock_actuel\",\n                  value: formData.stock_actuel,\n                  onChange: handleInputChange,\n                  min: \"0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 921,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 919,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Stock max\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 931,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  name: \"stock_max\",\n                  value: formData.stock_max,\n                  onChange: handleInputChange,\n                  min: \"1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 932,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 930,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Stock min\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 942,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  name: \"stock_min\",\n                  value: formData.stock_min,\n                  onChange: handleInputChange,\n                  min: \"0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 943,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 941,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 822,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-checkbox\",\n              children: /*#__PURE__*/_jsxDEV(\"label\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  name: \"en_stock\",\n                  checked: formData.en_stock,\n                  onChange: handleInputChange\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 955,\n                  columnNumber: 21\n                }, this), \"En stock\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 954,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 953,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-actions\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: resetForm,\n                children: \"Annuler\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 966,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                disabled: submitting,\n                children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                  icon: currentEquipement ? faSave : faPlus,\n                  style: {\n                    marginRight: 8\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 970,\n                  columnNumber: 21\n                }, this), submitting ? 'En cours...' : currentEquipement ? 'Enregistrer' : 'Ajouter']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 969,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 965,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 821,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 813,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 812,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 712,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 661,\n    columnNumber: 5\n  }, this);\n}\n_s(Equipements, \"eJiLtHr6zN4yRdSIXtWM3cjXfpI=\", false, function () {\n  return [useLocation];\n});\n_c = Equipements;\nvar _c;\n$RefreshReg$(_c, \"Equipements\");", "map": {"version": 3, "names": ["Link", "useLocation", "FontAwesomeIcon", "faLaptop", "faUsers", "faExchangeAlt", "faTruck", "faUserCog", "faSignOutAlt", "faSearch", "faBell", "faTimes", "faPlus", "faEdit", "faTrash", "faSave", "useEffect", "useState", "jsPDF", "XLSX", "jsxDEV", "_jsxDEV", "styles", "Equipements", "_s", "location", "equipements", "setEquipements", "filteredEquipements", "setFilteredEquipements", "loading", "setLoading", "error", "setError", "showModal", "setShowModal", "currentEquipement", "setCurrentEquipement", "fournisseurs", "setFournisseurs", "categories", "setCategories", "submitting", "setSubmitting", "searchTerm", "setSearchTerm", "formData", "setFormData", "nom", "type", "numero_serie", "statut", "date_achat", "fournisseur_id", "date_debut_garantie", "date_fin_garantie", "en_stock", "stock_actuel", "stock_max", "stock_min", "categorie_id", "statutOptions", "typeOptions", "styleElement", "document", "createElement", "innerHTML", "head", "append<PERSON><PERSON><PERSON>", "loadEquipements", "loadFournisseurs", "loadCategories", "<PERSON><PERSON><PERSON><PERSON>", "filtered", "filter", "equipement", "Object", "values", "some", "value", "toString", "toLowerCase", "includes", "token", "localStorage", "getItem", "response", "fetch", "headers", "ok", "Error", "data", "json", "err", "message", "Array", "isArray", "console", "handleInputChange", "e", "name", "checked", "target", "prev", "handleSubmit", "preventDefault", "method", "url", "id", "body", "JSON", "stringify", "result", "map", "eq", "alert", "resetForm", "handleEdit", "_equipement$date_acha", "_equipement$fournisse", "_equipement$date_debu", "_equipement$date_fin_", "split", "<PERSON><PERSON><PERSON><PERSON>", "handleDelete", "window", "confirm", "handleExportExcel", "length", "worksheet", "utils", "json_to_sheet", "workbook", "book_new", "book_append_sheet", "writeFile", "handleExportPDF", "doc", "text", "autoTable", "_eq$date_achat", "_eq$fournisseur", "startY", "save", "handlePrint", "print", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "pathname", "icon", "style", "marginRight", "marginTop", "padding", "textAlign", "onClick", "removeItem", "href", "placeholder", "onChange", "_equipement$date_acha2", "_equipement$fournisse2", "title", "onSubmit", "required", "min", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/gestion-equipement-frontend/src/pages/Equipements.js"], "sourcesContent": ["import { Link, useLocation } from 'react-router-dom';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { \n  faLaptop, faUsers, faExchangeAlt, faTruck, \n  faUserCog, faSignOutAlt, faSearch, faBell, \n  faTimes, faPlus, faEdit, faTrash, faSave \n} from '@fortawesome/free-solid-svg-icons';\nimport { useEffect, useState } from \"react\";\nimport jsPDF from \"jspdf\";\nimport \"jspdf-autotable\";\nimport * as XLSX from \"xlsx\";\n\n// Styles CSS intégrés\nconst styles = `\n.equipements-container {\n  display: flex;\n  min-height: 100vh;\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n}\n\n/* Sidebar Styles */\n.sidebar {\n  width: 250px;\n  background-color: #2c3e50;\n  color: white;\n  display: flex;\n  flex-direction: column;\n  padding: 20px 0;\n}\n\n.logo-section {\n  padding: 0 20px 20px;\n  border-bottom: 1px solid #34495e;\n  margin-bottom: 20px;\n}\n\n.company-logo {\n  max-width: 100%;\n  height: auto;\n}\n\n.main-menu ul {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n\n.main-menu li a {\n  color: #ecf0f1;\n  text-decoration: none;\n  padding: 12px 20px;\n  display: block;\n  transition: background-color 0.3s;\n}\n\n.main-menu li a:hover {\n  background-color: #34495e;\n}\n\n.main-menu li a.active {\n  background-color: #3498db;\n  border-left: 4px solid #2980b9;\n}\n\n.secondary-links {\n  padding: 20px;\n  margin-top: auto;\n}\n\n.secondary-links a {\n  color: #ecf0f1;\n  text-decoration: none;\n  display: block;\n  padding: 8px 0;\n}\n\n.logout-btn {\n  background-color: #e74c3c;\n  color: white;\n  border: none;\n  padding: 10px 15px;\n  border-radius: 4px;\n  cursor: pointer;\n  width: 80%;\n  font-weight: 600;\n}\n\n.logout-btn:hover {\n  background-color: #c0392b;\n}\n\n/* Main Content Styles */\n.equipements-content {\n  flex: 1;\n  padding: 30px;\n  background-color: #f5f7fa;\n}\n\n.dashboard-header-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 30px;\n}\n\n.search-bar {\n  position: relative;\n  flex: 1;\n  max-width: 500px;\n}\n\n.search-bar input {\n  width: 100%;\n  padding: 10px 15px 10px 40px;\n  border: 1px solid #ddd;\n  border-radius: 6px;\n  font-size: 14px;\n}\n\n.search-icon {\n  position: absolute;\n  left: 15px;\n  top: 50%;\n  transform: translateY(-50%);\n  color: #3498db;\n}\n\n.profile-block {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n.notification-icon {\n  position: relative;\n  cursor: pointer;\n}\n\n.notification-icon span {\n  position: absolute;\n  top: -5px;\n  right: -5px;\n  background-color: #e74c3c;\n  color: white;\n  border-radius: 50%;\n  padding: 2px 6px;\n  font-size: 12px;\n}\n\n.equipements-title {\n  color: #2c3e50;\n  margin-bottom: 25px;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 15px;\n  margin-bottom: 25px;\n  flex-wrap: wrap;\n}\n\n.btn {\n  padding: 10px 20px;\n  border-radius: 6px;\n  font-weight: 600;\n  cursor: pointer;\n  border: none;\n  display: flex;\n  align-items: center;\n  font-size: 14px;\n}\n\n.btn-primary {\n  background-color: #3498db;\n  color: white;\n}\n\n.btn-primary:hover {\n  background-color: #2980b9;\n}\n\n/* Table Styles */\n.equipements-table {\n  width: 100%;\n  border-collapse: collapse;\n  background-color: white;\n  box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n}\n\n.equipements-table th {\n  background-color: #3498db;\n  color: white;\n  padding: 12px 15px;\n  text-align: left;\n}\n\n.equipements-table td {\n  padding: 12px 15px;\n  border-bottom: 1px solid #eee;\n}\n\n.equipements-table tr:hover {\n  background-color: #f8f9fa;\n}\n\n.actions-cell {\n  display: flex;\n  gap: 10px;\n}\n\n.action-btn {\n  background: none;\n  border: none;\n  cursor: pointer;\n  padding: 5px;\n  border-radius: 4px;\n  width: 30px;\n  height: 30px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.edit-btn {\n  color: #3498db;\n}\n\n.edit-btn:hover {\n  background-color: #ebf5fb;\n}\n\n.delete-btn {\n  color: #e74c3c;\n}\n\n.delete-btn:hover {\n  background-color: #fdedec;\n}\n\n/* Modal Styles */\n.modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0,0,0,0.5);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n}\n\n.modal-content {\n  background-color: white;\n  border-radius: 8px;\n  padding: 30px;\n  width: 90%;\n  max-width: 700px;\n  max-height: 90vh;\n  overflow-y: auto;\n  box-shadow: 0 5px 20px rgba(0,0,0,0.2);\n}\n\n.modal-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.modal-header h3 {\n  margin: 0;\n  color: #2c3e50;\n}\n\n.modal-header button {\n  background: none;\n  border: none;\n  font-size: 20px;\n  cursor: pointer;\n  color: #7f8c8d;\n}\n\n.form-grid {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 20px;\n  margin-bottom: 20px;\n}\n\n.form-group {\n  margin-bottom: 15px;\n}\n\n.form-group label {\n  display: block;\n  margin-bottom: 8px;\n  font-weight: 600;\n  color: #2c3e50;\n}\n\n.form-group input,\n.form-group select {\n  width: 100%;\n  padding: 10px;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  font-size: 14px;\n}\n\n.form-checkbox {\n  margin-bottom: 20px;\n}\n\n.form-checkbox label {\n  display: flex;\n  align-items: center;\n  cursor: pointer;\n}\n\n.form-checkbox input {\n  margin-right: 8px;\n}\n\n.form-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 15px;\n}\n\n.form-actions button {\n  padding: 10px 20px;\n  border-radius: 4px;\n  cursor: pointer;\n  font-weight: 600;\n}\n\n.form-actions button[type=\"button\"] {\n  background-color: #ecf0f1;\n  border: 1px solid #bdc3c7;\n}\n\n.form-actions button[type=\"submit\"] {\n  background-color: #2ecc71;\n  color: white;\n  border: none;\n}\n\n.form-actions button[type=\"submit\"]:disabled {\n  background-color: #95a5a6;\n  cursor: not-allowed;\n}\n\n/* Messages Styles */\n.loading-message,\n.error-message,\n.empty-message {\n  text-align: center;\n  padding: 40px;\n  background-color: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n}\n\n.error-message {\n  color: #e74c3c;\n}\n\n.error-message button {\n  margin-top: 15px;\n  padding: 8px 16px;\n  background-color: #3498db;\n  color: white;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n}\n\n.home-active {\n  background-color: #3498db !important;\n}\n`;\n\nexport default function Equipements() {\n  const location = useLocation();\n  const [equipements, setEquipements] = useState([]);\n  const [filteredEquipements, setFilteredEquipements] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [showModal, setShowModal] = useState(false);\n  const [currentEquipement, setCurrentEquipement] = useState(null);\n  const [fournisseurs, setFournisseurs] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [submitting, setSubmitting] = useState(false);\n  const [searchTerm, setSearchTerm] = useState('');\n\n  // État du formulaire\n  const [formData, setFormData] = useState({\n    nom: '',\n    type: '',\n    numero_serie: '',\n    statut: 'DISPONIBLE',\n    date_achat: '',\n    fournisseur_id: '',\n    date_debut_garantie: '',\n    date_fin_garantie: '',\n    en_stock: true,\n    stock_actuel: 1,\n    stock_max: 1,\n    stock_min: 1,\n    categorie_id: ''\n  });\n\n  // Options pour les statuts et types\n  const statutOptions = ['DISPONIBLE', 'EN_MAINTENANCE', 'OCCUPE', 'HORS_SERVICE'];\n  const typeOptions = ['ORDINATEUR', 'IMPRIMANTE', 'SCANNER', 'TELEPHONE', 'TABLETTE', 'SERVEUR', 'AUTRE'];\n\n  useEffect(() => {\n    // Injecter les styles dans le head\n    const styleElement = document.createElement('style');\n    styleElement.innerHTML = styles;\n    document.head.appendChild(styleElement);\n\n    loadEquipements();\n    loadFournisseurs();\n    loadCategories();\n\n    return () => {\n      document.head.removeChild(styleElement);\n    };\n  }, []);\n\n  useEffect(() => {\n    if (searchTerm === '') {\n      setFilteredEquipements(equipements);\n    } else {\n      const filtered = equipements.filter(equipement =>\n        Object.values(equipement).some(\n          value => value && \n          value.toString().toLowerCase().includes(searchTerm.toLowerCase())\n        )\n      );\n      setFilteredEquipements(filtered);\n    }\n  }, [searchTerm, equipements]);\n\n  const loadEquipements = async () => {\n    setLoading(true);\n    try {\n      const token = localStorage.getItem('authToken');\n      const response = await fetch(\"http://localhost:8081/api/equipements\", {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      \n      if (!response.ok) throw new Error('Erreur de chargement');\n      \n      const data = await response.json();\n      setEquipements(data);\n      setFilteredEquipements(data);\n      setError(null);\n    } catch (err) {\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadFournisseurs = async () => {\n    try {\n      const token = localStorage.getItem('authToken');\n      const response = await fetch(\"http://localhost:8081/api/fournisseurs\", {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      \n      const data = await response.json();\n      setFournisseurs(Array.isArray(data) ? data : []);\n    } catch (err) {\n      console.error('Erreur chargement fournisseurs:', err);\n    }\n  };\n\n  const loadCategories = async () => {\n    try {\n      const token = localStorage.getItem('authToken');\n      const response = await fetch(\"http://localhost:8081/api/categories\", {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      \n      const data = await response.json();\n      setCategories(Array.isArray(data) ? data : []);\n    } catch (err) {\n      console.error('Erreur chargement catégories:', err);\n    }\n  };\n\n  const handleInputChange = (e) => {\n    const { name, value, type, checked } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setSubmitting(true);\n\n    try {\n      const token = localStorage.getItem('authToken');\n      const method = currentEquipement ? 'PUT' : 'POST';\n      const url = currentEquipement \n        ? `http://localhost:8081/api/equipements/${currentEquipement.id}`\n        : \"http://localhost:8081/api/equipements\";\n\n      const response = await fetch(url, {\n        method,\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`\n        },\n        body: JSON.stringify(formData)\n      });\n\n      if (!response.ok) throw new Error('Erreur lors de la requête');\n\n      const result = await response.json();\n      \n      if (currentEquipement) {\n        setEquipements(prev => \n          prev.map(eq => eq.id === currentEquipement.id ? result : eq)\n        );\n        alert('Équipement modifié avec succès!');\n      } else {\n        setEquipements(prev => [...prev, result]);\n        alert('Équipement ajouté avec succès!');\n      }\n\n      resetForm();\n      loadEquipements(); // Recharger les données\n    } catch (err) {\n      console.error('Erreur:', err);\n      alert(`Erreur: ${err.message}`);\n    } finally {\n      setSubmitting(false);\n    }\n  };\n\n  const handleEdit = (equipement) => {\n    setCurrentEquipement(equipement);\n    setFormData({\n      nom: equipement.nom,\n      type: equipement.type,\n      numero_serie: equipement.numero_serie,\n      statut: equipement.statut,\n      date_achat: equipement.date_achat?.split('T')[0] || '',\n      fournisseur_id: equipement.fournisseur?.id || '',\n      date_debut_garantie: equipement.date_debut_garantie?.split('T')[0] || '',\n      date_fin_garantie: equipement.date_fin_garantie?.split('T')[0] || '',\n      en_stock: equipement.en_stock,\n      stock_actuel: equipement.stock_actuel,\n      stock_max: equipement.stock_max,\n      stock_min: equipement.stock_min,\n      categorie_id: equipement.categorie_id\n    });\n    setShowModal(true);\n  };\n\n  const handleDelete = async (id) => {\n    if (!window.confirm(\"Êtes-vous sûr de vouloir supprimer cet équipement?\")) return;\n\n    try {\n      const token = localStorage.getItem('authToken');\n      const response = await fetch(`http://localhost:8081/api/equipements/${id}`, {\n        method: 'DELETE',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      if (!response.ok) throw new Error('Erreur lors de la suppression');\n\n      setEquipements(prev => prev.filter(eq => eq.id !== id));\n      alert('Équipement supprimé avec succès!');\n    } catch (err) {\n      console.error('Erreur:', err);\n      alert(`Erreur lors de la suppression: ${err.message}`);\n    }\n  };\n\n  const resetForm = () => {\n    setFormData({\n      nom: '',\n      type: '',\n      numero_serie: '',\n      statut: 'DISPONIBLE',\n      date_achat: '',\n      fournisseur_id: '',\n      date_debut_garantie: '',\n      date_fin_garantie: '',\n      en_stock: true,\n      stock_actuel: 1,\n      stock_max: 1,\n      stock_min: 1,\n      categorie_id: ''\n    });\n    setCurrentEquipement(null);\n    setShowModal(false);\n  };\n\n  const handleExportExcel = () => {\n    if (filteredEquipements.length === 0) {\n      alert(\"Aucune donnée à exporter\");\n      return;\n    }\n    \n    const worksheet = XLSX.utils.json_to_sheet(filteredEquipements);\n    const workbook = XLSX.utils.book_new();\n    XLSX.utils.book_append_sheet(workbook, worksheet, \"Equipements\");\n    XLSX.writeFile(workbook, \"equipements.xlsx\");\n  };\n\n  const handleExportPDF = () => {\n    if (filteredEquipements.length === 0) {\n      alert(\"Aucune donnée à exporter\");\n      return;\n    }\n    \n    const doc = new jsPDF();\n    doc.text(\"Liste des équipements\", 14, 16);\n    doc.autoTable({\n      head: [['Nom', 'Type', 'Numéro série', 'Statut', 'Date achat', 'Fournisseur']],\n      body: filteredEquipements.map(eq => [\n        eq.nom || '',\n        eq.type || '',\n        eq.numero_serie || '',\n        eq.statut || '',\n        eq.date_achat?.split('T')[0] || '',\n        eq.fournisseur?.nom || ''\n      ]),\n      startY: 20\n    });\n    doc.save(\"equipements.pdf\");\n  };\n\n  const handlePrint = () => {\n    window.print();\n  };\n\n  return (\n    <div className=\"equipements-container\">\n      {/* Sidebar */}\n      <div className=\"sidebar\">\n        <div className=\"logo-section\">\n          <h2>Gestion IT</h2>\n        </div>\n        <nav className=\"main-menu\">\n          <ul>\n            <li>\n              <Link to=\"/\" className={location.pathname === '/' ? 'home-active active' : ''}>\n                <FontAwesomeIcon icon={faLaptop} style={{marginRight:8}} /> Accueil\n              </Link>\n            </li>\n            <li>\n              <Link to=\"/equipements\" className={location.pathname === '/equipements' ? 'active' : ''}>\n                <FontAwesomeIcon icon={faLaptop} style={{marginRight:8}} /> Équipements\n              </Link>\n            </li>\n            <li>\n              <Link to=\"/employes\">\n                <FontAwesomeIcon icon={faUsers} style={{marginRight:8}} /> Employés\n              </Link>\n            </li>\n            <li>\n              <Link to=\"/affectations\">\n                <FontAwesomeIcon icon={faExchangeAlt} style={{marginRight:8}} /> Affectations\n              </Link>\n            </li>\n            <li>\n              <Link to=\"/fournisseurs\">\n                <FontAwesomeIcon icon={faTruck} style={{marginRight:8}} /> Fournisseurs\n              </Link>\n            </li>\n          </ul>\n        </nav>\n        <div className=\"secondary-links\">\n          <Link to=\"/profil\">\n            <FontAwesomeIcon icon={faUserCog} style={{marginRight:8}} /> Profil\n          </Link>\n        </div>\n        <div style={{ marginTop: 'auto', padding: '20px', textAlign: 'center' }}>\n          <button className=\"logout-btn\" onClick={() => {\n            localStorage.removeItem('authToken');\n            window.location.href = '/login';\n          }}>\n            <FontAwesomeIcon icon={faSignOutAlt} style={{marginRight:8}} /> Déconnexion\n          </button>\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"equipements-content\">\n        <div className=\"dashboard-header-row\">\n          <div className=\"search-bar\">\n            <FontAwesomeIcon icon={faSearch} className=\"search-icon\" />\n            <input \n              type=\"text\" \n              placeholder=\"Rechercher...\" \n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n            />\n          </div>\n          <div className=\"profile-block\">\n            <div className=\"notification-icon\">\n              <FontAwesomeIcon icon={faBell} />\n              <span>3</span>\n            </div>\n            <span>Admin IT</span>\n            <FontAwesomeIcon icon={faUserCog} />\n          </div>\n        </div>\n\n        <h2 className=\"equipements-title\">\n          Gestion des équipements ({filteredEquipements.length})\n        </h2>\n\n        <div className=\"action-buttons\">\n          <button className=\"btn btn-primary\" onClick={() => setShowModal(true)}>\n            <FontAwesomeIcon icon={faPlus} style={{marginRight:8}} /> Ajouter\n          </button>\n          <button className=\"btn btn-primary\" onClick={handleExportExcel}>\n            Export Excel\n          </button>\n          <button className=\"btn btn-primary\" onClick={handleExportPDF}>\n            Export PDF\n          </button>\n          <button className=\"btn btn-primary\" onClick={handlePrint}>\n            Imprimer\n          </button>\n        </div>\n\n        {/* Tableau des équipements */}\n        {loading ? (\n          <div className=\"loading-message\">\n            <p>Chargement en cours...</p>\n          </div>\n        ) : error ? (\n          <div className=\"error-message\">\n            <p>{error}</p>\n            <button onClick={loadEquipements}>Réessayer</button>\n          </div>\n        ) : filteredEquipements.length === 0 ? (\n          <div className=\"empty-message\">\n            <p>{searchTerm ? \"Aucun résultat trouvé\" : \"Aucun équipement enregistré\"}</p>\n          </div>\n        ) : (\n          <table className=\"equipements-table\">\n            <thead>\n              <tr>\n                <th>Nom</th>\n                <th>Type</th>\n                <th>Numéro série</th>\n                <th>Statut</th>\n                <th>Date achat</th>\n                <th>Fournisseur</th>\n                <th>Actions</th>\n              </tr>\n            </thead>\n            <tbody>\n              {filteredEquipements.map(equipement => (\n                <tr key={equipement.id}>\n                  <td>{equipement.nom}</td>\n                  <td>{equipement.type}</td>\n                  <td>{equipement.numero_serie || '-'}</td>\n                  <td>{equipement.statut}</td>\n                  <td>{equipement.date_achat?.split('T')[0] || '-'}</td>\n                  <td>{equipement.fournisseur?.nom || '-'}</td>\n                  <td className=\"actions-cell\">\n                    <button \n                      className=\"action-btn edit-btn\"\n                      onClick={() => handleEdit(equipement)}\n                      title=\"Modifier\"\n                    >\n                      <FontAwesomeIcon icon={faEdit} />\n                    </button>\n                    <button \n                      className=\"action-btn delete-btn\"\n                      onClick={() => handleDelete(equipement.id)}\n                      title=\"Supprimer\"\n                    >\n                      <FontAwesomeIcon icon={faTrash} />\n                    </button>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        )}\n\n        {/* Modal pour ajout/modification */}\n        {showModal && (\n          <div className=\"modal-overlay\">\n            <div className=\"modal-content\">\n              <div className=\"modal-header\">\n                <h3>{currentEquipement ? 'Modifier équipement' : 'Ajouter un équipement'}</h3>\n                <button onClick={resetForm}>\n                  <FontAwesomeIcon icon={faTimes} />\n                </button>\n              </div>\n\n              <form onSubmit={handleSubmit}>\n                <div className=\"form-grid\">\n                  <div className=\"form-group\">\n                    <label>Nom *</label>\n                    <input\n                      type=\"text\"\n                      name=\"nom\"\n                      value={formData.nom}\n                      onChange={handleInputChange}\n                      required\n                    />\n                  </div>\n\n                  <div className=\"form-group\">\n                    <label>Type *</label>\n                    <select\n                      name=\"type\"\n                      value={formData.type}\n                      onChange={handleInputChange}\n                      required\n                    >\n                      <option value=\"\">Sélectionner...</option>\n                      {typeOptions.map(type => (\n                        <option key={type} value={type}>{type}</option>\n                      ))}\n                    </select>\n                  </div>\n\n                  <div className=\"form-group\">\n                    <label>Numéro de série</label>\n                    <input\n                      type=\"text\"\n                      name=\"numero_serie\"\n                      value={formData.numero_serie}\n                      onChange={handleInputChange}\n                    />\n                  </div>\n\n                  <div className=\"form-group\">\n                    <label>Statut *</label>\n                    <select\n                      name=\"statut\"\n                      value={formData.statut}\n                      onChange={handleInputChange}\n                      required\n                    >\n                      {statutOptions.map(statut => (\n                        <option key={statut} value={statut}>{statut}</option>\n                      ))}\n                    </select>\n                  </div>\n\n                  <div className=\"form-group\">\n                    <label>Date d'achat</label>\n                    <input\n                      type=\"date\"\n                      name=\"date_achat\"\n                      value={formData.date_achat}\n                      onChange={handleInputChange}\n                    />\n                  </div>\n\n                  <div className=\"form-group\">\n                    <label>Fournisseur</label>\n                    <select\n                      name=\"fournisseur_id\"\n                      value={formData.fournisseur_id}\n                      onChange={handleInputChange}\n                    >\n                      <option value=\"\">Sélectionner...</option>\n                      {fournisseurs.map(fournisseur => (\n                        <option key={fournisseur.id} value={fournisseur.id}>\n                          {fournisseur.nom}\n                        </option>\n                      ))}\n                    </select>\n                  </div>\n\n                  <div className=\"form-group\">\n                    <label>Début garantie</label>\n                    <input\n                      type=\"date\"\n                      name=\"date_debut_garantie\"\n                      value={formData.date_debut_garantie}\n                      onChange={handleInputChange}\n                    />\n                  </div>\n\n                  <div className=\"form-group\">\n                    <label>Fin garantie</label>\n                    <input\n                      type=\"date\"\n                      name=\"date_fin_garantie\"\n                      value={formData.date_fin_garantie}\n                      onChange={handleInputChange}\n                    />\n                  </div>\n\n                  <div className=\"form-group\">\n                    <label>Stock actuel</label>\n                    <input\n                      type=\"number\"\n                      name=\"stock_actuel\"\n                      value={formData.stock_actuel}\n                      onChange={handleInputChange}\n                      min=\"0\"\n                    />\n                  </div>\n\n                  <div className=\"form-group\">\n                    <label>Stock max</label>\n                    <input\n                      type=\"number\"\n                      name=\"stock_max\"\n                      value={formData.stock_max}\n                      onChange={handleInputChange}\n                      min=\"1\"\n                    />\n                  </div>\n\n                  <div className=\"form-group\">\n                    <label>Stock min</label>\n                    <input\n                      type=\"number\"\n                      name=\"stock_min\"\n                      value={formData.stock_min}\n                      onChange={handleInputChange}\n                      min=\"0\"\n                    />\n                  </div>\n                </div>\n\n                <div className=\"form-checkbox\">\n                  <label>\n                    <input\n                      type=\"checkbox\"\n                      name=\"en_stock\"\n                      checked={formData.en_stock}\n                      onChange={handleInputChange}\n                    />\n                    En stock\n                  </label>\n                </div>\n\n                <div className=\"form-actions\">\n                  <button type=\"button\" onClick={resetForm}>\n                    Annuler\n                  </button>\n                  <button type=\"submit\" disabled={submitting}>\n                    <FontAwesomeIcon icon={currentEquipement ? faSave : faPlus} style={{marginRight:8}} />\n                    {submitting ? 'En cours...' : currentEquipement ? 'Enregistrer' : 'Ajouter'}\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}"], "mappings": ";;AAAA,SAASA,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SACEC,QAAQ,EAAEC,OAAO,EAAEC,aAAa,EAAEC,OAAO,EACzCC,SAAS,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,MAAM,EACzCC,OAAO,EAAEC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEC,MAAM,QACnC,mCAAmC;AAC1C,SAASC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,iBAAiB;AACxB,OAAO,KAAKC,IAAI,MAAM,MAAM;;AAE5B;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,MAAM,GAAG;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,eAAe,SAASC,WAAWA,CAAA,EAAG;EAAAC,EAAA;EACpC,MAAMC,QAAQ,GAAGxB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACyB,WAAW,EAAEC,cAAc,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACW,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACe,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACiB,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACmB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACqB,YAAY,EAAEC,eAAe,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACuB,UAAU,EAAEC,aAAa,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACyB,UAAU,EAAEC,aAAa,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;;EAEhD;EACA,MAAM,CAAC6B,QAAQ,EAAEC,WAAW,CAAC,GAAG9B,QAAQ,CAAC;IACvC+B,GAAG,EAAE,EAAE;IACPC,IAAI,EAAE,EAAE;IACRC,YAAY,EAAE,EAAE;IAChBC,MAAM,EAAE,YAAY;IACpBC,UAAU,EAAE,EAAE;IACdC,cAAc,EAAE,EAAE;IAClBC,mBAAmB,EAAE,EAAE;IACvBC,iBAAiB,EAAE,EAAE;IACrBC,QAAQ,EAAE,IAAI;IACdC,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE,CAAC;IACZC,SAAS,EAAE,CAAC;IACZC,YAAY,EAAE;EAChB,CAAC,CAAC;;EAEF;EACA,MAAMC,aAAa,GAAG,CAAC,YAAY,EAAE,gBAAgB,EAAE,QAAQ,EAAE,cAAc,CAAC;EAChF,MAAMC,WAAW,GAAG,CAAC,YAAY,EAAE,YAAY,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,SAAS,EAAE,OAAO,CAAC;EAExG9C,SAAS,CAAC,MAAM;IACd;IACA,MAAM+C,YAAY,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;IACpDF,YAAY,CAACG,SAAS,GAAG5C,MAAM;IAC/B0C,QAAQ,CAACG,IAAI,CAACC,WAAW,CAACL,YAAY,CAAC;IAEvCM,eAAe,CAAC,CAAC;IACjBC,gBAAgB,CAAC,CAAC;IAClBC,cAAc,CAAC,CAAC;IAEhB,OAAO,MAAM;MACXP,QAAQ,CAACG,IAAI,CAACK,WAAW,CAACT,YAAY,CAAC;IACzC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN/C,SAAS,CAAC,MAAM;IACd,IAAI4B,UAAU,KAAK,EAAE,EAAE;MACrBf,sBAAsB,CAACH,WAAW,CAAC;IACrC,CAAC,MAAM;MACL,MAAM+C,QAAQ,GAAG/C,WAAW,CAACgD,MAAM,CAACC,UAAU,IAC5CC,MAAM,CAACC,MAAM,CAACF,UAAU,CAAC,CAACG,IAAI,CAC5BC,KAAK,IAAIA,KAAK,IACdA,KAAK,CAACC,QAAQ,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACtC,UAAU,CAACqC,WAAW,CAAC,CAAC,CAClE,CACF,CAAC;MACDpD,sBAAsB,CAAC4C,QAAQ,CAAC;IAClC;EACF,CAAC,EAAE,CAAC7B,UAAU,EAAElB,WAAW,CAAC,CAAC;EAE7B,MAAM2C,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClCtC,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMoD,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;MAC/C,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,uCAAuC,EAAE;QACpEC,OAAO,EAAE;UACP,eAAe,EAAE,UAAUL,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,IAAI,CAACG,QAAQ,CAACG,EAAE,EAAE,MAAM,IAAIC,KAAK,CAAC,sBAAsB,CAAC;MAEzD,MAAMC,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;MAClCjE,cAAc,CAACgE,IAAI,CAAC;MACpB9D,sBAAsB,CAAC8D,IAAI,CAAC;MAC5B1D,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,CAAC,OAAO4D,GAAG,EAAE;MACZ5D,QAAQ,CAAC4D,GAAG,CAACC,OAAO,CAAC;IACvB,CAAC,SAAS;MACR/D,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMuC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAMa,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;MAC/C,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,wCAAwC,EAAE;QACrEC,OAAO,EAAE;UACP,eAAe,EAAE,UAAUL,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,MAAMQ,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;MAClCrD,eAAe,CAACwD,KAAK,CAACC,OAAO,CAACL,IAAI,CAAC,GAAGA,IAAI,GAAG,EAAE,CAAC;IAClD,CAAC,CAAC,OAAOE,GAAG,EAAE;MACZI,OAAO,CAACjE,KAAK,CAAC,iCAAiC,EAAE6D,GAAG,CAAC;IACvD;EACF,CAAC;EAED,MAAMtB,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,MAAMY,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;MAC/C,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,sCAAsC,EAAE;QACnEC,OAAO,EAAE;UACP,eAAe,EAAE,UAAUL,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,MAAMQ,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;MAClCnD,aAAa,CAACsD,KAAK,CAACC,OAAO,CAACL,IAAI,CAAC,GAAGA,IAAI,GAAG,EAAE,CAAC;IAChD,CAAC,CAAC,OAAOE,GAAG,EAAE;MACZI,OAAO,CAACjE,KAAK,CAAC,+BAA+B,EAAE6D,GAAG,CAAC;IACrD;EACF,CAAC;EAED,MAAMK,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAErB,KAAK;MAAE9B,IAAI;MAAEoD;IAAQ,CAAC,GAAGF,CAAC,CAACG,MAAM;IAC/CvD,WAAW,CAACwD,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGnD,IAAI,KAAK,UAAU,GAAGoD,OAAO,GAAGtB;IAC1C,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMyB,YAAY,GAAG,MAAOL,CAAC,IAAK;IAChCA,CAAC,CAACM,cAAc,CAAC,CAAC;IAClB9D,aAAa,CAAC,IAAI,CAAC;IAEnB,IAAI;MACF,MAAMwC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;MAC/C,MAAMqB,MAAM,GAAGtE,iBAAiB,GAAG,KAAK,GAAG,MAAM;MACjD,MAAMuE,GAAG,GAAGvE,iBAAiB,GACzB,yCAAyCA,iBAAiB,CAACwE,EAAE,EAAE,GAC/D,uCAAuC;MAE3C,MAAMtB,QAAQ,GAAG,MAAMC,KAAK,CAACoB,GAAG,EAAE;QAChCD,MAAM;QACNlB,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAE,UAAUL,KAAK;QAClC,CAAC;QACD0B,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACjE,QAAQ;MAC/B,CAAC,CAAC;MAEF,IAAI,CAACwC,QAAQ,CAACG,EAAE,EAAE,MAAM,IAAIC,KAAK,CAAC,2BAA2B,CAAC;MAE9D,MAAMsB,MAAM,GAAG,MAAM1B,QAAQ,CAACM,IAAI,CAAC,CAAC;MAEpC,IAAIxD,iBAAiB,EAAE;QACrBT,cAAc,CAAC4E,IAAI,IACjBA,IAAI,CAACU,GAAG,CAACC,EAAE,IAAIA,EAAE,CAACN,EAAE,KAAKxE,iBAAiB,CAACwE,EAAE,GAAGI,MAAM,GAAGE,EAAE,CAC7D,CAAC;QACDC,KAAK,CAAC,iCAAiC,CAAC;MAC1C,CAAC,MAAM;QACLxF,cAAc,CAAC4E,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAES,MAAM,CAAC,CAAC;QACzCG,KAAK,CAAC,gCAAgC,CAAC;MACzC;MAEAC,SAAS,CAAC,CAAC;MACX/C,eAAe,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOwB,GAAG,EAAE;MACZI,OAAO,CAACjE,KAAK,CAAC,SAAS,EAAE6D,GAAG,CAAC;MAC7BsB,KAAK,CAAC,WAAWtB,GAAG,CAACC,OAAO,EAAE,CAAC;IACjC,CAAC,SAAS;MACRnD,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAM0E,UAAU,GAAI1C,UAAU,IAAK;IAAA,IAAA2C,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;IACjCpF,oBAAoB,CAACsC,UAAU,CAAC;IAChC5B,WAAW,CAAC;MACVC,GAAG,EAAE2B,UAAU,CAAC3B,GAAG;MACnBC,IAAI,EAAE0B,UAAU,CAAC1B,IAAI;MACrBC,YAAY,EAAEyB,UAAU,CAACzB,YAAY;MACrCC,MAAM,EAAEwB,UAAU,CAACxB,MAAM;MACzBC,UAAU,EAAE,EAAAkE,qBAAA,GAAA3C,UAAU,CAACvB,UAAU,cAAAkE,qBAAA,uBAArBA,qBAAA,CAAuBI,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAI,EAAE;MACtDrE,cAAc,EAAE,EAAAkE,qBAAA,GAAA5C,UAAU,CAACgD,WAAW,cAAAJ,qBAAA,uBAAtBA,qBAAA,CAAwBX,EAAE,KAAI,EAAE;MAChDtD,mBAAmB,EAAE,EAAAkE,qBAAA,GAAA7C,UAAU,CAACrB,mBAAmB,cAAAkE,qBAAA,uBAA9BA,qBAAA,CAAgCE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAI,EAAE;MACxEnE,iBAAiB,EAAE,EAAAkE,qBAAA,GAAA9C,UAAU,CAACpB,iBAAiB,cAAAkE,qBAAA,uBAA5BA,qBAAA,CAA8BC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAI,EAAE;MACpElE,QAAQ,EAAEmB,UAAU,CAACnB,QAAQ;MAC7BC,YAAY,EAAEkB,UAAU,CAAClB,YAAY;MACrCC,SAAS,EAAEiB,UAAU,CAACjB,SAAS;MAC/BC,SAAS,EAAEgB,UAAU,CAAChB,SAAS;MAC/BC,YAAY,EAAEe,UAAU,CAACf;IAC3B,CAAC,CAAC;IACFzB,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMyF,YAAY,GAAG,MAAOhB,EAAE,IAAK;IACjC,IAAI,CAACiB,MAAM,CAACC,OAAO,CAAC,oDAAoD,CAAC,EAAE;IAE3E,IAAI;MACF,MAAM3C,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;MAC/C,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,yCAAyCqB,EAAE,EAAE,EAAE;QAC1EF,MAAM,EAAE,QAAQ;QAChBlB,OAAO,EAAE;UACP,eAAe,EAAE,UAAUL,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,IAAI,CAACG,QAAQ,CAACG,EAAE,EAAE,MAAM,IAAIC,KAAK,CAAC,+BAA+B,CAAC;MAElE/D,cAAc,CAAC4E,IAAI,IAAIA,IAAI,CAAC7B,MAAM,CAACwC,EAAE,IAAIA,EAAE,CAACN,EAAE,KAAKA,EAAE,CAAC,CAAC;MACvDO,KAAK,CAAC,kCAAkC,CAAC;IAC3C,CAAC,CAAC,OAAOtB,GAAG,EAAE;MACZI,OAAO,CAACjE,KAAK,CAAC,SAAS,EAAE6D,GAAG,CAAC;MAC7BsB,KAAK,CAAC,kCAAkCtB,GAAG,CAACC,OAAO,EAAE,CAAC;IACxD;EACF,CAAC;EAED,MAAMsB,SAAS,GAAGA,CAAA,KAAM;IACtBrE,WAAW,CAAC;MACVC,GAAG,EAAE,EAAE;MACPC,IAAI,EAAE,EAAE;MACRC,YAAY,EAAE,EAAE;MAChBC,MAAM,EAAE,YAAY;MACpBC,UAAU,EAAE,EAAE;MACdC,cAAc,EAAE,EAAE;MAClBC,mBAAmB,EAAE,EAAE;MACvBC,iBAAiB,EAAE,EAAE;MACrBC,QAAQ,EAAE,IAAI;MACdC,YAAY,EAAE,CAAC;MACfC,SAAS,EAAE,CAAC;MACZC,SAAS,EAAE,CAAC;MACZC,YAAY,EAAE;IAChB,CAAC,CAAC;IACFvB,oBAAoB,CAAC,IAAI,CAAC;IAC1BF,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,MAAM4F,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAInG,mBAAmB,CAACoG,MAAM,KAAK,CAAC,EAAE;MACpCb,KAAK,CAAC,0BAA0B,CAAC;MACjC;IACF;IAEA,MAAMc,SAAS,GAAG9G,IAAI,CAAC+G,KAAK,CAACC,aAAa,CAACvG,mBAAmB,CAAC;IAC/D,MAAMwG,QAAQ,GAAGjH,IAAI,CAAC+G,KAAK,CAACG,QAAQ,CAAC,CAAC;IACtClH,IAAI,CAAC+G,KAAK,CAACI,iBAAiB,CAACF,QAAQ,EAAEH,SAAS,EAAE,aAAa,CAAC;IAChE9G,IAAI,CAACoH,SAAS,CAACH,QAAQ,EAAE,kBAAkB,CAAC;EAC9C,CAAC;EAED,MAAMI,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI5G,mBAAmB,CAACoG,MAAM,KAAK,CAAC,EAAE;MACpCb,KAAK,CAAC,0BAA0B,CAAC;MACjC;IACF;IAEA,MAAMsB,GAAG,GAAG,IAAIvH,KAAK,CAAC,CAAC;IACvBuH,GAAG,CAACC,IAAI,CAAC,uBAAuB,EAAE,EAAE,EAAE,EAAE,CAAC;IACzCD,GAAG,CAACE,SAAS,CAAC;MACZxE,IAAI,EAAE,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE,cAAc,EAAE,QAAQ,EAAE,YAAY,EAAE,aAAa,CAAC,CAAC;MAC9E0C,IAAI,EAAEjF,mBAAmB,CAACqF,GAAG,CAACC,EAAE;QAAA,IAAA0B,cAAA,EAAAC,eAAA;QAAA,OAAI,CAClC3B,EAAE,CAAClE,GAAG,IAAI,EAAE,EACZkE,EAAE,CAACjE,IAAI,IAAI,EAAE,EACbiE,EAAE,CAAChE,YAAY,IAAI,EAAE,EACrBgE,EAAE,CAAC/D,MAAM,IAAI,EAAE,EACf,EAAAyF,cAAA,GAAA1B,EAAE,CAAC9D,UAAU,cAAAwF,cAAA,uBAAbA,cAAA,CAAelB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAI,EAAE,EAClC,EAAAmB,eAAA,GAAA3B,EAAE,CAACS,WAAW,cAAAkB,eAAA,uBAAdA,eAAA,CAAgB7F,GAAG,KAAI,EAAE,CAC1B;MAAA,EAAC;MACF8F,MAAM,EAAE;IACV,CAAC,CAAC;IACFL,GAAG,CAACM,IAAI,CAAC,iBAAiB,CAAC;EAC7B,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxBnB,MAAM,CAACoB,KAAK,CAAC,CAAC;EAChB,CAAC;EAED,oBACE5H,OAAA;IAAK6H,SAAS,EAAC,uBAAuB;IAAAC,QAAA,gBAEpC9H,OAAA;MAAK6H,SAAS,EAAC,SAAS;MAAAC,QAAA,gBACtB9H,OAAA;QAAK6H,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3B9H,OAAA;UAAA8H,QAAA,EAAI;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC,eACNlI,OAAA;QAAK6H,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxB9H,OAAA;UAAA8H,QAAA,gBACE9H,OAAA;YAAA8H,QAAA,eACE9H,OAAA,CAACrB,IAAI;cAACwJ,EAAE,EAAC,GAAG;cAACN,SAAS,EAAEzH,QAAQ,CAACgI,QAAQ,KAAK,GAAG,GAAG,oBAAoB,GAAG,EAAG;cAAAN,QAAA,gBAC5E9H,OAAA,CAACnB,eAAe;gBAACwJ,IAAI,EAAEvJ,QAAS;gBAACwJ,KAAK,EAAE;kBAACC,WAAW,EAAC;gBAAC;cAAE;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,YAC7D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACLlI,OAAA;YAAA8H,QAAA,eACE9H,OAAA,CAACrB,IAAI;cAACwJ,EAAE,EAAC,cAAc;cAACN,SAAS,EAAEzH,QAAQ,CAACgI,QAAQ,KAAK,cAAc,GAAG,QAAQ,GAAG,EAAG;cAAAN,QAAA,gBACtF9H,OAAA,CAACnB,eAAe;gBAACwJ,IAAI,EAAEvJ,QAAS;gBAACwJ,KAAK,EAAE;kBAACC,WAAW,EAAC;gBAAC;cAAE;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,mBAC7D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACLlI,OAAA;YAAA8H,QAAA,eACE9H,OAAA,CAACrB,IAAI;cAACwJ,EAAE,EAAC,WAAW;cAAAL,QAAA,gBAClB9H,OAAA,CAACnB,eAAe;gBAACwJ,IAAI,EAAEtJ,OAAQ;gBAACuJ,KAAK,EAAE;kBAACC,WAAW,EAAC;gBAAC;cAAE;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAC5D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACLlI,OAAA;YAAA8H,QAAA,eACE9H,OAAA,CAACrB,IAAI;cAACwJ,EAAE,EAAC,eAAe;cAAAL,QAAA,gBACtB9H,OAAA,CAACnB,eAAe;gBAACwJ,IAAI,EAAErJ,aAAc;gBAACsJ,KAAK,EAAE;kBAACC,WAAW,EAAC;gBAAC;cAAE;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,iBAClE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACLlI,OAAA;YAAA8H,QAAA,eACE9H,OAAA,CAACrB,IAAI;cAACwJ,EAAE,EAAC,eAAe;cAAAL,QAAA,gBACtB9H,OAAA,CAACnB,eAAe;gBAACwJ,IAAI,EAAEpJ,OAAQ;gBAACqJ,KAAK,EAAE;kBAACC,WAAW,EAAC;gBAAC;cAAE;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,iBAC5D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACNlI,OAAA;QAAK6H,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9B9H,OAAA,CAACrB,IAAI;UAACwJ,EAAE,EAAC,SAAS;UAAAL,QAAA,gBAChB9H,OAAA,CAACnB,eAAe;YAACwJ,IAAI,EAAEnJ,SAAU;YAACoJ,KAAK,EAAE;cAACC,WAAW,EAAC;YAAC;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,WAC9D;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNlI,OAAA;QAAKsI,KAAK,EAAE;UAAEE,SAAS,EAAE,MAAM;UAAEC,OAAO,EAAE,MAAM;UAAEC,SAAS,EAAE;QAAS,CAAE;QAAAZ,QAAA,eACtE9H,OAAA;UAAQ6H,SAAS,EAAC,YAAY;UAACc,OAAO,EAAEA,CAAA,KAAM;YAC5C5E,YAAY,CAAC6E,UAAU,CAAC,WAAW,CAAC;YACpCpC,MAAM,CAACpG,QAAQ,CAACyI,IAAI,GAAG,QAAQ;UACjC,CAAE;UAAAf,QAAA,gBACA9H,OAAA,CAACnB,eAAe;YAACwJ,IAAI,EAAElJ,YAAa;YAACmJ,KAAK,EAAE;cAACC,WAAW,EAAC;YAAC;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,mBACjE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlI,OAAA;MAAK6H,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClC9H,OAAA;QAAK6H,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACnC9H,OAAA;UAAK6H,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB9H,OAAA,CAACnB,eAAe;YAACwJ,IAAI,EAAEjJ,QAAS;YAACyI,SAAS,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3DlI,OAAA;YACE4B,IAAI,EAAC,MAAM;YACXkH,WAAW,EAAC,eAAe;YAC3BpF,KAAK,EAAEnC,UAAW;YAClBwH,QAAQ,EAAGjE,CAAC,IAAKtD,aAAa,CAACsD,CAAC,CAACG,MAAM,CAACvB,KAAK;UAAE;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNlI,OAAA;UAAK6H,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B9H,OAAA;YAAK6H,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChC9H,OAAA,CAACnB,eAAe;cAACwJ,IAAI,EAAEhJ;YAAO;cAAA0I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjClI,OAAA;cAAA8H,QAAA,EAAM;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eACNlI,OAAA;YAAA8H,QAAA,EAAM;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACrBlI,OAAA,CAACnB,eAAe;YAACwJ,IAAI,EAAEnJ;UAAU;YAAA6I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENlI,OAAA;QAAI6H,SAAS,EAAC,mBAAmB;QAAAC,QAAA,GAAC,8BACP,EAACvH,mBAAmB,CAACoG,MAAM,EAAC,GACvD;MAAA;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELlI,OAAA;QAAK6H,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B9H,OAAA;UAAQ6H,SAAS,EAAC,iBAAiB;UAACc,OAAO,EAAEA,CAAA,KAAM7H,YAAY,CAAC,IAAI,CAAE;UAAAgH,QAAA,gBACpE9H,OAAA,CAACnB,eAAe;YAACwJ,IAAI,EAAE9I,MAAO;YAAC+I,KAAK,EAAE;cAACC,WAAW,EAAC;YAAC;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,YAC3D;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTlI,OAAA;UAAQ6H,SAAS,EAAC,iBAAiB;UAACc,OAAO,EAAEjC,iBAAkB;UAAAoB,QAAA,EAAC;QAEhE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTlI,OAAA;UAAQ6H,SAAS,EAAC,iBAAiB;UAACc,OAAO,EAAExB,eAAgB;UAAAW,QAAA,EAAC;QAE9D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTlI,OAAA;UAAQ6H,SAAS,EAAC,iBAAiB;UAACc,OAAO,EAAEhB,WAAY;UAAAG,QAAA,EAAC;QAE1D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGLzH,OAAO,gBACNT,OAAA;QAAK6H,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9B9H,OAAA;UAAA8H,QAAA,EAAG;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,GACJvH,KAAK,gBACPX,OAAA;QAAK6H,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5B9H,OAAA;UAAA8H,QAAA,EAAInH;QAAK;UAAAoH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACdlI,OAAA;UAAQ2I,OAAO,EAAE3F,eAAgB;UAAA8E,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC,GACJ3H,mBAAmB,CAACoG,MAAM,KAAK,CAAC,gBAClC3G,OAAA;QAAK6H,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC5B9H,OAAA;UAAA8H,QAAA,EAAIvG,UAAU,GAAG,uBAAuB,GAAG;QAA6B;UAAAwG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1E,CAAC,gBAENlI,OAAA;QAAO6H,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAClC9H,OAAA;UAAA8H,QAAA,eACE9H,OAAA;YAAA8H,QAAA,gBACE9H,OAAA;cAAA8H,QAAA,EAAI;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACZlI,OAAA;cAAA8H,QAAA,EAAI;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACblI,OAAA;cAAA8H,QAAA,EAAI;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrBlI,OAAA;cAAA8H,QAAA,EAAI;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACflI,OAAA;cAAA8H,QAAA,EAAI;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnBlI,OAAA;cAAA8H,QAAA,EAAI;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpBlI,OAAA;cAAA8H,QAAA,EAAI;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACRlI,OAAA;UAAA8H,QAAA,EACGvH,mBAAmB,CAACqF,GAAG,CAACtC,UAAU;YAAA,IAAA0F,sBAAA,EAAAC,sBAAA;YAAA,oBACjCjJ,OAAA;cAAA8H,QAAA,gBACE9H,OAAA;gBAAA8H,QAAA,EAAKxE,UAAU,CAAC3B;cAAG;gBAAAoG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACzBlI,OAAA;gBAAA8H,QAAA,EAAKxE,UAAU,CAAC1B;cAAI;gBAAAmG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC1BlI,OAAA;gBAAA8H,QAAA,EAAKxE,UAAU,CAACzB,YAAY,IAAI;cAAG;gBAAAkG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACzClI,OAAA;gBAAA8H,QAAA,EAAKxE,UAAU,CAACxB;cAAM;gBAAAiG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC5BlI,OAAA;gBAAA8H,QAAA,EAAK,EAAAkB,sBAAA,GAAA1F,UAAU,CAACvB,UAAU,cAAAiH,sBAAA,uBAArBA,sBAAA,CAAuB3C,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAI;cAAG;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtDlI,OAAA;gBAAA8H,QAAA,EAAK,EAAAmB,sBAAA,GAAA3F,UAAU,CAACgD,WAAW,cAAA2C,sBAAA,uBAAtBA,sBAAA,CAAwBtH,GAAG,KAAI;cAAG;gBAAAoG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC7ClI,OAAA;gBAAI6H,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC1B9H,OAAA;kBACE6H,SAAS,EAAC,qBAAqB;kBAC/Bc,OAAO,EAAEA,CAAA,KAAM3C,UAAU,CAAC1C,UAAU,CAAE;kBACtC4F,KAAK,EAAC,UAAU;kBAAApB,QAAA,eAEhB9H,OAAA,CAACnB,eAAe;oBAACwJ,IAAI,EAAE7I;kBAAO;oBAAAuI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,eACTlI,OAAA;kBACE6H,SAAS,EAAC,uBAAuB;kBACjCc,OAAO,EAAEA,CAAA,KAAMpC,YAAY,CAACjD,UAAU,CAACiC,EAAE,CAAE;kBAC3C2D,KAAK,EAAC,WAAW;kBAAApB,QAAA,eAEjB9H,OAAA,CAACnB,eAAe;oBAACwJ,IAAI,EAAE5I;kBAAQ;oBAAAsI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC;YAAA,GAtBE5E,UAAU,CAACiC,EAAE;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAuBlB,CAAC;UAAA,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACR,EAGArH,SAAS,iBACRb,OAAA;QAAK6H,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC5B9H,OAAA;UAAK6H,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B9H,OAAA;YAAK6H,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B9H,OAAA;cAAA8H,QAAA,EAAK/G,iBAAiB,GAAG,qBAAqB,GAAG;YAAuB;cAAAgH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC9ElI,OAAA;cAAQ2I,OAAO,EAAE5C,SAAU;cAAA+B,QAAA,eACzB9H,OAAA,CAACnB,eAAe;gBAACwJ,IAAI,EAAE/I;cAAQ;gBAAAyI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENlI,OAAA;YAAMmJ,QAAQ,EAAEhE,YAAa;YAAA2C,QAAA,gBAC3B9H,OAAA;cAAK6H,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB9H,OAAA;gBAAK6H,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB9H,OAAA;kBAAA8H,QAAA,EAAO;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACpBlI,OAAA;kBACE4B,IAAI,EAAC,MAAM;kBACXmD,IAAI,EAAC,KAAK;kBACVrB,KAAK,EAAEjC,QAAQ,CAACE,GAAI;kBACpBoH,QAAQ,EAAElE,iBAAkB;kBAC5BuE,QAAQ;gBAAA;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENlI,OAAA;gBAAK6H,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB9H,OAAA;kBAAA8H,QAAA,EAAO;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrBlI,OAAA;kBACE+E,IAAI,EAAC,MAAM;kBACXrB,KAAK,EAAEjC,QAAQ,CAACG,IAAK;kBACrBmH,QAAQ,EAAElE,iBAAkB;kBAC5BuE,QAAQ;kBAAAtB,QAAA,gBAER9H,OAAA;oBAAQ0D,KAAK,EAAC,EAAE;oBAAAoE,QAAA,EAAC;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACxCzF,WAAW,CAACmD,GAAG,CAAChE,IAAI,iBACnB5B,OAAA;oBAAmB0D,KAAK,EAAE9B,IAAK;oBAAAkG,QAAA,EAAElG;kBAAI,GAAxBA,IAAI;oBAAAmG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAA6B,CAC/C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAENlI,OAAA;gBAAK6H,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB9H,OAAA;kBAAA8H,QAAA,EAAO;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9BlI,OAAA;kBACE4B,IAAI,EAAC,MAAM;kBACXmD,IAAI,EAAC,cAAc;kBACnBrB,KAAK,EAAEjC,QAAQ,CAACI,YAAa;kBAC7BkH,QAAQ,EAAElE;gBAAkB;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENlI,OAAA;gBAAK6H,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB9H,OAAA;kBAAA8H,QAAA,EAAO;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACvBlI,OAAA;kBACE+E,IAAI,EAAC,QAAQ;kBACbrB,KAAK,EAAEjC,QAAQ,CAACK,MAAO;kBACvBiH,QAAQ,EAAElE,iBAAkB;kBAC5BuE,QAAQ;kBAAAtB,QAAA,EAEPtF,aAAa,CAACoD,GAAG,CAAC9D,MAAM,iBACvB9B,OAAA;oBAAqB0D,KAAK,EAAE5B,MAAO;oBAAAgG,QAAA,EAAEhG;kBAAM,GAA9BA,MAAM;oBAAAiG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAiC,CACrD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAENlI,OAAA;gBAAK6H,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB9H,OAAA;kBAAA8H,QAAA,EAAO;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3BlI,OAAA;kBACE4B,IAAI,EAAC,MAAM;kBACXmD,IAAI,EAAC,YAAY;kBACjBrB,KAAK,EAAEjC,QAAQ,CAACM,UAAW;kBAC3BgH,QAAQ,EAAElE;gBAAkB;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENlI,OAAA;gBAAK6H,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB9H,OAAA;kBAAA8H,QAAA,EAAO;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1BlI,OAAA;kBACE+E,IAAI,EAAC,gBAAgB;kBACrBrB,KAAK,EAAEjC,QAAQ,CAACO,cAAe;kBAC/B+G,QAAQ,EAAElE,iBAAkB;kBAAAiD,QAAA,gBAE5B9H,OAAA;oBAAQ0D,KAAK,EAAC,EAAE;oBAAAoE,QAAA,EAAC;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACxCjH,YAAY,CAAC2E,GAAG,CAACU,WAAW,iBAC3BtG,OAAA;oBAA6B0D,KAAK,EAAE4C,WAAW,CAACf,EAAG;oBAAAuC,QAAA,EAChDxB,WAAW,CAAC3E;kBAAG,GADL2E,WAAW,CAACf,EAAE;oBAAAwC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEnB,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAENlI,OAAA;gBAAK6H,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB9H,OAAA;kBAAA8H,QAAA,EAAO;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC7BlI,OAAA;kBACE4B,IAAI,EAAC,MAAM;kBACXmD,IAAI,EAAC,qBAAqB;kBAC1BrB,KAAK,EAAEjC,QAAQ,CAACQ,mBAAoB;kBACpC8G,QAAQ,EAAElE;gBAAkB;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENlI,OAAA;gBAAK6H,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB9H,OAAA;kBAAA8H,QAAA,EAAO;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3BlI,OAAA;kBACE4B,IAAI,EAAC,MAAM;kBACXmD,IAAI,EAAC,mBAAmB;kBACxBrB,KAAK,EAAEjC,QAAQ,CAACS,iBAAkB;kBAClC6G,QAAQ,EAAElE;gBAAkB;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENlI,OAAA;gBAAK6H,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB9H,OAAA;kBAAA8H,QAAA,EAAO;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3BlI,OAAA;kBACE4B,IAAI,EAAC,QAAQ;kBACbmD,IAAI,EAAC,cAAc;kBACnBrB,KAAK,EAAEjC,QAAQ,CAACW,YAAa;kBAC7B2G,QAAQ,EAAElE,iBAAkB;kBAC5BwE,GAAG,EAAC;gBAAG;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENlI,OAAA;gBAAK6H,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB9H,OAAA;kBAAA8H,QAAA,EAAO;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxBlI,OAAA;kBACE4B,IAAI,EAAC,QAAQ;kBACbmD,IAAI,EAAC,WAAW;kBAChBrB,KAAK,EAAEjC,QAAQ,CAACY,SAAU;kBAC1B0G,QAAQ,EAAElE,iBAAkB;kBAC5BwE,GAAG,EAAC;gBAAG;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENlI,OAAA;gBAAK6H,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB9H,OAAA;kBAAA8H,QAAA,EAAO;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxBlI,OAAA;kBACE4B,IAAI,EAAC,QAAQ;kBACbmD,IAAI,EAAC,WAAW;kBAChBrB,KAAK,EAAEjC,QAAQ,CAACa,SAAU;kBAC1ByG,QAAQ,EAAElE,iBAAkB;kBAC5BwE,GAAG,EAAC;gBAAG;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENlI,OAAA;cAAK6H,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5B9H,OAAA;gBAAA8H,QAAA,gBACE9H,OAAA;kBACE4B,IAAI,EAAC,UAAU;kBACfmD,IAAI,EAAC,UAAU;kBACfC,OAAO,EAAEvD,QAAQ,CAACU,QAAS;kBAC3B4G,QAAQ,EAAElE;gBAAkB;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC,YAEJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAENlI,OAAA;cAAK6H,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B9H,OAAA;gBAAQ4B,IAAI,EAAC,QAAQ;gBAAC+G,OAAO,EAAE5C,SAAU;gBAAA+B,QAAA,EAAC;cAE1C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTlI,OAAA;gBAAQ4B,IAAI,EAAC,QAAQ;gBAAC0H,QAAQ,EAAEjI,UAAW;gBAAAyG,QAAA,gBACzC9H,OAAA,CAACnB,eAAe;kBAACwJ,IAAI,EAAEtH,iBAAiB,GAAGrB,MAAM,GAAGH,MAAO;kBAAC+I,KAAK,EAAE;oBAACC,WAAW,EAAC;kBAAC;gBAAE;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACrF7G,UAAU,GAAG,aAAa,GAAGN,iBAAiB,GAAG,aAAa,GAAG,SAAS;cAAA;gBAAAgH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC/H,EAAA,CAplBuBD,WAAW;EAAA,QAChBtB,WAAW;AAAA;AAAA2K,EAAA,GADNrJ,WAAW;AAAA,IAAAqJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}