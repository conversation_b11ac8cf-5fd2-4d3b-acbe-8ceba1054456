import './Equipements.css';
import { Link, useLocation } from 'react-router-dom';
import logo from '../assets/images/asment logo.jpg';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faLaptop, faUsers, faExchangeAlt, faTruck, faUserCog, faSignOutAlt, faSearch, faBell, faTimes, faPlus, faEdit, faTrash } from '@fortawesome/free-solid-svg-icons';
import { useEffect, useState } from "react";
import jsPDF from "jspdf";
import autoTable from "jspdf-autotable";
import * as XLSX from "xlsx";
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement } from 'chart.js';
import { Bar, Doughnut } from 'react-chartjs-2';

ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement);

export default function Equipements() {
  const location = useLocation();
  const [equipements, setEquipements] = useState([]);
  const [filteredEquipements, setFilteredEquipements] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [fournisseurs, setFournisseurs] = useState([]);
  const [categories, setCategories] = useState([]);
  const [submitting, setSubmitting] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [editingId, setEditingId] = useState(null);
  const [showStats, setShowStats] = useState(false);

  // État du formulaire
  const [formData, setFormData] = useState({
    nom: '',
    type: '',
    numero_serie: '',
    statut: 'DISPONIBLE',
    date_achat: '',
    fournisseur_id: '',
    date_debut_garantie: '',
    date_fin_garantie: '',
    en_stock: true,
    stock_actuel: 1,
    stock_max: 1,
    stock_min: 1,
    categorie_id: ''
  });

  // Options pour les statuts et types
  const statutOptions = ['DISPONIBLE', 'EN_MAINTENANCE', 'OCCUPE', 'HORS_SERVICE'];
  const typeOptions = ['ORDINATEUR', 'IMPRIMANTE', 'SCANNER', 'TELEPHONE', 'TABLETTE', 'SERVEUR', 'AUTRE'];

  useEffect(() => {
    loadEquipements();
    loadFournisseurs();
    loadCategories();
  }, []);

  useEffect(() => {
    if (searchTerm === '') {
      setFilteredEquipements(equipements);
    } else {
      const filtered = equipements.filter(equipement =>
        equipement.nom.toLowerCase().includes(searchTerm.toLowerCase()) ||
        equipement.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (equipement.numero_serie && equipement.numero_serie.toLowerCase().includes(searchTerm.toLowerCase())) ||
        equipement.statut.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (equipement.fournisseur?.nom && equipement.fournisseur.nom.toLowerCase().includes(searchTerm.toLowerCase()))
      );
      setFilteredEquipements(filtered);
    }
  }, [searchTerm, equipements]);

  const loadEquipements = () => {
    console.log("Chargement des équipements...");

    const token = localStorage.getItem('authToken');
    const headers = {
      'Content-Type': 'application/json',
    };
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    fetch("http://localhost:8081/api/equipements", {
      method: 'GET',
      headers: headers
    })
      .then(res => {
        console.log("Status de la réponse équipements:", res.status);
        if (res.status === 500) {
          console.error("Erreur 500 du serveur backend - Utilisation de données de test");
          // Utiliser des données de test en cas d'erreur 500
          return [
            {
              id: 1,
              nom: "Ordinateur portable Dell",
              type: "ORDINATEUR",
              numero_serie: "DL001",
              statut: "DISPONIBLE",
              date_achat: "2024-01-15",
              date_fin_garantie: "2027-01-15",
              fournisseur: { id: 1, nom: "Dell Technologies" }
            },
            {
              id: 2,
              nom: "Imprimante HP LaserJet",
              type: "IMPRIMANTE",
              numero_serie: "HP002",
              statut: "EN_MAINTENANCE",
              date_achat: "2024-02-10",
              date_fin_garantie: "2026-02-10",
              fournisseur: { id: 2, nom: "HP Inc." }
            }
          ];
        }
        if (!res.ok) {
          throw new Error(`Erreur HTTP: ${res.status}`);
        }
        return res.json();
      })
      .then(data => {
        console.log("Données équipements reçues:", data);
        if (Array.isArray(data)) {
          setEquipements(data);
          setFilteredEquipements(data);
          setError(null);
        } else {
          console.warn("Les données équipements ne sont pas un tableau:", data);
          setEquipements([]);
          setFilteredEquipements([]);
          setError("Format de données incorrect");
        }
        setLoading(false);
      })
      .catch(err => {
        console.error("Erreur lors du chargement des équipements:", err);
        console.log("Utilisation de données de test en cas d'erreur");

        // Données de test en cas d'erreur
        const testData = [
          {
            id: 1,
            nom: "Ordinateur portable Dell (Test)",
            type: "ORDINATEUR",
            numero_serie: "TEST001",
            statut: "DISPONIBLE",
            date_achat: "2024-01-15",
            date_fin_garantie: "2027-01-15",
            fournisseur: { id: 1, nom: "Dell Technologies" }
          }
        ];

        setEquipements(testData);
        setFilteredEquipements(testData);
        setError("⚠️ Erreur backend - Données de test affichées. Vérifiez le serveur backend.");
        setLoading(false);
      });
  };

  const loadFournisseurs = () => {
    console.log("Chargement des fournisseurs...");

    const token = localStorage.getItem('authToken');
    const headers = {
      'Content-Type': 'application/json',
    };
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    fetch("http://localhost:8081/api/fournisseurs", {
      method: 'GET',
      headers: headers
    })
      .then(res => {
        console.log("Status de la réponse fournisseurs:", res.status);
        if (!res.ok) {
          throw new Error(`Erreur HTTP: ${res.status}`);
        }
        return res.json();
      })
      .then(data => {
        console.log("Données fournisseurs reçues:", data);
        if (Array.isArray(data)) {
          setFournisseurs(data);
          console.log("Fournisseurs chargés:", data.length, "éléments");
        } else {
          console.warn("Les données fournisseurs ne sont pas un tableau:", data);
          setFournisseurs([]);
        }
      })
      .catch(err => {
        console.error("Erreur lors du chargement des fournisseurs:", err);
        setFournisseurs([]);
      });
  };

  const loadCategories = () => {
    const token = localStorage.getItem('authToken');
    const headers = {
      'Content-Type': 'application/json',
    };
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    fetch("http://localhost:8081/api/categories", {
      method: 'GET',
      headers: headers
    })
      .then(res => res.ok ? res.json() : [])
      .then(data => setCategories(Array.isArray(data) ? data : []))
      .catch(err => {
        console.error("Erreur lors du chargement des catégories:", err);
        setCategories([]);
      });
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSubmitting(true);

    console.log("handleSubmit - editingId:", editingId);
    console.log("handleSubmit - formData:", formData);

    const dataToSend = {
      ...formData,
      fournisseur_id: formData.fournisseur_id || null
    };

    try {
      const token = localStorage.getItem('authToken');
      const headers = {
        'Content-Type': 'application/json',
      };
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const url = editingId
        ? `http://localhost:8081/api/equipements/${editingId}`
        : "http://localhost:8081/api/equipements";

      console.log("URL générée:", url);
      const method = editingId ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method: method,
        headers: headers,
        body: JSON.stringify(dataToSend)
      });

      if (!response.ok) {
        throw new Error(`Erreur HTTP: ${response.status}`);
      }

      const result = await response.json();
      
      if (editingId) {
        setEquipements(prev => prev.map(eq => String(eq.id).split(':')[0] === String(editingId).split(':')[0] ? result : eq));
        setFilteredEquipements(prev => prev.map(eq => String(eq.id).split(':')[0] === String(editingId).split(':')[0] ? result : eq));
      } else {
        setEquipements(prev => [...prev, result]);
        setFilteredEquipements(prev => [...prev, result]);
      }
      
      setFormData({
        nom: '',
        type: '',
        numero_serie: '',
        statut: 'DISPONIBLE',
        date_achat: '',
        fournisseur_id: '',
        date_debut_garantie: '',
        date_fin_garantie: '',
        en_stock: true,
        stock_actuel: 1,
        stock_max: 1,
        stock_min: 1,
        categorie_id: ''
      });
      setShowModal(false);
      setEditingId(null);
      
      alert(editingId ? 'Équipement modifié avec succès !' : 'Équipement ajouté avec succès !');
    } catch (err) {
      console.error("Erreur lors de l'opération:", err);
      alert(`Erreur: ${err.message}`);
    } finally {
      setSubmitting(false);
    }
  };

  const handleEdit = (equipement) => {
    console.log("handleEdit - equipement complet:", equipement);
    console.log("handleEdit - equipement.id:", equipement.id);

    // S'assurer que l'ID est bien un nombre ou une chaîne simple
    const cleanId = String(equipement.id).split(':')[0];
    console.log("handleEdit - cleanId:", cleanId);

    setEditingId(cleanId);
    setFormData({
      nom: equipement.nom,
      type: equipement.type,
      numero_serie: equipement.numero_serie || equipement.numeroSerie || '',
      statut: equipement.statut,
      date_achat: equipement.date_achat || '',
      fournisseur_id: equipement.fournisseur?.id || equipement.fournisseur_id || '',
      date_debut_garantie: equipement.date_debut_garantie || '',
      date_fin_garantie: equipement.date_fin_garantie || '',
      en_stock: equipement.en_stock !== undefined ? equipement.en_stock : true,
      stock_actuel: equipement.stock_actuel || 1,
      stock_max: equipement.stock_max || 1,
      stock_min: equipement.stock_min || 1,
      categorie_id: equipement.categorie_id || ''
    });
    setShowModal(true);
  };

  const handleDelete = async (id) => {
    if (!window.confirm("Êtes-vous sûr de vouloir supprimer cet équipement ?")) {
      return;
    }

    console.log("handleDelete - id original:", id);
    // S'assurer que l'ID est bien un nombre ou une chaîne simple
    const cleanId = String(id).split(':')[0];
    console.log("handleDelete - cleanId:", cleanId);

    try {
      const token = localStorage.getItem('authToken');
      const headers = {
        'Content-Type': 'application/json',
      };
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch(`http://localhost:8081/api/equipements/${cleanId}`, {
        method: 'DELETE',
        headers: headers
      });

      if (!response.ok) {
        throw new Error(`Erreur HTTP: ${response.status}`);
      }

      setEquipements(prev => prev.filter(eq => String(eq.id).split(':')[0] !== cleanId));
      setFilteredEquipements(prev => prev.filter(eq => String(eq.id).split(':')[0] !== cleanId));
      
      alert('Équipement supprimé avec succès !');
    } catch (err) {
      console.error("Erreur lors de la suppression:", err);
      alert(`Erreur lors de la suppression: ${err.message}`);
    }
  };

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };

  const formatDate = (dateInput) => {
    if (!dateInput || dateInput === null || dateInput === undefined || dateInput === '') {
      return 'N/A';
    }

    try {
      const dateObj = new Date(dateInput);
      if (!isNaN(dateObj.getTime())) {
        return dateObj.toLocaleDateString('fr-FR');
      }
      return 'N/A';
    } catch (e) {
      console.error("Erreur de formatage de date", e, "pour la valeur:", dateInput);
      return 'N/A';
    }
  };

  const handleExportExcel = () => {
    if (!Array.isArray(filteredEquipements) || filteredEquipements.length === 0) {
      alert("Aucune donnée à exporter");
      return;
    }
    
    const columns = [
      "Nom",
      "Type",
      "Numéro de série",
      "Statut",
      "Date d'achat",
      "Date fin garantie",
      "Fournisseur",
      "Actions"
    ];
    const rows = filteredEquipements.map(eq => [
      eq.nom || "",
      eq.type || "",
      eq.numero_serie || eq.numeroSerie || "",
      eq.statut || "",
      formatDate(eq.dateAchat || eq.date_achat) || "",
      formatDate(eq.dateFinGarantie || eq.date_fin_garantie) || "",
      eq.fournisseur?.nom || "",
      ""
    ]);
    const worksheet = XLSX.utils.aoa_to_sheet([columns, ...rows]);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "Equipements");
    XLSX.writeFile(workbook, "equipements.xlsx");
  };

  const handleExportPDF = () => {
    if (!Array.isArray(filteredEquipements) || filteredEquipements.length === 0) {
      alert("Aucune donnée à exporter");
      return;
    }
    
    const doc = new jsPDF();
    doc.text("Liste des équipements", 14, 16);
    const columns = [
      "Nom",
      "Type",
      "Numéro de série",
      "Statut",
      "Date d'achat",
      "Date fin garantie",
      "Fournisseur"
    ];
    const rows = filteredEquipements.map(eq => [
      eq.nom || "",
      eq.type || "",
      eq.numero_serie || eq.numeroSerie || "",
      eq.statut || "",
      formatDate(eq.dateAchat || eq.date_achat) || "",
      formatDate(eq.dateFinGarantie || eq.date_fin_garantie) || "",
      eq.fournisseur?.nom || ""
    ]);
    autoTable(doc, {
      head: [columns],
      body: rows,
      startY: 22,
      styles: { fontSize: 10 }
    });
    doc.save("equipements.pdf");
  };

  const handlePrint = () => {
    window.print();
  };

  // Fonctions pour calculer les statistiques
  const getStatutStats = () => {
    const statutCounts = {};
    filteredEquipements.forEach(eq => {
      const statut = eq.statut || 'Non défini';
      statutCounts[statut] = (statutCounts[statut] || 0) + 1;
    });
    return statutCounts;
  };

  const getTypeStats = () => {
    const typeCounts = {};
    filteredEquipements.forEach(eq => {
      const type = eq.type || 'Non défini';
      typeCounts[type] = (typeCounts[type] || 0) + 1;
    });
    return typeCounts;
  };

  // Configuration des graphiques
  const statutStats = getStatutStats();
  const typeStats = getTypeStats();

  const statutChartData = {
    labels: Object.keys(statutStats),
    datasets: [{
      label: 'Nombre d\'équipements',
      data: Object.values(statutStats),
      backgroundColor: [
        '#4e73df',
        '#1cc88a',
        '#36b9cc',
        '#f6c23e',
        '#e74a3b'
      ],
      borderColor: [
        '#4e73df',
        '#1cc88a',
        '#36b9cc',
        '#f6c23e',
        '#e74a3b'
      ],
      borderWidth: 1
    }]
  };

  const typeChartData = {
    labels: Object.keys(typeStats),
    datasets: [{
      data: Object.values(typeStats),
      backgroundColor: [
        '#1976d2',
        '#43a047',
        '#d32f2f',
        '#ffa000',
        '#9c27b0',
        '#00acc1',
        '#8bc34a'
      ],
      borderWidth: 2,
      borderColor: '#fff'
    }]
  };

  const chartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: 'Répartition par statut'
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          stepSize: 1
        }
      }
    }
  };

  const doughnutOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'right',
      },
      title: {
        display: true,
        text: 'Répartition par type'
      }
    }
  };

  return (
    <div className="equipements-container">
      {/* Sidebar */}
      <div className="sidebar">
        <div className="logo-section">
          <img src={logo} alt="Logo Entreprise" className="company-logo" />
        </div>
        <nav className="main-menu">
          <ul>
            <li>
              <Link to="/" className={location.pathname === '/' ? 'home-active active' : ''}>
                <FontAwesomeIcon icon={faLaptop} style={{marginRight:8}} /> Accueil
              </Link>
            </li>
            <li>
              <Link to="/equipements" className={location.pathname === '/equipements' ? 'active' : ''}>
                <FontAwesomeIcon icon={faLaptop} style={{marginRight:8}} /> Gérer les équipements
              </Link>
            </li>
            <li>
              <Link to="/employes" className={location.pathname === '/employes' ? 'active' : ''}>
                <FontAwesomeIcon icon={faUsers} style={{marginRight:8}} /> Gérer les employés
              </Link>
            </li>
            <li>
              <Link to="/affectations" className={location.pathname === '/affectations' ? 'active' : ''}>
                <FontAwesomeIcon icon={faExchangeAlt} style={{marginRight:8}} /> Suivi des affectations
              </Link>
            </li>
            <li>
              <Link to="/fournisseurs" className={location.pathname === '/fournisseurs' ? 'active' : ''}>
                <FontAwesomeIcon icon={faTruck} style={{marginRight:8}} /> Gérer les fournisseurs
              </Link>
            </li>
          </ul>
        </nav>
        <div className="secondary-links">
          <Link to="/profil">
            <FontAwesomeIcon icon={faUserCog} style={{marginRight:8}} /> Mon profil & paramètres
          </Link>
        </div>
        <div style={{ marginTop: 'auto', padding: '20px 0 0 0', textAlign: 'center' }}>
          <button
            className="logout-btn"
            onClick={() => {
              localStorage.removeItem('authToken');
              window.location.href = '/';
            }}
          >
            <FontAwesomeIcon icon={faSignOutAlt} style={{marginRight:8}} /> Déconnexion
          </button>
        </div>
      </div>

      {/* Main content */}
      <div className="equipements-content">
        {/* Dashboard header row */}
        <div className="dashboard-header-row" style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: 32 }}>
          <div className="search-bar" style={{ flex: 1, maxWidth: 480, marginRight: 24, position: 'relative' }}>
            <FontAwesomeIcon icon={faSearch} style={{ position: 'absolute', left: 14, top: '50%', transform: 'translateY(-50%)', color: '#1976d2', fontSize: '1.1rem', opacity: 0.8 }} />
            <input
              type="text"
              placeholder="Recherche par mot clé"
              value={searchTerm}
              onChange={handleSearchChange}
              style={{ width: '100%', padding: '10px 16px 10px 38px', borderRadius: 8, border: '1px solid #dbeafe', fontSize: '1rem', boxShadow: '0 2px 8px rgba(21,101,192,0.06)' }}
            />
          </div>
          <div className="profile-block" style={{ display: 'flex', alignItems: 'center', gap: 20 }}>
            <div className="notification-icon" style={{ position: 'relative', marginRight: 8, cursor: 'pointer' }}>
              <FontAwesomeIcon icon={faBell} style={{ fontSize: '1.3rem', color: '#1976d2' }} />
              <span style={{ position: 'absolute', top: -6, right: -6, background: '#e74a3b', color: '#fff', borderRadius: '50%', fontSize: '0.7rem', padding: '2px 6px', fontWeight: 600 }}>3</span>
            </div>
            <span style={{ fontWeight: 500, color: '#2e3a4e', fontSize: '1rem', marginRight: 8 }}>Responsable IT</span>
            <FontAwesomeIcon icon={faUserCog} style={{ fontSize: '1.5rem', color: '#1976d2', background: '#fff', borderRadius: '50%', padding: 6, boxShadow: '0 2px 8px rgba(21,101,192,0.10)' }} />
          </div>
        </div>

        <h2 className="equipements-title">
          Liste des équipements ({filteredEquipements.length})
        </h2>

        {/* Message d'erreur si problème backend */}
        {error && (
          <div style={{
            background: '#fff3cd',
            border: '1px solid #ffeaa7',
            borderRadius: 8,
            padding: 16,
            marginBottom: 20,
            color: '#856404'
          }}>
            <strong>⚠️ Attention :</strong> {error}
          </div>
        )}
        
        <div style={{ display: 'flex', gap: 16, marginBottom: 24 }}>
          <button
            className="btn btn-primary"
            style={{ fontWeight: 700, fontSize: '1rem', borderRadius: 10, background: 'linear-gradient(90deg, #1976d2 0%, #42a5f5 100%)', color: '#fff', boxShadow: '0 4px 16px rgba(21,101,192,0.18)' }}
            onClick={() => {
              setEditingId(null);
              setShowModal(true);
            }}
          >
            <FontAwesomeIcon icon={faPlus} style={{marginRight: 8}} />
            Ajouter un équipement
          </button>
          <button className="btn btn-primary" style={{ fontWeight: 700, fontSize: '1rem', borderRadius: 10, background: 'linear-gradient(90deg, #43a047 0%, #66bb6a 100%)', color: '#fff', boxShadow: '0 4px 16px rgba(67,160,71,0.18)' }} onClick={handleExportExcel}>
            Exporter Excel
          </button>
          <button className="btn btn-primary" style={{ fontWeight: 700, fontSize: '1rem', borderRadius: 10, background: 'linear-gradient(90deg, #d32f2f 0%, #e57373 100%)', color: '#fff', boxShadow: '0 4px 16px rgba(211,47,47,0.18)' }} onClick={handleExportPDF}>
            Exporter PDF
          </button>
          <button className="btn btn-primary" style={{ fontWeight: 700, fontSize: '1rem', borderRadius: 10, background: 'linear-gradient(90deg, #ffa000 0%, #ffd54f 100%)', color: '#fff', boxShadow: '0 4px 16px rgba(255,160,0,0.18)' }} onClick={handlePrint}>
            Imprimer
          </button>
          <button
            className="btn btn-primary"
            style={{ fontWeight: 700, fontSize: '1rem', borderRadius: 10, background: 'linear-gradient(90deg, #9c27b0 0%, #ba68c8 100%)', color: '#fff', boxShadow: '0 4px 16px rgba(156,39,176,0.18)' }}
            onClick={() => setShowStats(!showStats)}
          >
            {showStats ? 'Masquer' : 'Afficher'} les statistiques
          </button>
        </div>

        {/* Section des statistiques */}
        {showStats && (
          <div style={{
            background: '#fff',
            borderRadius: 12,
            padding: 24,
            marginBottom: 24,
            boxShadow: '0 4px 16px rgba(0,0,0,0.1)',
            border: '1px solid #e0e0e0'
          }}>
            <h3 style={{
              color: '#1976d2',
              marginBottom: 24,
              fontSize: '1.3rem',
              fontWeight: 700,
              textAlign: 'center'
            }}>
              📊 Statistiques des équipements
            </h3>

            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 32, alignItems: 'center' }}>
              {/* Graphique en barres pour les statuts */}
              <div style={{
                background: '#fafafa',
                padding: 20,
                borderRadius: 8,
                border: '1px solid #e0e0e0',
                height: '350px'
              }}>
                <Bar data={statutChartData} options={chartOptions} />
              </div>

              {/* Graphique en donut pour les types */}
              <div style={{
                background: '#fafafa',
                padding: 20,
                borderRadius: 8,
                border: '1px solid #e0e0e0',
                height: '350px'
              }}>
                <Doughnut data={typeChartData} options={doughnutOptions} />
              </div>
            </div>

            {/* Résumé textuel */}
            <div style={{
              marginTop: 24,
              padding: 16,
              background: '#f8f9fa',
              borderRadius: 8,
              border: '1px solid #dee2e6'
            }}>
              <h4 style={{ color: '#495057', marginBottom: 12, fontSize: '1.1rem' }}>📈 Résumé</h4>
              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: 16 }}>
                <div style={{ textAlign: 'center', padding: 12, background: '#fff', borderRadius: 6, border: '1px solid #e9ecef' }}>
                  <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#1976d2' }}>
                    {filteredEquipements.length}
                  </div>
                  <div style={{ color: '#6c757d', fontSize: '0.9rem' }}>Total équipements</div>
                </div>
                <div style={{ textAlign: 'center', padding: 12, background: '#fff', borderRadius: 6, border: '1px solid #e9ecef' }}>
                  <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#28a745' }}>
                    {statutStats['DISPONIBLE'] || 0}
                  </div>
                  <div style={{ color: '#6c757d', fontSize: '0.9rem' }}>Disponibles</div>
                </div>
                <div style={{ textAlign: 'center', padding: 12, background: '#fff', borderRadius: 6, border: '1px solid #e9ecef' }}>
                  <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#ffc107' }}>
                    {statutStats['EN_MAINTENANCE'] || 0}
                  </div>
                  <div style={{ color: '#6c757d', fontSize: '0.9rem' }}>En maintenance</div>
                </div>
                <div style={{ textAlign: 'center', padding: 12, background: '#fff', borderRadius: 6, border: '1px solid #e9ecef' }}>
                  <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#dc3545' }}>
                    {statutStats['HORS_SERVICE'] || 0}
                  </div>
                  <div style={{ color: '#6c757d', fontSize: '0.9rem' }}>Hors service</div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Modal d'ajout/modification d'équipement */}
        {showModal && (
          <div style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 1000
          }}>
            <div style={{
              backgroundColor: 'white',
              borderRadius: 12,
              padding: 32,
              maxWidth: 600,
              width: '90%',
              maxHeight: '90vh',
              overflowY: 'auto',
              boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)'
            }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
                <h3 style={{ margin: 0, color: '#1976d2', fontSize: '1.5rem', fontWeight: 700 }}>
                  {editingId ? 'Modifier un équipement' : 'Ajouter un nouvel équipement'}
                </h3>
                <button
                  onClick={() => {
                    setShowModal(false);
                    setEditingId(null);
                  }}
                  style={{
                    background: 'none',
                    border: 'none',
                    fontSize: '1.5rem',
                    color: '#666',
                    cursor: 'pointer',
                    padding: 8,
                    borderRadius: '50%'
                  }}
                >
                  <FontAwesomeIcon icon={faTimes} />
                </button>
              </div>

              <form onSubmit={handleSubmit}>
                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 16, marginBottom: 16 }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>
                      Nom de l'équipement *
                    </label>
                    <input
                      type="text"
                      name="nom"
                      value={formData.nom}
                      onChange={handleInputChange}
                      required
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: 8,
                        fontSize: '1rem',
                        transition: 'border-color 0.2s'
                      }}
                      onFocus={(e) => e.target.style.borderColor = '#1976d2'}
                      onBlur={(e) => e.target.style.borderColor = '#e0e0e0'}
                    />
                  </div>

                  <div>
                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>
                      Type *
                    </label>
                    <select
                      name="type"
                      value={formData.type}
                      onChange={handleInputChange}
                      required
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: 8,
                        fontSize: '1rem'
                      }}
                    >
                      <option value="">Sélectionner un type</option>
                      {typeOptions.map(type => (
                        <option key={type} value={type}>{type}</option>
                      ))}
                    </select>
                  </div>
                </div>

                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 16, marginBottom: 16 }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>
                      Numéro de série
                    </label>
                    <input
                      type="text"
                      name="numero_serie"
                      value={formData.numero_serie}
                      onChange={handleInputChange}
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: 8,
                        fontSize: '1rem'
                      }}
                    />
                  </div>

                  <div>
                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>
                      Statut *
                    </label>
                    <select
                      name="statut"
                      value={formData.statut}
                      onChange={handleInputChange}
                      required
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: 8,
                        fontSize: '1rem'
                      }}
                    >
                      {statutOptions.map(statut => (
                        <option key={statut} value={statut}>{statut}</option>
                      ))}
                    </select>
                  </div>
                </div>

                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 16, marginBottom: 16 }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>
                      Date d'achat
                    </label>
                    <input
                      type="date"
                      name="date_achat"
                      value={formData.date_achat}
                      onChange={handleInputChange}
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: 8,
                        fontSize: '1rem'
                      }}
                    />
                  </div>

                  <div>
                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>
                      Fournisseur
                    </label>
                    <select
                      name="fournisseur_id"
                      value={formData.fournisseur_id}
                      onChange={handleInputChange}
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: 8,
                        fontSize: '1rem'
                      }}
                    >
                      <option value="">Sélectionner un fournisseur</option>
                      {fournisseurs.map(fournisseur => (
                        <option key={fournisseur.id} value={fournisseur.id}>
                          {fournisseur.nom}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 16, marginBottom: 16 }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>
                      Début de garantie
                    </label>
                    <input
                      type="date"
                      name="date_debut_garantie"
                      value={formData.date_debut_garantie}
                      onChange={handleInputChange}
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: 8,
                        fontSize: '1rem'
                      }}
                    />
                  </div>

                  <div>
                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>
                      Fin de garantie
                    </label>
                    <input
                      type="date"
                      name="date_fin_garantie"
                      value={formData.date_fin_garantie}
                      onChange={handleInputChange}
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: 8,
                        fontSize: '1rem'
                      }}
                    />
                  </div>
                </div>

                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: 16, marginBottom: 16 }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>
                      Stock actuel
                    </label>
                    <input
                      type="number"
                      name="stock_actuel"
                      value={formData.stock_actuel}
                      onChange={handleInputChange}
                      min="0"
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: 8,
                        fontSize: '1rem'
                      }}
                    />
                  </div>

                  <div>
                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>
                      Stock maximum
                    </label>
                    <input
                      type="number"
                      name="stock_max"
                      value={formData.stock_max}
                      onChange={handleInputChange}
                      min="1"
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: 8,
                        fontSize: '1rem'
                      }}
                    />
                  </div>

                  <div>
                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>
                      Stock minimum
                    </label>
                    <input
                      type="number"
                      name="stock_min"
                      value={formData.stock_min}
                      onChange={handleInputChange}
                      min="0"
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: 8,
                        fontSize: '1rem'
                      }}
                    />
                  </div>
                </div>

                <div style={{ marginBottom: 24 }}>
                  <label style={{ display: 'flex', alignItems: 'center', fontWeight: 600, color: '#333', cursor: 'pointer' }}>
                    <input
                      type="checkbox"
                      name="en_stock"
                      checked={formData.en_stock}
                      onChange={handleInputChange}
                      style={{ marginRight: 8, transform: 'scale(1.2)' }}
                    />
                    Équipement en stock
                  </label>
                </div>

                <div style={{ display: 'flex', gap: 12, justifyContent: 'flex-end' }}>
                  <button
                    type="button"
                    onClick={() => {
                      setShowModal(false);
                      setEditingId(null);
                    }}
                    style={{
                      padding: '12px 24px',
                      border: '2px solid #e0e0e0',
                      borderRadius: 8,
                      background: 'white',
                      color: '#666',
                      fontSize: '1rem',
                      fontWeight: 600,
                      cursor: 'pointer'
                    }}
                  >
                    Annuler
                  </button>
                  <button
                    type="submit"
                    disabled={submitting}
                    style={{
                      padding: '12px 24px',
                      border: 'none',
                      borderRadius: 8,
                      background: submitting ? '#ccc' : 'linear-gradient(90deg, #1976d2 0%, #42a5f5 100%)',
                      color: 'white',
                      fontSize: '1rem',
                      fontWeight: 600,
                      cursor: submitting ? 'not-allowed' : 'pointer',
                      boxShadow: '0 4px 16px rgba(21,101,192,0.18)'
                    }}
                  >
                    {submitting 
                      ? (editingId ? 'Modification en cours...' : 'Ajout en cours...') 
                      : (editingId ? 'Modifier l\'équipement' : 'Ajouter l\'équipement')}
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* Contenu principal */}
        {loading ? (
          <div style={{ textAlign: 'center', padding: '40px' }}>
            <p>Chargement des équipements...</p>
          </div>
        ) : error ? (
          <div style={{ textAlign: 'center', padding: '40px', color: '#e74a3b' }}>
            <p>Erreur lors du chargement : {error}</p>
            <button 
              onClick={() => window.location.reload()} 
              style={{ marginTop: '10px', padding: '8px 16px', background: '#1976d2', color: 'white', border: 'none', borderRadius: '4px', cursor: 'pointer' }}
            >
              Actualiser la page
            </button>
          </div>
        ) : !Array.isArray(filteredEquipements) || filteredEquipements.length === 0 ? (
          <div style={{ textAlign: 'center', padding: '40px' }}>
            <p>{searchTerm ? 'Aucun résultat trouvé' : 'Aucun équipement trouvé'}</p>
          </div>
        ) : (
          <table className="equipements-table">
            <thead>
              <tr>
                <th>Nom</th>
                <th>Type</th>
                <th>Numéro de série</th>
                <th>Statut</th>
                <th>Date d'achat</th>
                <th>Date fin garantie</th>
                <th>Fournisseur</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredEquipements.map((eq, index) => {
                const numeroSerie = eq.numero_serie || eq.numeroSerie || 'N/A';
                const dateAchat = formatDate(eq.dateAchat || eq.date_achat);
                const dateFinGarantie = formatDate(eq.dateFinGarantie || eq.date_fin_garantie);
                const fournisseur = eq.fournisseur?.nom || eq.fournisseur_id || 'Non spécifié';

                return (
                  <tr key={eq.id || index}>
                    <td>{eq.nom || 'N/A'}</td>
                    <td>{eq.type || 'N/A'}</td>
                    <td>{numeroSerie}</td>
                    <td>{eq.statut || 'N/A'}</td>
                    <td>{dateAchat}</td>
                    <td>{dateFinGarantie}</td>
                    <td>{fournisseur}</td>
                    <td>
                      <div style={{ display: 'flex', gap: '8px' }}>
                        <button 
                          onClick={() => handleEdit(eq)}
                          style={{
                            background: '#1976d2',
                            color: 'white',
                            border: 'none',
                            borderRadius: '4px',
                            padding: '6px 12px',
                            cursor: 'pointer',
                            display: 'flex',
                            alignItems: 'center',
                            gap: '4px'
                          }}
                        >
                          <FontAwesomeIcon icon={faEdit} />
                          <span>Modifier</span>
                        </button>
                        <button 
                          onClick={() => handleDelete(eq.id)}
                          style={{
                            background: '#e74a3b',
                            color: 'white',
                            border: 'none',
                            borderRadius: '4px',
                            padding: '6px 12px',
                            cursor: 'pointer',
                            display: 'flex',
                            alignItems: 'center',
                            gap: '4px'
                          }}
                        >
                          <FontAwesomeIcon icon={faTrash} />
                          <span>Supprimer</span>
                        </button>
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        )}
      </div>
    </div>
  );
}