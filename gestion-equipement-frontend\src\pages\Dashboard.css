.main-menu li a.home-active {
  background: linear-gradient(90deg, #64b5f6 0%, #1976d2 100%) !important;
  color: #fff !important;
  font-weight: bold;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(21,101,192,0.10);
  border-left: 6px solid #fff;
  transition: background 0.2s, color 0.2s;
}
.main-menu ul li:first-child a.active {
  background: linear-gradient(90deg, #64b5f6 0%, #1976d2 100%) !important;
  color: #fff !important;
  font-weight: bold;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(21,101,192,0.10);
  border-left: 6px solid #fff;
  transition: background 0.2s, color 0.2s;
}
.main-menu li a.active, .main-menu li .active {
  background: linear-gradient(90deg, #64b5f6 0%, #1976d2 100%) !important;
  color: #fff !important;
  font-weight: bold;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(21,101,192,0.10);
  border-left: 6px solid #fff;
  transition: background 0.2s, color 0.2s;
}
/* Structure principale */
.dashboard-container {
  display: flex;
  min-height: 100vh;
  background-color: #f8f9fc;
}

.sidebar {
  width: 250px;
  background: linear-gradient(135deg, #1565c0 0%, #1e88e5 100%);
  color: #fff;
  display: flex;
  flex-direction: column;
  padding: 20px 0;
  box-shadow: 2px 0 16px rgba(21,101,192,0.12);
}

.logo-section {
  padding: 32px 20px 24px;
  text-align: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.company-logo {
  width: 160px;
  height: 150px;
  object-fit: contain;
  background: #fff;
  padding: 8px;
  object-fit: cover;
  border-radius: 50%;
  box-shadow: 0 4px 24px rgba(21,101,192,0.15);
  margin: 0 auto 16px auto;
  
}

.main-menu {
  flex-grow: 1;
  margin-top: 20px;
}

.main-menu ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.main-menu li a {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: rgba(255, 255, 255, 0.92);
  text-decoration: none;
  transition: all 0.3s;
}

.main-menu li a.active {
  background: #1976d2;
  color: #fff !important;
  font-weight: bold;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(21,101,192,0.10);
  border-left: 6px solid #fff;
  transition: background 0.2s, color 0.2s;
}

.main-menu li a:hover {
  background: rgba(255,255,255,0.18);
  color: #fff;
}

.main-menu li a i {
  margin-right: 10px;
  width: 20px;
  text-align: center;
}

.notification-badge {
  background: #e74a3b;
  color: white;
  border-radius: 10px;
  padding: 2px 8px;
  font-size: 0.7rem;
  margin-left: auto;
}

.secondary-links {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 15px 20px 0;
}

.secondary-links a {
  display: block;
  color: #e3f2fd;
  text-decoration: none;
  padding: 8px 0;
  font-size: 0.9rem;
}

.secondary-links a:hover {
  color: #bbdefb;
  background: linear-gradient(90deg, #1976d2 0%, #64b5f6 100%);
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 10px 28px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  margin-top: 24px;
  box-shadow: 0 2px 8px rgba(21,101,192,0.10);
  transition: background 0.2s, transform 0.2s;
}
.logout-btn:hover {
  background: linear-gradient(90deg, #64b5f6 0%, #1976d2 100%);
  transform: scale(1.04);
}

.secondary-links a i {
  margin-right: 10px;
}

/* Contenu principal */
.main-content {
  flex-grow: 1;
  padding: 20px 30px;
}

.content-header {
  margin-bottom: 30px;
}

.content-header h1 {
  color: #2e3a4e;
  margin-bottom: 5px;
}

.content-header p {
  color: #6c757d;
  margin: 0;
}

/* Cartes de statistiques */

/* Nouvelle disposition : stats et activités côte à côte */
.dashboard-top-row {
  display: flex;
  gap: 40px;
  margin-bottom: 30px;
  align-items: stretch;
  background: #f5f8fc;
  border-radius: 16px;
  box-shadow: 0 2px 16px rgba(21,101,192,0.08);
  padding: 32px 24px;
}

.dashboard-top-row > div:first-child {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 16px rgba(21,101,192,0.12);
  padding: 40px 40px 32px 40px;
  min-width: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
  flex: 2 1 0;
  width: 100%;
  max-width: none;
}

.dashboard-top-row .chartjs-render-monitor {
  width: 100% !important;
  height: 340px !important;
}

.stats-container {
  flex: 2;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.dashboard-section.recent-activity-section {
    min-width: 320px;
    max-width: 420px;
    margin-bottom: 0;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    height: 400px;
    flex: 1 1 0;
}

.stat-card {
  background: white;
  border-radius: 5px;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.stat-info h3 {
  color: #6c757d;
  font-size: 0.9rem;
  margin: 0 0 5px 0;
  text-transform: uppercase;
}

.stat-value {
  color: #2e3a4e;
  font-size: 1.5rem;
  font-weight: bold;
  margin: 0;
}

.stat-icon {
  font-size: 2rem;
  opacity: 0.7;
}

/* Section activité */
.dashboard-section {
  background: white;
  border-radius: 5px;
  padding: 20px;
  box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
  margin-bottom: 20px;
}

.dashboard-section h2 {
  color: #2e3a4e;
  font-size: 1.2rem;
  margin-top: 0;
  margin-bottom: 15px;
}

.recent-activity {
  color: #6c757d;
  font-size: 0.9rem;
}
