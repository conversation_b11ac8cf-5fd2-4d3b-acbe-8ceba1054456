import { Link, useLocation } from 'react-router-dom';
import logo from '../assets/images/asment logo.jpg';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faLaptop, faUsers, faExchangeAlt, faTruck, faBell, faUserCog, faSignOutAlt, faSearch } from '@fortawesome/free-solid-svg-icons';
import './Dashboard.css';
import { Bar } from 'react-chartjs-2';
import { Chart, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend } from 'chart.js';
import { useState, useEffect } from "react";
Chart.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);

export default function Dashboard() {
  const location = useLocation();

  // État dynamique pour les stats
  const [stats, setStats] = useState([
    { title: "Équipements", value: 0, icon: "fas fa-laptop", color: "#4e73df" },
    { title: "Employés", value: 0, icon: "fas fa-users", color: "#1cc88a" },
    { title: "Affectations", value: 0, icon: "fas fa-exchange-alt", color: "#36b9cc" },
    { title: "Fournisseurs", value: 0, icon: "fas fa-truck", color: "#f6c23e" }
  ]);

  // État pour le graphe dynamique
  const [barData, setBarData] = useState({
    labels: [],
    datasets: [
      {
        label: "Nombre de visites",
        data: [],
        backgroundColor: [
          '#1976d2',
          '#1cc88a',
          '#f6c23e',
          '#36b9cc'
        ],
      },
    ],
  });

  // État pour l'activité récente - Initialisé avec un tableau vide
  const [recentActivities, setRecentActivities] = useState([]);

  // Charger les stats
  useEffect(() => {
    console.log("Chargement des statistiques...");
    
    // Récupérer le token d'authentification
    const token = localStorage.getItem('authToken');
    const headers = {
      'Content-Type': 'application/json',
    };
    
    // Ajouter le token si il existe
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }
    
    fetch("http://localhost:8081/api/dashboard/stats", {
      method: 'GET',
      headers: headers
    })
      .then(res => {
        console.log("Status stats:", res.status);
        if (!res.ok) {
          throw new Error(`Erreur HTTP: ${res.status}`);
        }
        return res.json();
      })
      .then(data => {
        console.log("Données stats reçues:", data);
        console.log("Structure complète des données:", JSON.stringify(data, null, 2));
        
        // Essayer différentes variantes de noms de propriétés
        const totalFournisseurs = data.totalFournisseurs || data.fournisseurs || data.totalFournisseur || data.fournisseur || 0;
        const totalEquipements = data.totalEquipements || data.equipements || data.totalEquipement || data.equipement || 0;
        const totalEmployes = data.totalEmployes || data.employes || data.totalEmploye || data.employe || 0;
        const totalAffectations = data.totalAffectations || data.affectations || data.totalAffectation || data.affectation || 0;
        
        console.log("Valeurs extraites:", {
          totalFournisseurs,
          totalEquipements,
          totalEmployes,
          totalAffectations
        });
        
        setStats([
          { title: "Équipements", value: totalEquipements, icon: "fas fa-laptop", color: "#4e73df" },
          { title: "Employés", value: totalEmployes, icon: "fas fa-users", color: "#1cc88a" },
          { title: "Affectations", value: totalAffectations, icon: "fas fa-exchange-alt", color: "#36b9cc" },
          { title: "Fournisseurs", value: totalFournisseurs, icon: "fas fa-truck", color: "#f6c23e" }
        ]);
      })
      .catch(err => {
        console.error("Erreur lors du chargement des stats :", err);
      });
  }, []);

  // Charger les données du graphe des visites
  useEffect(() => {
    console.log("Chargement des données du graphique des visites...");
    
    const token = localStorage.getItem('authToken');
    const headers = {
      'Content-Type': 'application/json',
    };
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }
    
    fetch("http://localhost:8081/api/dashboard/visites-par-periode", {
      method: 'GET',
      headers: headers
    })
      .then(res => {
        console.log("Status graphique visites:", res.status);
        if (!res.ok) {
          throw new Error(`Erreur HTTP: ${res.status}`);
        }
        return res.json();
      })
      .then(data => {
        console.log("Données graphique visites reçues:", data);
        const labels = Object.keys(data);
        const values = Object.values(data);
        setBarData({
          labels: labels,
          datasets: [
            {
              label: "Nombre de visites",
              data: values,
              backgroundColor: [
                '#1976d2',
                '#1cc88a',
                '#f6c23e',
                '#36b9cc',
                '#e74a3b',
                '#9b59b6'
              ],
            },
          ],
        });
      })
      .catch(err => {
        console.error("Erreur lors du chargement du graphe des visites :", err);
      });
  }, []);

  // Charger l'activité récente - Avec vérification que la réponse est un tableau
  useEffect(() => {
    console.log("Chargement des activités récentes...");
    
    const token = localStorage.getItem('authToken');
    const headers = {
      'Content-Type': 'application/json',
    };
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }
    
    fetch("http://localhost:8081/api/dashboard/recent-activities", {
      method: 'GET',
      headers: headers
    })
      .then(res => {
        console.log("Status de la réponse:", res.status);
        if (!res.ok) {
          throw new Error(`Erreur HTTP: ${res.status}`);
        }
        return res.json();
      })
      .then(data => {
        console.log("Données reçues pour les activités:", data);
        // Vérifier que data est un tableau, sinon utiliser un tableau vide
        if (Array.isArray(data)) {
          setRecentActivities(data);
          console.log("Activités définies:", data.length, "éléments");
        } else {
          console.warn("Les données d'activité récente ne sont pas un tableau:", data);
          setRecentActivities([]);
        }
      })
      .catch(err => {
        console.error("Erreur lors du chargement des activités récentes :", err);
        // En cas d'erreur, s'assurer que recentActivities reste un tableau vide
        setRecentActivities([]);
      });
  }, []);

  const barOptions = {
    responsive: true,
    plugins: {
      legend: { position: 'top' },
      title: { display: true, text: 'Nombre de visites par période' },
    },
  };

  return (
    <div className="dashboard-container">
      {/* Sidebar avec navigation */}
      <div className="sidebar">
        <div className="logo-section">
          <img src={logo} alt="Logo Entreprise" className="company-logo" />
        </div>
        <nav className="main-menu">
          <ul>
            <li>
              <Link to="/" className={location.pathname === '/' ? 'home-active active' : ''}>
                <FontAwesomeIcon icon={faLaptop} style={{marginRight:8}} /> Accueil
              </Link>
            </li>
            <li>
              <Link to="/equipements" className={location.pathname === '/equipements' ? 'active' : ''}>
                <FontAwesomeIcon icon={faLaptop} style={{marginRight:8}} /> Gérer les équipements
              </Link>
            </li>
            <li>
              <Link to="/employes" className={location.pathname === '/employes' ? 'active' : ''}>
                <FontAwesomeIcon icon={faUsers} style={{marginRight:8}} /> Gérer les employés
              </Link>
            </li>
            <li>
              <Link to="/affectations" className={location.pathname === '/affectations' ? 'active' : ''}>
                <FontAwesomeIcon icon={faExchangeAlt} style={{marginRight:8}} /> Suivi des affectations
              </Link>
            </li>
            <li>
              <Link to="/fournisseurs" className={location.pathname === '/fournisseurs' ? 'active' : ''}>
                <FontAwesomeIcon icon={faTruck} style={{marginRight:8}} /> Gérer les fournisseurs
              </Link>
            </li>
          </ul>
        </nav>
        <div className="secondary-links">
          <Link to="/profil">
            <FontAwesomeIcon icon={faUserCog} style={{marginRight:8}} /> Mon profil & paramètres
          </Link>
        </div>
        <div style={{ marginTop: 'auto', padding: '20px 0 0 0', textAlign: 'center' }}>
          <button
            className="logout-btn"
            onClick={() => {
              localStorage.removeItem('authToken');
              window.location.href = '/';
            }}
          >
            <FontAwesomeIcon icon={faSignOutAlt} style={{marginRight:8}} /> Déconnexion
          </button>
        </div>
      </div>

      {/* Contenu principal */}
      <div className="main-content">
        <div className="dashboard-header-row" style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: 32 }}>
          <div className="search-bar" style={{ flex: 1, maxWidth: 480, marginRight: 24, position: 'relative' }}>
            <FontAwesomeIcon icon={faSearch} style={{ position: 'absolute', left: 14, top: '50%', transform: 'translateY(-50%)', color: '#1976d2', fontSize: '1.1rem', opacity: 0.8 }} />
            <input
              type="text"
              placeholder="Recherche par mot clé"
              style={{ width: '100%', padding: '10px 16px 10px 38px', borderRadius: 8, border: '1px solid #dbeafe', fontSize: '1rem', boxShadow: '0 2px 8px rgba(21,101,192,0.06)' }}
            />
          </div>
          <div className="profile-block" style={{ display: 'flex', alignItems: 'center', gap: 20 }}>
            <div className="notification-icon" style={{ position: 'relative', marginRight: 8, cursor: 'pointer' }}>
              <FontAwesomeIcon icon={faBell} style={{ fontSize: '1.3rem', color: '#1976d2' }} />
              <span style={{ position: 'absolute', top: -6, right: -6, background: '#e74a3b', color: '#fff', borderRadius: '50%', fontSize: '0.7rem', padding: '2px 6px', fontWeight: 600 }}>3</span>
            </div>
            <span style={{ fontWeight: 500, color: '#2e3a4e', fontSize: '1rem', marginRight: 8 }}>Responsable IT</span>
            <FontAwesomeIcon icon={faUserCog} style={{ fontSize: '1.5rem', color: '#1976d2', background: '#fff', borderRadius: '50%', padding: 6, boxShadow: '0 2px 8px rgba(21,101,192,0.10)' }} />
          </div>
        </div>

        <header className="content-header">
          <h1>Bienvenue, Responsable IT</h1>
          <p>Voici un aperçu de votre activité</p>
        </header>

        {/* Statistiques dynamiques */}
        <div className="stats-container">
          {stats.map((stat, index) => (
            <div key={index} className="stat-card" style={{ borderLeft: `4px solid ${stat.color}` }}>
              <div className="stat-info">
                <h3>{stat.title}</h3>
                <p className="stat-value">{stat.value}</p>
              </div>
              <div className="stat-icon" style={{ color: stat.color }}>
                <i className={stat.icon}></i>
              </div>
            </div>
          ))}
        </div>

        <div className="dashboard-top-row">
          <div style={{ background: '#fff', borderRadius: 8, boxShadow: '0 2px 8px rgba(21,101,192,0.10)', padding: 24, minWidth: 320, maxWidth: 420, marginBottom: 0, display: 'flex', alignItems: 'center', justifyContent: 'center', flex: 2 }}>
            <Bar data={barData} options={barOptions} />
          </div>
          <div className="dashboard-section recent-activity-section" style={{ flex: 1 }}>
            <h2>Activité récente</h2>
            <div className="recent-activity">
              {!Array.isArray(recentActivities) || recentActivities.length === 0 ? (
                <p>Aucune activité récente à afficher pour le moment.</p>
              ) : (
                <ul>
                  {recentActivities.map((act, idx) => (
                    <li key={idx}>
                      {act.equipement?.nom} affecté à {act.employe?.nom} le {act.dateAffectation}
                    </li>
                  ))}
                </ul>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}