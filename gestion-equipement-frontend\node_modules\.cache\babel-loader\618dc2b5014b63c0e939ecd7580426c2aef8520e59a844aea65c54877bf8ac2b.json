{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\gestion-equipement-frontend\\\\src\\\\pages\\\\Equipements.js\",\n  _s = $RefreshSig$();\nimport './Equipements.css';\nimport { Link, useLocation } from 'react-router-dom';\nimport logo from '../assets/images/asment logo.jpg';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { faLaptop, faUsers, faExchangeAlt, faTruck, faUserCog, faSignOutAlt, faSearch, faBell, faTimes, faPlus, faEdit, faTrash } from '@fortawesome/free-solid-svg-icons';\nimport { useEffect, useState } from \"react\";\nimport jsPDF from \"jspdf\";\nimport autoTable from \"jspdf-autotable\";\nimport * as XLSX from \"xlsx\";\nimport { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement } from 'chart.js';\nimport { Bar, Doughnut } from 'react-chartjs-2';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement);\nexport default function Equipements() {\n  _s();\n  const location = useLocation();\n  const [equipements, setEquipements] = useState([]);\n  const [filteredEquipements, setFilteredEquipements] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [showModal, setShowModal] = useState(false);\n  const [fournisseurs, setFournisseurs] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [submitting, setSubmitting] = useState(false);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [editingId, setEditingId] = useState(null);\n  const [showStats, setShowStats] = useState(false);\n\n  // État du formulaire\n  const [formData, setFormData] = useState({\n    nom: '',\n    type: '',\n    numero_serie: '',\n    statut: 'DISPONIBLE',\n    date_achat: '',\n    fournisseur_id: '',\n    date_debut_garantie: '',\n    date_fin_garantie: '',\n    en_stock: true,\n    stock_actuel: 1,\n    stock_max: 1,\n    stock_min: 1,\n    categorie_id: ''\n  });\n\n  // Options pour les statuts et types\n  const statutOptions = ['DISPONIBLE', 'EN_MAINTENANCE', 'OCCUPE', 'HORS_SERVICE'];\n  const typeOptions = ['ORDINATEUR', 'IMPRIMANTE', 'SCANNER', 'TELEPHONE', 'TABLETTE', 'SERVEUR', 'AUTRE'];\n  useEffect(() => {\n    loadEquipements();\n    loadFournisseurs();\n    loadCategories();\n  }, []);\n  useEffect(() => {\n    if (searchTerm === '') {\n      setFilteredEquipements(equipements);\n    } else {\n      const filtered = equipements.filter(equipement => {\n        var _equipement$fournisse;\n        return equipement.nom.toLowerCase().includes(searchTerm.toLowerCase()) || equipement.type.toLowerCase().includes(searchTerm.toLowerCase()) || equipement.numero_serie && equipement.numero_serie.toLowerCase().includes(searchTerm.toLowerCase()) || equipement.statut.toLowerCase().includes(searchTerm.toLowerCase()) || ((_equipement$fournisse = equipement.fournisseur) === null || _equipement$fournisse === void 0 ? void 0 : _equipement$fournisse.nom) && equipement.fournisseur.nom.toLowerCase().includes(searchTerm.toLowerCase());\n      });\n      setFilteredEquipements(filtered);\n    }\n  }, [searchTerm, equipements]);\n  const loadEquipements = () => {\n    console.log(\"Chargement des équipements...\");\n    const token = localStorage.getItem('authToken');\n    const headers = {\n      'Content-Type': 'application/json'\n    };\n    if (token) {\n      headers['Authorization'] = `Bearer ${token}`;\n    }\n    fetch(\"http://localhost:8081/api/equipements\", {\n      method: 'GET',\n      headers: headers\n    }).then(res => {\n      console.log(\"Status de la réponse équipements:\", res.status);\n      if (!res.ok) {\n        throw new Error(`Erreur HTTP: ${res.status}`);\n      }\n      return res.json();\n    }).then(data => {\n      console.log(\"Données équipements reçues:\", data);\n      if (Array.isArray(data)) {\n        setEquipements(data);\n        setFilteredEquipements(data);\n        setError(null);\n      } else {\n        console.warn(\"Les données équipements ne sont pas un tableau:\", data);\n        setEquipements([]);\n        setFilteredEquipements([]);\n        setError(\"Format de données incorrect\");\n      }\n      setLoading(false);\n    }).catch(err => {\n      console.error(\"Erreur lors du chargement des équipements:\", err);\n      setEquipements([]);\n      setFilteredEquipements([]);\n      setError(err.message || \"Erreur de chargement\");\n      setLoading(false);\n    });\n  };\n  const loadFournisseurs = () => {\n    console.log(\"Chargement des fournisseurs...\");\n    const token = localStorage.getItem('authToken');\n    const headers = {\n      'Content-Type': 'application/json'\n    };\n    if (token) {\n      headers['Authorization'] = `Bearer ${token}`;\n    }\n    fetch(\"http://localhost:8081/api/fournisseurs\", {\n      method: 'GET',\n      headers: headers\n    }).then(res => {\n      console.log(\"Status de la réponse fournisseurs:\", res.status);\n      if (!res.ok) {\n        throw new Error(`Erreur HTTP: ${res.status}`);\n      }\n      return res.json();\n    }).then(data => {\n      console.log(\"Données fournisseurs reçues:\", data);\n      if (Array.isArray(data)) {\n        setFournisseurs(data);\n        console.log(\"Fournisseurs chargés:\", data.length, \"éléments\");\n      } else {\n        console.warn(\"Les données fournisseurs ne sont pas un tableau:\", data);\n        setFournisseurs([]);\n      }\n    }).catch(err => {\n      console.error(\"Erreur lors du chargement des fournisseurs:\", err);\n      setFournisseurs([]);\n    });\n  };\n  const loadCategories = () => {\n    const token = localStorage.getItem('authToken');\n    const headers = {\n      'Content-Type': 'application/json'\n    };\n    if (token) {\n      headers['Authorization'] = `Bearer ${token}`;\n    }\n    fetch(\"http://localhost:8081/api/categories\", {\n      method: 'GET',\n      headers: headers\n    }).then(res => res.ok ? res.json() : []).then(data => setCategories(Array.isArray(data) ? data : [])).catch(err => {\n      console.error(\"Erreur lors du chargement des catégories:\", err);\n      setCategories([]);\n    });\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value,\n      type,\n      checked\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setSubmitting(true);\n    console.log(\"handleSubmit - editingId:\", editingId);\n    console.log(\"handleSubmit - formData:\", formData);\n    const dataToSend = {\n      ...formData,\n      fournisseur_id: formData.fournisseur_id || null\n    };\n    try {\n      const token = localStorage.getItem('authToken');\n      const headers = {\n        'Content-Type': 'application/json'\n      };\n      if (token) {\n        headers['Authorization'] = `Bearer ${token}`;\n      }\n      const url = editingId ? `http://localhost:8081/api/equipements/${editingId}` : \"http://localhost:8081/api/equipements\";\n      console.log(\"URL générée:\", url);\n      const method = editingId ? 'PUT' : 'POST';\n      const response = await fetch(url, {\n        method: method,\n        headers: headers,\n        body: JSON.stringify(dataToSend)\n      });\n      if (!response.ok) {\n        throw new Error(`Erreur HTTP: ${response.status}`);\n      }\n      const result = await response.json();\n      if (editingId) {\n        setEquipements(prev => prev.map(eq => eq.id === editingId ? result : eq));\n        setFilteredEquipements(prev => prev.map(eq => eq.id === editingId ? result : eq));\n      } else {\n        setEquipements(prev => [...prev, result]);\n        setFilteredEquipements(prev => [...prev, result]);\n      }\n      setFormData({\n        nom: '',\n        type: '',\n        numero_serie: '',\n        statut: 'DISPONIBLE',\n        date_achat: '',\n        fournisseur_id: '',\n        date_debut_garantie: '',\n        date_fin_garantie: '',\n        en_stock: true,\n        stock_actuel: 1,\n        stock_max: 1,\n        stock_min: 1,\n        categorie_id: ''\n      });\n      setShowModal(false);\n      setEditingId(null);\n      alert(editingId ? 'Équipement modifié avec succès !' : 'Équipement ajouté avec succès !');\n    } catch (err) {\n      console.error(\"Erreur lors de l'opération:\", err);\n      alert(`Erreur: ${err.message}`);\n    } finally {\n      setSubmitting(false);\n    }\n  };\n  const handleEdit = equipement => {\n    var _equipement$fournisse2;\n    setEditingId(equipement.id);\n    setFormData({\n      nom: equipement.nom,\n      type: equipement.type,\n      numero_serie: equipement.numero_serie || equipement.numeroSerie || '',\n      statut: equipement.statut,\n      date_achat: equipement.date_achat || '',\n      fournisseur_id: ((_equipement$fournisse2 = equipement.fournisseur) === null || _equipement$fournisse2 === void 0 ? void 0 : _equipement$fournisse2.id) || equipement.fournisseur_id || '',\n      date_debut_garantie: equipement.date_debut_garantie || '',\n      date_fin_garantie: equipement.date_fin_garantie || '',\n      en_stock: equipement.en_stock !== undefined ? equipement.en_stock : true,\n      stock_actuel: equipement.stock_actuel || 1,\n      stock_max: equipement.stock_max || 1,\n      stock_min: equipement.stock_min || 1,\n      categorie_id: equipement.categorie_id || ''\n    });\n    setShowModal(true);\n  };\n  const handleDelete = async id => {\n    if (!window.confirm(\"Êtes-vous sûr de vouloir supprimer cet équipement ?\")) {\n      return;\n    }\n    try {\n      const token = localStorage.getItem('authToken');\n      const headers = {\n        'Content-Type': 'application/json'\n      };\n      if (token) {\n        headers['Authorization'] = `Bearer ${token}`;\n      }\n      const response = await fetch(`http://localhost:8081/api/equipements/${id}`, {\n        method: 'DELETE',\n        headers: headers\n      });\n      if (!response.ok) {\n        throw new Error(`Erreur HTTP: ${response.status}`);\n      }\n      setEquipements(prev => prev.filter(eq => eq.id !== id));\n      setFilteredEquipements(prev => prev.filter(eq => eq.id !== id));\n      alert('Équipement supprimé avec succès !');\n    } catch (err) {\n      console.error(\"Erreur lors de la suppression:\", err);\n      alert(`Erreur lors de la suppression: ${err.message}`);\n    }\n  };\n  const handleSearchChange = e => {\n    setSearchTerm(e.target.value);\n  };\n  const formatDate = dateInput => {\n    if (!dateInput || dateInput === null || dateInput === undefined || dateInput === '') {\n      return 'N/A';\n    }\n    try {\n      const dateObj = new Date(dateInput);\n      if (!isNaN(dateObj.getTime())) {\n        return dateObj.toLocaleDateString('fr-FR');\n      }\n      return 'N/A';\n    } catch (e) {\n      console.error(\"Erreur de formatage de date\", e, \"pour la valeur:\", dateInput);\n      return 'N/A';\n    }\n  };\n  const handleExportExcel = () => {\n    if (!Array.isArray(filteredEquipements) || filteredEquipements.length === 0) {\n      alert(\"Aucune donnée à exporter\");\n      return;\n    }\n    const columns = [\"Nom\", \"Type\", \"Numéro de série\", \"Statut\", \"Date d'achat\", \"Date fin garantie\", \"Fournisseur\", \"Actions\"];\n    const rows = filteredEquipements.map(eq => {\n      var _eq$fournisseur;\n      return [eq.nom || \"\", eq.type || \"\", eq.numero_serie || eq.numeroSerie || \"\", eq.statut || \"\", formatDate(eq.dateAchat || eq.date_achat) || \"\", formatDate(eq.dateFinGarantie || eq.date_fin_garantie) || \"\", ((_eq$fournisseur = eq.fournisseur) === null || _eq$fournisseur === void 0 ? void 0 : _eq$fournisseur.nom) || \"\", \"\"];\n    });\n    const worksheet = XLSX.utils.aoa_to_sheet([columns, ...rows]);\n    const workbook = XLSX.utils.book_new();\n    XLSX.utils.book_append_sheet(workbook, worksheet, \"Equipements\");\n    XLSX.writeFile(workbook, \"equipements.xlsx\");\n  };\n  const handleExportPDF = () => {\n    if (!Array.isArray(filteredEquipements) || filteredEquipements.length === 0) {\n      alert(\"Aucune donnée à exporter\");\n      return;\n    }\n    const doc = new jsPDF();\n    doc.text(\"Liste des équipements\", 14, 16);\n    const columns = [\"Nom\", \"Type\", \"Numéro de série\", \"Statut\", \"Date d'achat\", \"Date fin garantie\", \"Fournisseur\"];\n    const rows = filteredEquipements.map(eq => {\n      var _eq$fournisseur2;\n      return [eq.nom || \"\", eq.type || \"\", eq.numero_serie || eq.numeroSerie || \"\", eq.statut || \"\", formatDate(eq.dateAchat || eq.date_achat) || \"\", formatDate(eq.dateFinGarantie || eq.date_fin_garantie) || \"\", ((_eq$fournisseur2 = eq.fournisseur) === null || _eq$fournisseur2 === void 0 ? void 0 : _eq$fournisseur2.nom) || \"\"];\n    });\n    autoTable(doc, {\n      head: [columns],\n      body: rows,\n      startY: 22,\n      styles: {\n        fontSize: 10\n      }\n    });\n    doc.save(\"equipements.pdf\");\n  };\n  const handlePrint = () => {\n    window.print();\n  };\n\n  // Fonctions pour calculer les statistiques\n  const getStatutStats = () => {\n    const statutCounts = {};\n    filteredEquipements.forEach(eq => {\n      const statut = eq.statut || 'Non défini';\n      statutCounts[statut] = (statutCounts[statut] || 0) + 1;\n    });\n    return statutCounts;\n  };\n  const getTypeStats = () => {\n    const typeCounts = {};\n    filteredEquipements.forEach(eq => {\n      const type = eq.type || 'Non défini';\n      typeCounts[type] = (typeCounts[type] || 0) + 1;\n    });\n    return typeCounts;\n  };\n\n  // Configuration des graphiques\n  const statutStats = getStatutStats();\n  const typeStats = getTypeStats();\n  const statutChartData = {\n    labels: Object.keys(statutStats),\n    datasets: [{\n      label: 'Nombre d\\'équipements',\n      data: Object.values(statutStats),\n      backgroundColor: ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b'],\n      borderColor: ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b'],\n      borderWidth: 1\n    }]\n  };\n  const typeChartData = {\n    labels: Object.keys(typeStats),\n    datasets: [{\n      data: Object.values(typeStats),\n      backgroundColor: ['#1976d2', '#43a047', '#d32f2f', '#ffa000', '#9c27b0', '#00acc1', '#8bc34a'],\n      borderWidth: 2,\n      borderColor: '#fff'\n    }]\n  };\n  const chartOptions = {\n    responsive: true,\n    plugins: {\n      legend: {\n        position: 'top'\n      },\n      title: {\n        display: true,\n        text: 'Répartition par statut'\n      }\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        ticks: {\n          stepSize: 1\n        }\n      }\n    }\n  };\n  const doughnutOptions = {\n    responsive: true,\n    plugins: {\n      legend: {\n        position: 'right'\n      },\n      title: {\n        display: true,\n        text: 'Répartition par type'\n      }\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"equipements-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sidebar\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"logo-section\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: logo,\n          alt: \"Logo Entreprise\",\n          className: \"company-logo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 506,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 505,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"main-menu\",\n        children: /*#__PURE__*/_jsxDEV(\"ul\", {\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/\",\n              className: location.pathname === '/' ? 'home-active active' : '',\n              children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: faLaptop,\n                style: {\n                  marginRight: 8\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 512,\n                columnNumber: 17\n              }, this), \" Accueil\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 511,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 510,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/equipements\",\n              className: location.pathname === '/equipements' ? 'active' : '',\n              children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: faLaptop,\n                style: {\n                  marginRight: 8\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 517,\n                columnNumber: 17\n              }, this), \" G\\xE9rer les \\xE9quipements\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 516,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 515,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/employes\",\n              className: location.pathname === '/employes' ? 'active' : '',\n              children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: faUsers,\n                style: {\n                  marginRight: 8\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 522,\n                columnNumber: 17\n              }, this), \" G\\xE9rer les employ\\xE9s\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 521,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 520,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/affectations\",\n              className: location.pathname === '/affectations' ? 'active' : '',\n              children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: faExchangeAlt,\n                style: {\n                  marginRight: 8\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 527,\n                columnNumber: 17\n              }, this), \" Suivi des affectations\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 526,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 525,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/fournisseurs\",\n              className: location.pathname === '/fournisseurs' ? 'active' : '',\n              children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: faTruck,\n                style: {\n                  marginRight: 8\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 532,\n                columnNumber: 17\n              }, this), \" G\\xE9rer les fournisseurs\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 531,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 530,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 509,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 508,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"secondary-links\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/profil\",\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faUserCog,\n            style: {\n              marginRight: 8\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 539,\n            columnNumber: 13\n          }, this), \" Mon profil & param\\xE8tres\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 538,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 537,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: 'auto',\n          padding: '20px 0 0 0',\n          textAlign: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"logout-btn\",\n          onClick: () => {\n            localStorage.removeItem('authToken');\n            window.location.href = '/';\n          },\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faSignOutAlt,\n            style: {\n              marginRight: 8\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 550,\n            columnNumber: 13\n          }, this), \" D\\xE9connexion\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 543,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 542,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 504,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"equipements-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-header-row\",\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between',\n          marginBottom: 32\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-bar\",\n          style: {\n            flex: 1,\n            maxWidth: 480,\n            marginRight: 24,\n            position: 'relative'\n          },\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faSearch,\n            style: {\n              position: 'absolute',\n              left: 14,\n              top: '50%',\n              transform: 'translateY(-50%)',\n              color: '#1976d2',\n              fontSize: '1.1rem',\n              opacity: 0.8\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 560,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Recherche par mot cl\\xE9\",\n            value: searchTerm,\n            onChange: handleSearchChange,\n            style: {\n              width: '100%',\n              padding: '10px 16px 10px 38px',\n              borderRadius: 8,\n              border: '1px solid #dbeafe',\n              fontSize: '1rem',\n              boxShadow: '0 2px 8px rgba(21,101,192,0.06)'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 561,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 559,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"profile-block\",\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 20\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"notification-icon\",\n            style: {\n              position: 'relative',\n              marginRight: 8,\n              cursor: 'pointer'\n            },\n            children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n              icon: faBell,\n              style: {\n                fontSize: '1.3rem',\n                color: '#1976d2'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 571,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                position: 'absolute',\n                top: -6,\n                right: -6,\n                background: '#e74a3b',\n                color: '#fff',\n                borderRadius: '50%',\n                fontSize: '0.7rem',\n                padding: '2px 6px',\n                fontWeight: 600\n              },\n              children: \"3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 572,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 570,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              fontWeight: 500,\n              color: '#2e3a4e',\n              fontSize: '1rem',\n              marginRight: 8\n            },\n            children: \"Responsable IT\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 574,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faUserCog,\n            style: {\n              fontSize: '1.5rem',\n              color: '#1976d2',\n              background: '#fff',\n              borderRadius: '50%',\n              padding: 6,\n              boxShadow: '0 2px 8px rgba(21,101,192,0.10)'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 575,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 569,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 558,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"equipements-title\",\n        children: [\"Liste des \\xE9quipements (\", filteredEquipements.length, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 579,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: 16,\n          marginBottom: 24\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          style: {\n            fontWeight: 700,\n            fontSize: '1rem',\n            borderRadius: 10,\n            background: 'linear-gradient(90deg, #1976d2 0%, #42a5f5 100%)',\n            color: '#fff',\n            boxShadow: '0 4px 16px rgba(21,101,192,0.18)'\n          },\n          onClick: () => {\n            setEditingId(null);\n            setShowModal(true);\n          },\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faPlus,\n            style: {\n              marginRight: 8\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 592,\n            columnNumber: 13\n          }, this), \"Ajouter un \\xE9quipement\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 584,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          style: {\n            fontWeight: 700,\n            fontSize: '1rem',\n            borderRadius: 10,\n            background: 'linear-gradient(90deg, #43a047 0%, #66bb6a 100%)',\n            color: '#fff',\n            boxShadow: '0 4px 16px rgba(67,160,71,0.18)'\n          },\n          onClick: handleExportExcel,\n          children: \"Exporter Excel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 595,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          style: {\n            fontWeight: 700,\n            fontSize: '1rem',\n            borderRadius: 10,\n            background: 'linear-gradient(90deg, #d32f2f 0%, #e57373 100%)',\n            color: '#fff',\n            boxShadow: '0 4px 16px rgba(211,47,47,0.18)'\n          },\n          onClick: handleExportPDF,\n          children: \"Exporter PDF\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 598,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          style: {\n            fontWeight: 700,\n            fontSize: '1rem',\n            borderRadius: 10,\n            background: 'linear-gradient(90deg, #ffa000 0%, #ffd54f 100%)',\n            color: '#fff',\n            boxShadow: '0 4px 16px rgba(255,160,0,0.18)'\n          },\n          onClick: handlePrint,\n          children: \"Imprimer\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 601,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          style: {\n            fontWeight: 700,\n            fontSize: '1rem',\n            borderRadius: 10,\n            background: 'linear-gradient(90deg, #9c27b0 0%, #ba68c8 100%)',\n            color: '#fff',\n            boxShadow: '0 4px 16px rgba(156,39,176,0.18)'\n          },\n          onClick: () => setShowStats(!showStats),\n          children: [showStats ? 'Masquer' : 'Afficher', \" les statistiques\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 604,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 583,\n        columnNumber: 9\n      }, this), showStats && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: '#fff',\n          borderRadius: 12,\n          padding: 24,\n          marginBottom: 24,\n          boxShadow: '0 4px 16px rgba(0,0,0,0.1)',\n          border: '1px solid #e0e0e0'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            color: '#1976d2',\n            marginBottom: 24,\n            fontSize: '1.3rem',\n            fontWeight: 700,\n            textAlign: 'center'\n          },\n          children: \"\\uD83D\\uDCCA Statistiques des \\xE9quipements\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 623,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'grid',\n            gridTemplateColumns: '1fr 1fr',\n            gap: 32,\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: '#fafafa',\n              padding: 20,\n              borderRadius: 8,\n              border: '1px solid #e0e0e0',\n              height: '350px'\n            },\n            children: /*#__PURE__*/_jsxDEV(Bar, {\n              data: statutChartData,\n              options: chartOptions\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 642,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 635,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: '#fafafa',\n              padding: 20,\n              borderRadius: 8,\n              border: '1px solid #e0e0e0',\n              height: '350px'\n            },\n            children: /*#__PURE__*/_jsxDEV(Doughnut, {\n              data: typeChartData,\n              options: doughnutOptions\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 653,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 646,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 633,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: 24,\n            padding: 16,\n            background: '#f8f9fa',\n            borderRadius: 8,\n            border: '1px solid #dee2e6'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              color: '#495057',\n              marginBottom: 12,\n              fontSize: '1.1rem'\n            },\n            children: \"\\uD83D\\uDCC8 R\\xE9sum\\xE9\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 665,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'grid',\n              gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n              gap: 16\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center',\n                padding: 12,\n                background: '#fff',\n                borderRadius: 6,\n                border: '1px solid #e9ecef'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '2rem',\n                  fontWeight: 'bold',\n                  color: '#1976d2'\n                },\n                children: filteredEquipements.length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 668,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: '#6c757d',\n                  fontSize: '0.9rem'\n                },\n                children: \"Total \\xE9quipements\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 671,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 667,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center',\n                padding: 12,\n                background: '#fff',\n                borderRadius: 6,\n                border: '1px solid #e9ecef'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '2rem',\n                  fontWeight: 'bold',\n                  color: '#28a745'\n                },\n                children: statutStats['DISPONIBLE'] || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 674,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: '#6c757d',\n                  fontSize: '0.9rem'\n                },\n                children: \"Disponibles\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 677,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 673,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center',\n                padding: 12,\n                background: '#fff',\n                borderRadius: 6,\n                border: '1px solid #e9ecef'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '2rem',\n                  fontWeight: 'bold',\n                  color: '#ffc107'\n                },\n                children: statutStats['EN_MAINTENANCE'] || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 680,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: '#6c757d',\n                  fontSize: '0.9rem'\n                },\n                children: \"En maintenance\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 683,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 679,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center',\n                padding: 12,\n                background: '#fff',\n                borderRadius: 6,\n                border: '1px solid #e9ecef'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '2rem',\n                  fontWeight: 'bold',\n                  color: '#dc3545'\n                },\n                children: statutStats['HORS_SERVICE'] || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 686,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: '#6c757d',\n                  fontSize: '0.9rem'\n                },\n                children: \"Hors service\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 689,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 685,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 666,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 658,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 615,\n        columnNumber: 11\n      }, this), showModal && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          backgroundColor: 'rgba(0, 0, 0, 0.5)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 1000\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            backgroundColor: 'white',\n            borderRadius: 12,\n            padding: 32,\n            maxWidth: 600,\n            width: '90%',\n            maxHeight: '90vh',\n            overflowY: 'auto',\n            boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              marginBottom: 24\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                margin: 0,\n                color: '#1976d2',\n                fontSize: '1.5rem',\n                fontWeight: 700\n              },\n              children: editingId ? 'Modifier un équipement' : 'Ajouter un nouvel équipement'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 721,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                setShowModal(false);\n                setEditingId(null);\n              },\n              style: {\n                background: 'none',\n                border: 'none',\n                fontSize: '1.5rem',\n                color: '#666',\n                cursor: 'pointer',\n                padding: 8,\n                borderRadius: '50%'\n              },\n              children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: faTimes\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 739,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 724,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 720,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSubmit,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: '1fr 1fr',\n                gap: 16,\n                marginBottom: 16\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: 8,\n                    fontWeight: 600,\n                    color: '#333'\n                  },\n                  children: \"Nom de l'\\xE9quipement *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 746,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"nom\",\n                  value: formData.nom,\n                  onChange: handleInputChange,\n                  required: true,\n                  style: {\n                    width: '100%',\n                    padding: '12px',\n                    border: '2px solid #e0e0e0',\n                    borderRadius: 8,\n                    fontSize: '1rem',\n                    transition: 'border-color 0.2s'\n                  },\n                  onFocus: e => e.target.style.borderColor = '#1976d2',\n                  onBlur: e => e.target.style.borderColor = '#e0e0e0'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 749,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 745,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: 8,\n                    fontWeight: 600,\n                    color: '#333'\n                  },\n                  children: \"Type *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 769,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"type\",\n                  value: formData.type,\n                  onChange: handleInputChange,\n                  required: true,\n                  style: {\n                    width: '100%',\n                    padding: '12px',\n                    border: '2px solid #e0e0e0',\n                    borderRadius: 8,\n                    fontSize: '1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"S\\xE9lectionner un type\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 785,\n                    columnNumber: 23\n                  }, this), typeOptions.map(type => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: type,\n                    children: type\n                  }, type, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 787,\n                    columnNumber: 25\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 772,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 768,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 744,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: '1fr 1fr',\n                gap: 16,\n                marginBottom: 16\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: 8,\n                    fontWeight: 600,\n                    color: '#333'\n                  },\n                  children: \"Num\\xE9ro de s\\xE9rie\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 795,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"numero_serie\",\n                  value: formData.numero_serie,\n                  onChange: handleInputChange,\n                  style: {\n                    width: '100%',\n                    padding: '12px',\n                    border: '2px solid #e0e0e0',\n                    borderRadius: 8,\n                    fontSize: '1rem'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 798,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 794,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: 8,\n                    fontWeight: 600,\n                    color: '#333'\n                  },\n                  children: \"Statut *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 814,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"statut\",\n                  value: formData.statut,\n                  onChange: handleInputChange,\n                  required: true,\n                  style: {\n                    width: '100%',\n                    padding: '12px',\n                    border: '2px solid #e0e0e0',\n                    borderRadius: 8,\n                    fontSize: '1rem'\n                  },\n                  children: statutOptions.map(statut => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: statut,\n                    children: statut\n                  }, statut, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 831,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 817,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 813,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 793,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: '1fr 1fr',\n                gap: 16,\n                marginBottom: 16\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: 8,\n                    fontWeight: 600,\n                    color: '#333'\n                  },\n                  children: \"Date d'achat\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 839,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  name: \"date_achat\",\n                  value: formData.date_achat,\n                  onChange: handleInputChange,\n                  style: {\n                    width: '100%',\n                    padding: '12px',\n                    border: '2px solid #e0e0e0',\n                    borderRadius: 8,\n                    fontSize: '1rem'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 842,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 838,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: 8,\n                    fontWeight: 600,\n                    color: '#333'\n                  },\n                  children: \"Fournisseur\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 858,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"fournisseur_id\",\n                  value: formData.fournisseur_id,\n                  onChange: handleInputChange,\n                  style: {\n                    width: '100%',\n                    padding: '12px',\n                    border: '2px solid #e0e0e0',\n                    borderRadius: 8,\n                    fontSize: '1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"S\\xE9lectionner un fournisseur\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 873,\n                    columnNumber: 23\n                  }, this), fournisseurs.map(fournisseur => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: fournisseur.id,\n                    children: fournisseur.nom\n                  }, fournisseur.id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 875,\n                    columnNumber: 25\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 861,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 857,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 837,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: '1fr 1fr',\n                gap: 16,\n                marginBottom: 16\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: 8,\n                    fontWeight: 600,\n                    color: '#333'\n                  },\n                  children: \"D\\xE9but de garantie\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 885,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  name: \"date_debut_garantie\",\n                  value: formData.date_debut_garantie,\n                  onChange: handleInputChange,\n                  style: {\n                    width: '100%',\n                    padding: '12px',\n                    border: '2px solid #e0e0e0',\n                    borderRadius: 8,\n                    fontSize: '1rem'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 888,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 884,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: 8,\n                    fontWeight: 600,\n                    color: '#333'\n                  },\n                  children: \"Fin de garantie\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 904,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  name: \"date_fin_garantie\",\n                  value: formData.date_fin_garantie,\n                  onChange: handleInputChange,\n                  style: {\n                    width: '100%',\n                    padding: '12px',\n                    border: '2px solid #e0e0e0',\n                    borderRadius: 8,\n                    fontSize: '1rem'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 907,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 903,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 883,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: '1fr 1fr 1fr',\n                gap: 16,\n                marginBottom: 16\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: 8,\n                    fontWeight: 600,\n                    color: '#333'\n                  },\n                  children: \"Stock actuel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 925,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  name: \"stock_actuel\",\n                  value: formData.stock_actuel,\n                  onChange: handleInputChange,\n                  min: \"0\",\n                  style: {\n                    width: '100%',\n                    padding: '12px',\n                    border: '2px solid #e0e0e0',\n                    borderRadius: 8,\n                    fontSize: '1rem'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 928,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 924,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: 8,\n                    fontWeight: 600,\n                    color: '#333'\n                  },\n                  children: \"Stock maximum\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 945,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  name: \"stock_max\",\n                  value: formData.stock_max,\n                  onChange: handleInputChange,\n                  min: \"1\",\n                  style: {\n                    width: '100%',\n                    padding: '12px',\n                    border: '2px solid #e0e0e0',\n                    borderRadius: 8,\n                    fontSize: '1rem'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 948,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 944,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: 8,\n                    fontWeight: 600,\n                    color: '#333'\n                  },\n                  children: \"Stock minimum\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 965,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  name: \"stock_min\",\n                  value: formData.stock_min,\n                  onChange: handleInputChange,\n                  min: \"0\",\n                  style: {\n                    width: '100%',\n                    padding: '12px',\n                    border: '2px solid #e0e0e0',\n                    borderRadius: 8,\n                    fontSize: '1rem'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 968,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 964,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 923,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: 24\n              },\n              children: /*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  fontWeight: 600,\n                  color: '#333',\n                  cursor: 'pointer'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  name: \"en_stock\",\n                  checked: formData.en_stock,\n                  onChange: handleInputChange,\n                  style: {\n                    marginRight: 8,\n                    transform: 'scale(1.2)'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 987,\n                  columnNumber: 21\n                }, this), \"\\xC9quipement en stock\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 986,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 985,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                gap: 12,\n                justifyContent: 'flex-end'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => {\n                  setShowModal(false);\n                  setEditingId(null);\n                },\n                style: {\n                  padding: '12px 24px',\n                  border: '2px solid #e0e0e0',\n                  borderRadius: 8,\n                  background: 'white',\n                  color: '#666',\n                  fontSize: '1rem',\n                  fontWeight: 600,\n                  cursor: 'pointer'\n                },\n                children: \"Annuler\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 999,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                disabled: submitting,\n                style: {\n                  padding: '12px 24px',\n                  border: 'none',\n                  borderRadius: 8,\n                  background: submitting ? '#ccc' : 'linear-gradient(90deg, #1976d2 0%, #42a5f5 100%)',\n                  color: 'white',\n                  fontSize: '1rem',\n                  fontWeight: 600,\n                  cursor: submitting ? 'not-allowed' : 'pointer',\n                  boxShadow: '0 4px 16px rgba(21,101,192,0.18)'\n                },\n                children: submitting ? editingId ? 'Modification en cours...' : 'Ajout en cours...' : editingId ? 'Modifier l\\'équipement' : 'Ajouter l\\'équipement'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1018,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 998,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 743,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 710,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 698,\n        columnNumber: 11\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '40px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Chargement des \\xE9quipements...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1046,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1045,\n        columnNumber: 11\n      }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '40px',\n          color: '#e74a3b'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"Erreur lors du chargement : \", error]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1050,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.location.reload(),\n          style: {\n            marginTop: '10px',\n            padding: '8px 16px',\n            background: '#1976d2',\n            color: 'white',\n            border: 'none',\n            borderRadius: '4px',\n            cursor: 'pointer'\n          },\n          children: \"Actualiser la page\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1051,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1049,\n        columnNumber: 11\n      }, this) : !Array.isArray(filteredEquipements) || filteredEquipements.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '40px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: searchTerm ? 'Aucun résultat trouvé' : 'Aucun équipement trouvé'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1060,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1059,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"table\", {\n        className: \"equipements-table\",\n        children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Nom\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1066,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Type\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1067,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Num\\xE9ro de s\\xE9rie\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1068,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Statut\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1069,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Date d'achat\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1070,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Date fin garantie\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1071,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Fournisseur\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1072,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Actions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1073,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1065,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1064,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n          children: filteredEquipements.map((eq, index) => {\n            var _eq$fournisseur3;\n            const numeroSerie = eq.numero_serie || eq.numeroSerie || 'N/A';\n            const dateAchat = formatDate(eq.dateAchat || eq.date_achat);\n            const dateFinGarantie = formatDate(eq.dateFinGarantie || eq.date_fin_garantie);\n            const fournisseur = ((_eq$fournisseur3 = eq.fournisseur) === null || _eq$fournisseur3 === void 0 ? void 0 : _eq$fournisseur3.nom) || eq.fournisseur_id || 'Non spécifié';\n            return /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                children: eq.nom || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1085,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: eq.type || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1086,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: numeroSerie\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1087,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: eq.statut || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1088,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: dateAchat\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1089,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: dateFinGarantie\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1090,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: fournisseur\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1091,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    gap: '8px'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleEdit(eq),\n                    style: {\n                      background: '#1976d2',\n                      color: 'white',\n                      border: 'none',\n                      borderRadius: '4px',\n                      padding: '6px 12px',\n                      cursor: 'pointer',\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '4px'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                      icon: faEdit\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1108,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Modifier\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1109,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1094,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleDelete(eq.id),\n                    style: {\n                      background: '#e74a3b',\n                      color: 'white',\n                      border: 'none',\n                      borderRadius: '4px',\n                      padding: '6px 12px',\n                      cursor: 'pointer',\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '4px'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                      icon: faTrash\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1125,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Supprimer\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1126,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1111,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1093,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1092,\n                columnNumber: 21\n              }, this)]\n            }, eq.id || index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1084,\n              columnNumber: 19\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1076,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1063,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 556,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 502,\n    columnNumber: 5\n  }, this);\n}\n_s(Equipements, \"1cxR1r6yJnKkkIU+QDQbjlFE5TQ=\", false, function () {\n  return [useLocation];\n});\n_c = Equipements;\nvar _c;\n$RefreshReg$(_c, \"Equipements\");", "map": {"version": 3, "names": ["Link", "useLocation", "logo", "FontAwesomeIcon", "faLaptop", "faUsers", "faExchangeAlt", "faTruck", "faUserCog", "faSignOutAlt", "faSearch", "faBell", "faTimes", "faPlus", "faEdit", "faTrash", "useEffect", "useState", "jsPDF", "autoTable", "XLSX", "Chart", "ChartJS", "CategoryScale", "LinearScale", "BarElement", "Title", "<PERSON><PERSON><PERSON>", "Legend", "ArcElement", "Bar", "Doughnut", "jsxDEV", "_jsxDEV", "register", "Equipements", "_s", "location", "equipements", "setEquipements", "filteredEquipements", "setFilteredEquipements", "loading", "setLoading", "error", "setError", "showModal", "setShowModal", "fournisseurs", "setFournisseurs", "categories", "setCategories", "submitting", "setSubmitting", "searchTerm", "setSearchTerm", "editingId", "setEditingId", "showStats", "setShowStats", "formData", "setFormData", "nom", "type", "numero_serie", "statut", "date_achat", "fournisseur_id", "date_debut_garantie", "date_fin_garantie", "en_stock", "stock_actuel", "stock_max", "stock_min", "categorie_id", "statutOptions", "typeOptions", "loadEquipements", "loadFournisseurs", "loadCategories", "filtered", "filter", "equipement", "_equipement$fournisse", "toLowerCase", "includes", "<PERSON><PERSON><PERSON><PERSON>", "console", "log", "token", "localStorage", "getItem", "headers", "fetch", "method", "then", "res", "status", "ok", "Error", "json", "data", "Array", "isArray", "warn", "catch", "err", "message", "length", "handleInputChange", "e", "name", "value", "checked", "target", "prev", "handleSubmit", "preventDefault", "dataToSend", "url", "response", "body", "JSON", "stringify", "result", "map", "eq", "id", "alert", "handleEdit", "_equipement$fournisse2", "numeroSerie", "undefined", "handleDelete", "window", "confirm", "handleSearchChange", "formatDate", "dateInput", "date<PERSON><PERSON>j", "Date", "isNaN", "getTime", "toLocaleDateString", "handleExportExcel", "columns", "rows", "_eq$fournisseur", "dateAchat", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "worksheet", "utils", "aoa_to_sheet", "workbook", "book_new", "book_append_sheet", "writeFile", "handleExportPDF", "doc", "text", "_eq$fournisseur2", "head", "startY", "styles", "fontSize", "save", "handlePrint", "print", "getStatutStats", "statutCounts", "for<PERSON>ach", "getTypeStats", "typeCounts", "statutStats", "typeStats", "statutChartData", "labels", "Object", "keys", "datasets", "label", "values", "backgroundColor", "borderColor", "borderWidth", "typeChartData", "chartOptions", "responsive", "plugins", "legend", "position", "title", "display", "scales", "y", "beginAtZero", "ticks", "stepSize", "doughnutOptions", "className", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "pathname", "icon", "style", "marginRight", "marginTop", "padding", "textAlign", "onClick", "removeItem", "href", "alignItems", "justifyContent", "marginBottom", "flex", "max<PERSON><PERSON><PERSON>", "left", "top", "transform", "color", "opacity", "placeholder", "onChange", "width", "borderRadius", "border", "boxShadow", "gap", "cursor", "right", "background", "fontWeight", "gridTemplateColumns", "height", "options", "bottom", "zIndex", "maxHeight", "overflowY", "margin", "onSubmit", "required", "transition", "onFocus", "onBlur", "min", "disabled", "reload", "index", "_eq$fournisseur3", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/gestion-equipement-frontend/src/pages/Equipements.js"], "sourcesContent": ["import './Equipements.css';\nimport { Link, useLocation } from 'react-router-dom';\nimport logo from '../assets/images/asment logo.jpg';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { faLaptop, faUsers, faExchangeAlt, faTruck, faUserCog, faSignOutAlt, faSearch, faBell, faTimes, faPlus, faEdit, faTrash } from '@fortawesome/free-solid-svg-icons';\nimport { useEffect, useState } from \"react\";\nimport jsPDF from \"jspdf\";\nimport autoTable from \"jspdf-autotable\";\nimport * as XLSX from \"xlsx\";\nimport { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement } from 'chart.js';\nimport { Bar, Doughnut } from 'react-chartjs-2';\n\nChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement);\n\nexport default function Equipements() {\n  const location = useLocation();\n  const [equipements, setEquipements] = useState([]);\n  const [filteredEquipements, setFilteredEquipements] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [showModal, setShowModal] = useState(false);\n  const [fournisseurs, setFournisseurs] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [submitting, setSubmitting] = useState(false);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [editingId, setEditingId] = useState(null);\n  const [showStats, setShowStats] = useState(false);\n\n  // État du formulaire\n  const [formData, setFormData] = useState({\n    nom: '',\n    type: '',\n    numero_serie: '',\n    statut: 'DISPONIBLE',\n    date_achat: '',\n    fournisseur_id: '',\n    date_debut_garantie: '',\n    date_fin_garantie: '',\n    en_stock: true,\n    stock_actuel: 1,\n    stock_max: 1,\n    stock_min: 1,\n    categorie_id: ''\n  });\n\n  // Options pour les statuts et types\n  const statutOptions = ['DISPONIBLE', 'EN_MAINTENANCE', 'OCCUPE', 'HORS_SERVICE'];\n  const typeOptions = ['ORDINATEUR', 'IMPRIMANTE', 'SCANNER', 'TELEPHONE', 'TABLETTE', 'SERVEUR', 'AUTRE'];\n\n  useEffect(() => {\n    loadEquipements();\n    loadFournisseurs();\n    loadCategories();\n  }, []);\n\n  useEffect(() => {\n    if (searchTerm === '') {\n      setFilteredEquipements(equipements);\n    } else {\n      const filtered = equipements.filter(equipement =>\n        equipement.nom.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        equipement.type.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        (equipement.numero_serie && equipement.numero_serie.toLowerCase().includes(searchTerm.toLowerCase())) ||\n        equipement.statut.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        (equipement.fournisseur?.nom && equipement.fournisseur.nom.toLowerCase().includes(searchTerm.toLowerCase()))\n      );\n      setFilteredEquipements(filtered);\n    }\n  }, [searchTerm, equipements]);\n\n  const loadEquipements = () => {\n    console.log(\"Chargement des équipements...\");\n    \n    const token = localStorage.getItem('authToken');\n    const headers = {\n      'Content-Type': 'application/json',\n    };\n    if (token) {\n      headers['Authorization'] = `Bearer ${token}`;\n    }\n\n    fetch(\"http://localhost:8081/api/equipements\", {\n      method: 'GET',\n      headers: headers\n    })\n      .then(res => {\n        console.log(\"Status de la réponse équipements:\", res.status);\n        if (!res.ok) {\n          throw new Error(`Erreur HTTP: ${res.status}`);\n        }\n        return res.json();\n      })\n      .then(data => {\n        console.log(\"Données équipements reçues:\", data);\n        if (Array.isArray(data)) {\n          setEquipements(data);\n          setFilteredEquipements(data);\n          setError(null);\n        } else {\n          console.warn(\"Les données équipements ne sont pas un tableau:\", data);\n          setEquipements([]);\n          setFilteredEquipements([]);\n          setError(\"Format de données incorrect\");\n        }\n        setLoading(false);\n      })\n      .catch(err => {\n        console.error(\"Erreur lors du chargement des équipements:\", err);\n        setEquipements([]);\n        setFilteredEquipements([]);\n        setError(err.message || \"Erreur de chargement\");\n        setLoading(false);\n      });\n  };\n\n  const loadFournisseurs = () => {\n    console.log(\"Chargement des fournisseurs...\");\n\n    const token = localStorage.getItem('authToken');\n    const headers = {\n      'Content-Type': 'application/json',\n    };\n    if (token) {\n      headers['Authorization'] = `Bearer ${token}`;\n    }\n\n    fetch(\"http://localhost:8081/api/fournisseurs\", {\n      method: 'GET',\n      headers: headers\n    })\n      .then(res => {\n        console.log(\"Status de la réponse fournisseurs:\", res.status);\n        if (!res.ok) {\n          throw new Error(`Erreur HTTP: ${res.status}`);\n        }\n        return res.json();\n      })\n      .then(data => {\n        console.log(\"Données fournisseurs reçues:\", data);\n        if (Array.isArray(data)) {\n          setFournisseurs(data);\n          console.log(\"Fournisseurs chargés:\", data.length, \"éléments\");\n        } else {\n          console.warn(\"Les données fournisseurs ne sont pas un tableau:\", data);\n          setFournisseurs([]);\n        }\n      })\n      .catch(err => {\n        console.error(\"Erreur lors du chargement des fournisseurs:\", err);\n        setFournisseurs([]);\n      });\n  };\n\n  const loadCategories = () => {\n    const token = localStorage.getItem('authToken');\n    const headers = {\n      'Content-Type': 'application/json',\n    };\n    if (token) {\n      headers['Authorization'] = `Bearer ${token}`;\n    }\n\n    fetch(\"http://localhost:8081/api/categories\", {\n      method: 'GET',\n      headers: headers\n    })\n      .then(res => res.ok ? res.json() : [])\n      .then(data => setCategories(Array.isArray(data) ? data : []))\n      .catch(err => {\n        console.error(\"Erreur lors du chargement des catégories:\", err);\n        setCategories([]);\n      });\n  };\n\n  const handleInputChange = (e) => {\n    const { name, value, type, checked } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setSubmitting(true);\n\n    console.log(\"handleSubmit - editingId:\", editingId);\n    console.log(\"handleSubmit - formData:\", formData);\n\n    const dataToSend = {\n      ...formData,\n      fournisseur_id: formData.fournisseur_id || null\n    };\n\n    try {\n      const token = localStorage.getItem('authToken');\n      const headers = {\n        'Content-Type': 'application/json',\n      };\n      if (token) {\n        headers['Authorization'] = `Bearer ${token}`;\n      }\n\n      const url = editingId\n        ? `http://localhost:8081/api/equipements/${editingId}`\n        : \"http://localhost:8081/api/equipements\";\n\n      console.log(\"URL générée:\", url);\n      const method = editingId ? 'PUT' : 'POST';\n\n      const response = await fetch(url, {\n        method: method,\n        headers: headers,\n        body: JSON.stringify(dataToSend)\n      });\n\n      if (!response.ok) {\n        throw new Error(`Erreur HTTP: ${response.status}`);\n      }\n\n      const result = await response.json();\n      \n      if (editingId) {\n        setEquipements(prev => prev.map(eq => eq.id === editingId ? result : eq));\n        setFilteredEquipements(prev => prev.map(eq => eq.id === editingId ? result : eq));\n      } else {\n        setEquipements(prev => [...prev, result]);\n        setFilteredEquipements(prev => [...prev, result]);\n      }\n      \n      setFormData({\n        nom: '',\n        type: '',\n        numero_serie: '',\n        statut: 'DISPONIBLE',\n        date_achat: '',\n        fournisseur_id: '',\n        date_debut_garantie: '',\n        date_fin_garantie: '',\n        en_stock: true,\n        stock_actuel: 1,\n        stock_max: 1,\n        stock_min: 1,\n        categorie_id: ''\n      });\n      setShowModal(false);\n      setEditingId(null);\n      \n      alert(editingId ? 'Équipement modifié avec succès !' : 'Équipement ajouté avec succès !');\n    } catch (err) {\n      console.error(\"Erreur lors de l'opération:\", err);\n      alert(`Erreur: ${err.message}`);\n    } finally {\n      setSubmitting(false);\n    }\n  };\n\n  const handleEdit = (equipement) => {\n    setEditingId(equipement.id);\n    setFormData({\n      nom: equipement.nom,\n      type: equipement.type,\n      numero_serie: equipement.numero_serie || equipement.numeroSerie || '',\n      statut: equipement.statut,\n      date_achat: equipement.date_achat || '',\n      fournisseur_id: equipement.fournisseur?.id || equipement.fournisseur_id || '',\n      date_debut_garantie: equipement.date_debut_garantie || '',\n      date_fin_garantie: equipement.date_fin_garantie || '',\n      en_stock: equipement.en_stock !== undefined ? equipement.en_stock : true,\n      stock_actuel: equipement.stock_actuel || 1,\n      stock_max: equipement.stock_max || 1,\n      stock_min: equipement.stock_min || 1,\n      categorie_id: equipement.categorie_id || ''\n    });\n    setShowModal(true);\n  };\n\n  const handleDelete = async (id) => {\n    if (!window.confirm(\"Êtes-vous sûr de vouloir supprimer cet équipement ?\")) {\n      return;\n    }\n\n    try {\n      const token = localStorage.getItem('authToken');\n      const headers = {\n        'Content-Type': 'application/json',\n      };\n      if (token) {\n        headers['Authorization'] = `Bearer ${token}`;\n      }\n\n      const response = await fetch(`http://localhost:8081/api/equipements/${id}`, {\n        method: 'DELETE',\n        headers: headers\n      });\n\n      if (!response.ok) {\n        throw new Error(`Erreur HTTP: ${response.status}`);\n      }\n\n      setEquipements(prev => prev.filter(eq => eq.id !== id));\n      setFilteredEquipements(prev => prev.filter(eq => eq.id !== id));\n      \n      alert('Équipement supprimé avec succès !');\n    } catch (err) {\n      console.error(\"Erreur lors de la suppression:\", err);\n      alert(`Erreur lors de la suppression: ${err.message}`);\n    }\n  };\n\n  const handleSearchChange = (e) => {\n    setSearchTerm(e.target.value);\n  };\n\n  const formatDate = (dateInput) => {\n    if (!dateInput || dateInput === null || dateInput === undefined || dateInput === '') {\n      return 'N/A';\n    }\n\n    try {\n      const dateObj = new Date(dateInput);\n      if (!isNaN(dateObj.getTime())) {\n        return dateObj.toLocaleDateString('fr-FR');\n      }\n      return 'N/A';\n    } catch (e) {\n      console.error(\"Erreur de formatage de date\", e, \"pour la valeur:\", dateInput);\n      return 'N/A';\n    }\n  };\n\n  const handleExportExcel = () => {\n    if (!Array.isArray(filteredEquipements) || filteredEquipements.length === 0) {\n      alert(\"Aucune donnée à exporter\");\n      return;\n    }\n    \n    const columns = [\n      \"Nom\",\n      \"Type\",\n      \"Numéro de série\",\n      \"Statut\",\n      \"Date d'achat\",\n      \"Date fin garantie\",\n      \"Fournisseur\",\n      \"Actions\"\n    ];\n    const rows = filteredEquipements.map(eq => [\n      eq.nom || \"\",\n      eq.type || \"\",\n      eq.numero_serie || eq.numeroSerie || \"\",\n      eq.statut || \"\",\n      formatDate(eq.dateAchat || eq.date_achat) || \"\",\n      formatDate(eq.dateFinGarantie || eq.date_fin_garantie) || \"\",\n      eq.fournisseur?.nom || \"\",\n      \"\"\n    ]);\n    const worksheet = XLSX.utils.aoa_to_sheet([columns, ...rows]);\n    const workbook = XLSX.utils.book_new();\n    XLSX.utils.book_append_sheet(workbook, worksheet, \"Equipements\");\n    XLSX.writeFile(workbook, \"equipements.xlsx\");\n  };\n\n  const handleExportPDF = () => {\n    if (!Array.isArray(filteredEquipements) || filteredEquipements.length === 0) {\n      alert(\"Aucune donnée à exporter\");\n      return;\n    }\n    \n    const doc = new jsPDF();\n    doc.text(\"Liste des équipements\", 14, 16);\n    const columns = [\n      \"Nom\",\n      \"Type\",\n      \"Numéro de série\",\n      \"Statut\",\n      \"Date d'achat\",\n      \"Date fin garantie\",\n      \"Fournisseur\"\n    ];\n    const rows = filteredEquipements.map(eq => [\n      eq.nom || \"\",\n      eq.type || \"\",\n      eq.numero_serie || eq.numeroSerie || \"\",\n      eq.statut || \"\",\n      formatDate(eq.dateAchat || eq.date_achat) || \"\",\n      formatDate(eq.dateFinGarantie || eq.date_fin_garantie) || \"\",\n      eq.fournisseur?.nom || \"\"\n    ]);\n    autoTable(doc, {\n      head: [columns],\n      body: rows,\n      startY: 22,\n      styles: { fontSize: 10 }\n    });\n    doc.save(\"equipements.pdf\");\n  };\n\n  const handlePrint = () => {\n    window.print();\n  };\n\n  // Fonctions pour calculer les statistiques\n  const getStatutStats = () => {\n    const statutCounts = {};\n    filteredEquipements.forEach(eq => {\n      const statut = eq.statut || 'Non défini';\n      statutCounts[statut] = (statutCounts[statut] || 0) + 1;\n    });\n    return statutCounts;\n  };\n\n  const getTypeStats = () => {\n    const typeCounts = {};\n    filteredEquipements.forEach(eq => {\n      const type = eq.type || 'Non défini';\n      typeCounts[type] = (typeCounts[type] || 0) + 1;\n    });\n    return typeCounts;\n  };\n\n  // Configuration des graphiques\n  const statutStats = getStatutStats();\n  const typeStats = getTypeStats();\n\n  const statutChartData = {\n    labels: Object.keys(statutStats),\n    datasets: [{\n      label: 'Nombre d\\'équipements',\n      data: Object.values(statutStats),\n      backgroundColor: [\n        '#4e73df',\n        '#1cc88a',\n        '#36b9cc',\n        '#f6c23e',\n        '#e74a3b'\n      ],\n      borderColor: [\n        '#4e73df',\n        '#1cc88a',\n        '#36b9cc',\n        '#f6c23e',\n        '#e74a3b'\n      ],\n      borderWidth: 1\n    }]\n  };\n\n  const typeChartData = {\n    labels: Object.keys(typeStats),\n    datasets: [{\n      data: Object.values(typeStats),\n      backgroundColor: [\n        '#1976d2',\n        '#43a047',\n        '#d32f2f',\n        '#ffa000',\n        '#9c27b0',\n        '#00acc1',\n        '#8bc34a'\n      ],\n      borderWidth: 2,\n      borderColor: '#fff'\n    }]\n  };\n\n  const chartOptions = {\n    responsive: true,\n    plugins: {\n      legend: {\n        position: 'top',\n      },\n      title: {\n        display: true,\n        text: 'Répartition par statut'\n      }\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        ticks: {\n          stepSize: 1\n        }\n      }\n    }\n  };\n\n  const doughnutOptions = {\n    responsive: true,\n    plugins: {\n      legend: {\n        position: 'right',\n      },\n      title: {\n        display: true,\n        text: 'Répartition par type'\n      }\n    }\n  };\n\n  return (\n    <div className=\"equipements-container\">\n      {/* Sidebar */}\n      <div className=\"sidebar\">\n        <div className=\"logo-section\">\n          <img src={logo} alt=\"Logo Entreprise\" className=\"company-logo\" />\n        </div>\n        <nav className=\"main-menu\">\n          <ul>\n            <li>\n              <Link to=\"/\" className={location.pathname === '/' ? 'home-active active' : ''}>\n                <FontAwesomeIcon icon={faLaptop} style={{marginRight:8}} /> Accueil\n              </Link>\n            </li>\n            <li>\n              <Link to=\"/equipements\" className={location.pathname === '/equipements' ? 'active' : ''}>\n                <FontAwesomeIcon icon={faLaptop} style={{marginRight:8}} /> Gérer les équipements\n              </Link>\n            </li>\n            <li>\n              <Link to=\"/employes\" className={location.pathname === '/employes' ? 'active' : ''}>\n                <FontAwesomeIcon icon={faUsers} style={{marginRight:8}} /> Gérer les employés\n              </Link>\n            </li>\n            <li>\n              <Link to=\"/affectations\" className={location.pathname === '/affectations' ? 'active' : ''}>\n                <FontAwesomeIcon icon={faExchangeAlt} style={{marginRight:8}} /> Suivi des affectations\n              </Link>\n            </li>\n            <li>\n              <Link to=\"/fournisseurs\" className={location.pathname === '/fournisseurs' ? 'active' : ''}>\n                <FontAwesomeIcon icon={faTruck} style={{marginRight:8}} /> Gérer les fournisseurs\n              </Link>\n            </li>\n          </ul>\n        </nav>\n        <div className=\"secondary-links\">\n          <Link to=\"/profil\">\n            <FontAwesomeIcon icon={faUserCog} style={{marginRight:8}} /> Mon profil & paramètres\n          </Link>\n        </div>\n        <div style={{ marginTop: 'auto', padding: '20px 0 0 0', textAlign: 'center' }}>\n          <button\n            className=\"logout-btn\"\n            onClick={() => {\n              localStorage.removeItem('authToken');\n              window.location.href = '/';\n            }}\n          >\n            <FontAwesomeIcon icon={faSignOutAlt} style={{marginRight:8}} /> Déconnexion\n          </button>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"equipements-content\">\n        {/* Dashboard header row */}\n        <div className=\"dashboard-header-row\" style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: 32 }}>\n          <div className=\"search-bar\" style={{ flex: 1, maxWidth: 480, marginRight: 24, position: 'relative' }}>\n            <FontAwesomeIcon icon={faSearch} style={{ position: 'absolute', left: 14, top: '50%', transform: 'translateY(-50%)', color: '#1976d2', fontSize: '1.1rem', opacity: 0.8 }} />\n            <input\n              type=\"text\"\n              placeholder=\"Recherche par mot clé\"\n              value={searchTerm}\n              onChange={handleSearchChange}\n              style={{ width: '100%', padding: '10px 16px 10px 38px', borderRadius: 8, border: '1px solid #dbeafe', fontSize: '1rem', boxShadow: '0 2px 8px rgba(21,101,192,0.06)' }}\n            />\n          </div>\n          <div className=\"profile-block\" style={{ display: 'flex', alignItems: 'center', gap: 20 }}>\n            <div className=\"notification-icon\" style={{ position: 'relative', marginRight: 8, cursor: 'pointer' }}>\n              <FontAwesomeIcon icon={faBell} style={{ fontSize: '1.3rem', color: '#1976d2' }} />\n              <span style={{ position: 'absolute', top: -6, right: -6, background: '#e74a3b', color: '#fff', borderRadius: '50%', fontSize: '0.7rem', padding: '2px 6px', fontWeight: 600 }}>3</span>\n            </div>\n            <span style={{ fontWeight: 500, color: '#2e3a4e', fontSize: '1rem', marginRight: 8 }}>Responsable IT</span>\n            <FontAwesomeIcon icon={faUserCog} style={{ fontSize: '1.5rem', color: '#1976d2', background: '#fff', borderRadius: '50%', padding: 6, boxShadow: '0 2px 8px rgba(21,101,192,0.10)' }} />\n          </div>\n        </div>\n\n        <h2 className=\"equipements-title\">\n          Liste des équipements ({filteredEquipements.length})\n        </h2>\n        \n        <div style={{ display: 'flex', gap: 16, marginBottom: 24 }}>\n          <button\n            className=\"btn btn-primary\"\n            style={{ fontWeight: 700, fontSize: '1rem', borderRadius: 10, background: 'linear-gradient(90deg, #1976d2 0%, #42a5f5 100%)', color: '#fff', boxShadow: '0 4px 16px rgba(21,101,192,0.18)' }}\n            onClick={() => {\n              setEditingId(null);\n              setShowModal(true);\n            }}\n          >\n            <FontAwesomeIcon icon={faPlus} style={{marginRight: 8}} />\n            Ajouter un équipement\n          </button>\n          <button className=\"btn btn-primary\" style={{ fontWeight: 700, fontSize: '1rem', borderRadius: 10, background: 'linear-gradient(90deg, #43a047 0%, #66bb6a 100%)', color: '#fff', boxShadow: '0 4px 16px rgba(67,160,71,0.18)' }} onClick={handleExportExcel}>\n            Exporter Excel\n          </button>\n          <button className=\"btn btn-primary\" style={{ fontWeight: 700, fontSize: '1rem', borderRadius: 10, background: 'linear-gradient(90deg, #d32f2f 0%, #e57373 100%)', color: '#fff', boxShadow: '0 4px 16px rgba(211,47,47,0.18)' }} onClick={handleExportPDF}>\n            Exporter PDF\n          </button>\n          <button className=\"btn btn-primary\" style={{ fontWeight: 700, fontSize: '1rem', borderRadius: 10, background: 'linear-gradient(90deg, #ffa000 0%, #ffd54f 100%)', color: '#fff', boxShadow: '0 4px 16px rgba(255,160,0,0.18)' }} onClick={handlePrint}>\n            Imprimer\n          </button>\n          <button\n            className=\"btn btn-primary\"\n            style={{ fontWeight: 700, fontSize: '1rem', borderRadius: 10, background: 'linear-gradient(90deg, #9c27b0 0%, #ba68c8 100%)', color: '#fff', boxShadow: '0 4px 16px rgba(156,39,176,0.18)' }}\n            onClick={() => setShowStats(!showStats)}\n          >\n            {showStats ? 'Masquer' : 'Afficher'} les statistiques\n          </button>\n        </div>\n\n        {/* Section des statistiques */}\n        {showStats && (\n          <div style={{\n            background: '#fff',\n            borderRadius: 12,\n            padding: 24,\n            marginBottom: 24,\n            boxShadow: '0 4px 16px rgba(0,0,0,0.1)',\n            border: '1px solid #e0e0e0'\n          }}>\n            <h3 style={{\n              color: '#1976d2',\n              marginBottom: 24,\n              fontSize: '1.3rem',\n              fontWeight: 700,\n              textAlign: 'center'\n            }}>\n              📊 Statistiques des équipements\n            </h3>\n\n            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 32, alignItems: 'center' }}>\n              {/* Graphique en barres pour les statuts */}\n              <div style={{\n                background: '#fafafa',\n                padding: 20,\n                borderRadius: 8,\n                border: '1px solid #e0e0e0',\n                height: '350px'\n              }}>\n                <Bar data={statutChartData} options={chartOptions} />\n              </div>\n\n              {/* Graphique en donut pour les types */}\n              <div style={{\n                background: '#fafafa',\n                padding: 20,\n                borderRadius: 8,\n                border: '1px solid #e0e0e0',\n                height: '350px'\n              }}>\n                <Doughnut data={typeChartData} options={doughnutOptions} />\n              </div>\n            </div>\n\n            {/* Résumé textuel */}\n            <div style={{\n              marginTop: 24,\n              padding: 16,\n              background: '#f8f9fa',\n              borderRadius: 8,\n              border: '1px solid #dee2e6'\n            }}>\n              <h4 style={{ color: '#495057', marginBottom: 12, fontSize: '1.1rem' }}>📈 Résumé</h4>\n              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: 16 }}>\n                <div style={{ textAlign: 'center', padding: 12, background: '#fff', borderRadius: 6, border: '1px solid #e9ecef' }}>\n                  <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#1976d2' }}>\n                    {filteredEquipements.length}\n                  </div>\n                  <div style={{ color: '#6c757d', fontSize: '0.9rem' }}>Total équipements</div>\n                </div>\n                <div style={{ textAlign: 'center', padding: 12, background: '#fff', borderRadius: 6, border: '1px solid #e9ecef' }}>\n                  <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#28a745' }}>\n                    {statutStats['DISPONIBLE'] || 0}\n                  </div>\n                  <div style={{ color: '#6c757d', fontSize: '0.9rem' }}>Disponibles</div>\n                </div>\n                <div style={{ textAlign: 'center', padding: 12, background: '#fff', borderRadius: 6, border: '1px solid #e9ecef' }}>\n                  <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#ffc107' }}>\n                    {statutStats['EN_MAINTENANCE'] || 0}\n                  </div>\n                  <div style={{ color: '#6c757d', fontSize: '0.9rem' }}>En maintenance</div>\n                </div>\n                <div style={{ textAlign: 'center', padding: 12, background: '#fff', borderRadius: 6, border: '1px solid #e9ecef' }}>\n                  <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#dc3545' }}>\n                    {statutStats['HORS_SERVICE'] || 0}\n                  </div>\n                  <div style={{ color: '#6c757d', fontSize: '0.9rem' }}>Hors service</div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Modal d'ajout/modification d'équipement */}\n        {showModal && (\n          <div style={{\n            position: 'fixed',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            backgroundColor: 'rgba(0, 0, 0, 0.5)',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            zIndex: 1000\n          }}>\n            <div style={{\n              backgroundColor: 'white',\n              borderRadius: 12,\n              padding: 32,\n              maxWidth: 600,\n              width: '90%',\n              maxHeight: '90vh',\n              overflowY: 'auto',\n              boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)'\n            }}>\n              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>\n                <h3 style={{ margin: 0, color: '#1976d2', fontSize: '1.5rem', fontWeight: 700 }}>\n                  {editingId ? 'Modifier un équipement' : 'Ajouter un nouvel équipement'}\n                </h3>\n                <button\n                  onClick={() => {\n                    setShowModal(false);\n                    setEditingId(null);\n                  }}\n                  style={{\n                    background: 'none',\n                    border: 'none',\n                    fontSize: '1.5rem',\n                    color: '#666',\n                    cursor: 'pointer',\n                    padding: 8,\n                    borderRadius: '50%'\n                  }}\n                >\n                  <FontAwesomeIcon icon={faTimes} />\n                </button>\n              </div>\n\n              <form onSubmit={handleSubmit}>\n                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 16, marginBottom: 16 }}>\n                  <div>\n                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>\n                      Nom de l'équipement *\n                    </label>\n                    <input\n                      type=\"text\"\n                      name=\"nom\"\n                      value={formData.nom}\n                      onChange={handleInputChange}\n                      required\n                      style={{\n                        width: '100%',\n                        padding: '12px',\n                        border: '2px solid #e0e0e0',\n                        borderRadius: 8,\n                        fontSize: '1rem',\n                        transition: 'border-color 0.2s'\n                      }}\n                      onFocus={(e) => e.target.style.borderColor = '#1976d2'}\n                      onBlur={(e) => e.target.style.borderColor = '#e0e0e0'}\n                    />\n                  </div>\n\n                  <div>\n                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>\n                      Type *\n                    </label>\n                    <select\n                      name=\"type\"\n                      value={formData.type}\n                      onChange={handleInputChange}\n                      required\n                      style={{\n                        width: '100%',\n                        padding: '12px',\n                        border: '2px solid #e0e0e0',\n                        borderRadius: 8,\n                        fontSize: '1rem'\n                      }}\n                    >\n                      <option value=\"\">Sélectionner un type</option>\n                      {typeOptions.map(type => (\n                        <option key={type} value={type}>{type}</option>\n                      ))}\n                    </select>\n                  </div>\n                </div>\n\n                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 16, marginBottom: 16 }}>\n                  <div>\n                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>\n                      Numéro de série\n                    </label>\n                    <input\n                      type=\"text\"\n                      name=\"numero_serie\"\n                      value={formData.numero_serie}\n                      onChange={handleInputChange}\n                      style={{\n                        width: '100%',\n                        padding: '12px',\n                        border: '2px solid #e0e0e0',\n                        borderRadius: 8,\n                        fontSize: '1rem'\n                      }}\n                    />\n                  </div>\n\n                  <div>\n                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>\n                      Statut *\n                    </label>\n                    <select\n                      name=\"statut\"\n                      value={formData.statut}\n                      onChange={handleInputChange}\n                      required\n                      style={{\n                        width: '100%',\n                        padding: '12px',\n                        border: '2px solid #e0e0e0',\n                        borderRadius: 8,\n                        fontSize: '1rem'\n                      }}\n                    >\n                      {statutOptions.map(statut => (\n                        <option key={statut} value={statut}>{statut}</option>\n                      ))}\n                    </select>\n                  </div>\n                </div>\n\n                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 16, marginBottom: 16 }}>\n                  <div>\n                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>\n                      Date d'achat\n                    </label>\n                    <input\n                      type=\"date\"\n                      name=\"date_achat\"\n                      value={formData.date_achat}\n                      onChange={handleInputChange}\n                      style={{\n                        width: '100%',\n                        padding: '12px',\n                        border: '2px solid #e0e0e0',\n                        borderRadius: 8,\n                        fontSize: '1rem'\n                      }}\n                    />\n                  </div>\n\n                  <div>\n                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>\n                      Fournisseur\n                    </label>\n                    <select\n                      name=\"fournisseur_id\"\n                      value={formData.fournisseur_id}\n                      onChange={handleInputChange}\n                      style={{\n                        width: '100%',\n                        padding: '12px',\n                        border: '2px solid #e0e0e0',\n                        borderRadius: 8,\n                        fontSize: '1rem'\n                      }}\n                    >\n                      <option value=\"\">Sélectionner un fournisseur</option>\n                      {fournisseurs.map(fournisseur => (\n                        <option key={fournisseur.id} value={fournisseur.id}>\n                          {fournisseur.nom}\n                        </option>\n                      ))}\n                    </select>\n                  </div>\n                </div>\n\n                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 16, marginBottom: 16 }}>\n                  <div>\n                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>\n                      Début de garantie\n                    </label>\n                    <input\n                      type=\"date\"\n                      name=\"date_debut_garantie\"\n                      value={formData.date_debut_garantie}\n                      onChange={handleInputChange}\n                      style={{\n                        width: '100%',\n                        padding: '12px',\n                        border: '2px solid #e0e0e0',\n                        borderRadius: 8,\n                        fontSize: '1rem'\n                      }}\n                    />\n                  </div>\n\n                  <div>\n                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>\n                      Fin de garantie\n                    </label>\n                    <input\n                      type=\"date\"\n                      name=\"date_fin_garantie\"\n                      value={formData.date_fin_garantie}\n                      onChange={handleInputChange}\n                      style={{\n                        width: '100%',\n                        padding: '12px',\n                        border: '2px solid #e0e0e0',\n                        borderRadius: 8,\n                        fontSize: '1rem'\n                      }}\n                    />\n                  </div>\n                </div>\n\n                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: 16, marginBottom: 16 }}>\n                  <div>\n                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>\n                      Stock actuel\n                    </label>\n                    <input\n                      type=\"number\"\n                      name=\"stock_actuel\"\n                      value={formData.stock_actuel}\n                      onChange={handleInputChange}\n                      min=\"0\"\n                      style={{\n                        width: '100%',\n                        padding: '12px',\n                        border: '2px solid #e0e0e0',\n                        borderRadius: 8,\n                        fontSize: '1rem'\n                      }}\n                    />\n                  </div>\n\n                  <div>\n                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>\n                      Stock maximum\n                    </label>\n                    <input\n                      type=\"number\"\n                      name=\"stock_max\"\n                      value={formData.stock_max}\n                      onChange={handleInputChange}\n                      min=\"1\"\n                      style={{\n                        width: '100%',\n                        padding: '12px',\n                        border: '2px solid #e0e0e0',\n                        borderRadius: 8,\n                        fontSize: '1rem'\n                      }}\n                    />\n                  </div>\n\n                  <div>\n                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>\n                      Stock minimum\n                    </label>\n                    <input\n                      type=\"number\"\n                      name=\"stock_min\"\n                      value={formData.stock_min}\n                      onChange={handleInputChange}\n                      min=\"0\"\n                      style={{\n                        width: '100%',\n                        padding: '12px',\n                        border: '2px solid #e0e0e0',\n                        borderRadius: 8,\n                        fontSize: '1rem'\n                      }}\n                    />\n                  </div>\n                </div>\n\n                <div style={{ marginBottom: 24 }}>\n                  <label style={{ display: 'flex', alignItems: 'center', fontWeight: 600, color: '#333', cursor: 'pointer' }}>\n                    <input\n                      type=\"checkbox\"\n                      name=\"en_stock\"\n                      checked={formData.en_stock}\n                      onChange={handleInputChange}\n                      style={{ marginRight: 8, transform: 'scale(1.2)' }}\n                    />\n                    Équipement en stock\n                  </label>\n                </div>\n\n                <div style={{ display: 'flex', gap: 12, justifyContent: 'flex-end' }}>\n                  <button\n                    type=\"button\"\n                    onClick={() => {\n                      setShowModal(false);\n                      setEditingId(null);\n                    }}\n                    style={{\n                      padding: '12px 24px',\n                      border: '2px solid #e0e0e0',\n                      borderRadius: 8,\n                      background: 'white',\n                      color: '#666',\n                      fontSize: '1rem',\n                      fontWeight: 600,\n                      cursor: 'pointer'\n                    }}\n                  >\n                    Annuler\n                  </button>\n                  <button\n                    type=\"submit\"\n                    disabled={submitting}\n                    style={{\n                      padding: '12px 24px',\n                      border: 'none',\n                      borderRadius: 8,\n                      background: submitting ? '#ccc' : 'linear-gradient(90deg, #1976d2 0%, #42a5f5 100%)',\n                      color: 'white',\n                      fontSize: '1rem',\n                      fontWeight: 600,\n                      cursor: submitting ? 'not-allowed' : 'pointer',\n                      boxShadow: '0 4px 16px rgba(21,101,192,0.18)'\n                    }}\n                  >\n                    {submitting \n                      ? (editingId ? 'Modification en cours...' : 'Ajout en cours...') \n                      : (editingId ? 'Modifier l\\'équipement' : 'Ajouter l\\'équipement')}\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        )}\n\n        {/* Contenu principal */}\n        {loading ? (\n          <div style={{ textAlign: 'center', padding: '40px' }}>\n            <p>Chargement des équipements...</p>\n          </div>\n        ) : error ? (\n          <div style={{ textAlign: 'center', padding: '40px', color: '#e74a3b' }}>\n            <p>Erreur lors du chargement : {error}</p>\n            <button \n              onClick={() => window.location.reload()} \n              style={{ marginTop: '10px', padding: '8px 16px', background: '#1976d2', color: 'white', border: 'none', borderRadius: '4px', cursor: 'pointer' }}\n            >\n              Actualiser la page\n            </button>\n          </div>\n        ) : !Array.isArray(filteredEquipements) || filteredEquipements.length === 0 ? (\n          <div style={{ textAlign: 'center', padding: '40px' }}>\n            <p>{searchTerm ? 'Aucun résultat trouvé' : 'Aucun équipement trouvé'}</p>\n          </div>\n        ) : (\n          <table className=\"equipements-table\">\n            <thead>\n              <tr>\n                <th>Nom</th>\n                <th>Type</th>\n                <th>Numéro de série</th>\n                <th>Statut</th>\n                <th>Date d'achat</th>\n                <th>Date fin garantie</th>\n                <th>Fournisseur</th>\n                <th>Actions</th>\n              </tr>\n            </thead>\n            <tbody>\n              {filteredEquipements.map((eq, index) => {\n                const numeroSerie = eq.numero_serie || eq.numeroSerie || 'N/A';\n                const dateAchat = formatDate(eq.dateAchat || eq.date_achat);\n                const dateFinGarantie = formatDate(eq.dateFinGarantie || eq.date_fin_garantie);\n                const fournisseur = eq.fournisseur?.nom || eq.fournisseur_id || 'Non spécifié';\n\n                return (\n                  <tr key={eq.id || index}>\n                    <td>{eq.nom || 'N/A'}</td>\n                    <td>{eq.type || 'N/A'}</td>\n                    <td>{numeroSerie}</td>\n                    <td>{eq.statut || 'N/A'}</td>\n                    <td>{dateAchat}</td>\n                    <td>{dateFinGarantie}</td>\n                    <td>{fournisseur}</td>\n                    <td>\n                      <div style={{ display: 'flex', gap: '8px' }}>\n                        <button \n                          onClick={() => handleEdit(eq)}\n                          style={{\n                            background: '#1976d2',\n                            color: 'white',\n                            border: 'none',\n                            borderRadius: '4px',\n                            padding: '6px 12px',\n                            cursor: 'pointer',\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '4px'\n                          }}\n                        >\n                          <FontAwesomeIcon icon={faEdit} />\n                          <span>Modifier</span>\n                        </button>\n                        <button \n                          onClick={() => handleDelete(eq.id)}\n                          style={{\n                            background: '#e74a3b',\n                            color: 'white',\n                            border: 'none',\n                            borderRadius: '4px',\n                            padding: '6px 12px',\n                            cursor: 'pointer',\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '4px'\n                          }}\n                        >\n                          <FontAwesomeIcon icon={faTrash} />\n                          <span>Supprimer</span>\n                        </button>\n                      </div>\n                    </td>\n                  </tr>\n                );\n              })}\n            </tbody>\n          </table>\n        )}\n      </div>\n    </div>\n  );\n}"], "mappings": ";;AAAA,OAAO,mBAAmB;AAC1B,SAASA,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,OAAOC,IAAI,MAAM,kCAAkC;AACnD,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,QAAQ,EAAEC,OAAO,EAAEC,aAAa,EAAEC,OAAO,EAAEC,SAAS,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAEC,MAAM,EAAEC,MAAM,EAAEC,OAAO,QAAQ,mCAAmC;AAC1K,SAASC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAO,KAAKC,IAAI,MAAM,MAAM;AAC5B,SAASC,KAAK,IAAIC,OAAO,EAAEC,aAAa,EAAEC,WAAW,EAAEC,UAAU,EAAEC,KAAK,EAAEC,OAAO,EAAEC,MAAM,EAAEC,UAAU,QAAQ,UAAU;AACvH,SAASC,GAAG,EAAEC,QAAQ,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhDX,OAAO,CAACY,QAAQ,CAACX,aAAa,EAAEC,WAAW,EAAEC,UAAU,EAAEC,KAAK,EAAEC,OAAO,EAAEC,MAAM,EAAEC,UAAU,CAAC;AAE5F,eAAe,SAASM,WAAWA,CAAA,EAAG;EAAAC,EAAA;EACpC,MAAMC,QAAQ,GAAGpC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACqC,WAAW,EAAEC,cAAc,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACuB,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC2B,KAAK,EAAEC,QAAQ,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC6B,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC+B,YAAY,EAAEC,eAAe,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACiC,UAAU,EAAEC,aAAa,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACmC,UAAU,EAAEC,aAAa,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACqC,UAAU,EAAEC,aAAa,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACuC,SAAS,EAAEC,YAAY,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACyC,SAAS,EAAEC,YAAY,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;;EAEjD;EACA,MAAM,CAAC2C,QAAQ,EAAEC,WAAW,CAAC,GAAG5C,QAAQ,CAAC;IACvC6C,GAAG,EAAE,EAAE;IACPC,IAAI,EAAE,EAAE;IACRC,YAAY,EAAE,EAAE;IAChBC,MAAM,EAAE,YAAY;IACpBC,UAAU,EAAE,EAAE;IACdC,cAAc,EAAE,EAAE;IAClBC,mBAAmB,EAAE,EAAE;IACvBC,iBAAiB,EAAE,EAAE;IACrBC,QAAQ,EAAE,IAAI;IACdC,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE,CAAC;IACZC,SAAS,EAAE,CAAC;IACZC,YAAY,EAAE;EAChB,CAAC,CAAC;;EAEF;EACA,MAAMC,aAAa,GAAG,CAAC,YAAY,EAAE,gBAAgB,EAAE,QAAQ,EAAE,cAAc,CAAC;EAChF,MAAMC,WAAW,GAAG,CAAC,YAAY,EAAE,YAAY,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,SAAS,EAAE,OAAO,CAAC;EAExG5D,SAAS,CAAC,MAAM;IACd6D,eAAe,CAAC,CAAC;IACjBC,gBAAgB,CAAC,CAAC;IAClBC,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN/D,SAAS,CAAC,MAAM;IACd,IAAIsC,UAAU,KAAK,EAAE,EAAE;MACrBb,sBAAsB,CAACH,WAAW,CAAC;IACrC,CAAC,MAAM;MACL,MAAM0C,QAAQ,GAAG1C,WAAW,CAAC2C,MAAM,CAACC,UAAU;QAAA,IAAAC,qBAAA;QAAA,OAC5CD,UAAU,CAACpB,GAAG,CAACsB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC/B,UAAU,CAAC8B,WAAW,CAAC,CAAC,CAAC,IAC/DF,UAAU,CAACnB,IAAI,CAACqB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC/B,UAAU,CAAC8B,WAAW,CAAC,CAAC,CAAC,IAC/DF,UAAU,CAAClB,YAAY,IAAIkB,UAAU,CAAClB,YAAY,CAACoB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC/B,UAAU,CAAC8B,WAAW,CAAC,CAAC,CAAE,IACrGF,UAAU,CAACjB,MAAM,CAACmB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC/B,UAAU,CAAC8B,WAAW,CAAC,CAAC,CAAC,IACjE,EAAAD,qBAAA,GAAAD,UAAU,CAACI,WAAW,cAAAH,qBAAA,uBAAtBA,qBAAA,CAAwBrB,GAAG,KAAIoB,UAAU,CAACI,WAAW,CAACxB,GAAG,CAACsB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC/B,UAAU,CAAC8B,WAAW,CAAC,CAAC,CAAE;MAAA,CAC9G,CAAC;MACD3C,sBAAsB,CAACuC,QAAQ,CAAC;IAClC;EACF,CAAC,EAAE,CAAC1B,UAAU,EAAEhB,WAAW,CAAC,CAAC;EAE7B,MAAMuC,eAAe,GAAGA,CAAA,KAAM;IAC5BU,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;IAE5C,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IAC/C,MAAMC,OAAO,GAAG;MACd,cAAc,EAAE;IAClB,CAAC;IACD,IAAIH,KAAK,EAAE;MACTG,OAAO,CAAC,eAAe,CAAC,GAAG,UAAUH,KAAK,EAAE;IAC9C;IAEAI,KAAK,CAAC,uCAAuC,EAAE;MAC7CC,MAAM,EAAE,KAAK;MACbF,OAAO,EAAEA;IACX,CAAC,CAAC,CACCG,IAAI,CAACC,GAAG,IAAI;MACXT,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEQ,GAAG,CAACC,MAAM,CAAC;MAC5D,IAAI,CAACD,GAAG,CAACE,EAAE,EAAE;QACX,MAAM,IAAIC,KAAK,CAAC,gBAAgBH,GAAG,CAACC,MAAM,EAAE,CAAC;MAC/C;MACA,OAAOD,GAAG,CAACI,IAAI,CAAC,CAAC;IACnB,CAAC,CAAC,CACDL,IAAI,CAACM,IAAI,IAAI;MACZd,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEa,IAAI,CAAC;MAChD,IAAIC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,EAAE;QACvB9D,cAAc,CAAC8D,IAAI,CAAC;QACpB5D,sBAAsB,CAAC4D,IAAI,CAAC;QAC5BxD,QAAQ,CAAC,IAAI,CAAC;MAChB,CAAC,MAAM;QACL0C,OAAO,CAACiB,IAAI,CAAC,iDAAiD,EAAEH,IAAI,CAAC;QACrE9D,cAAc,CAAC,EAAE,CAAC;QAClBE,sBAAsB,CAAC,EAAE,CAAC;QAC1BI,QAAQ,CAAC,6BAA6B,CAAC;MACzC;MACAF,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,CACD8D,KAAK,CAACC,GAAG,IAAI;MACZnB,OAAO,CAAC3C,KAAK,CAAC,4CAA4C,EAAE8D,GAAG,CAAC;MAChEnE,cAAc,CAAC,EAAE,CAAC;MAClBE,sBAAsB,CAAC,EAAE,CAAC;MAC1BI,QAAQ,CAAC6D,GAAG,CAACC,OAAO,IAAI,sBAAsB,CAAC;MAC/ChE,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC;EACN,CAAC;EAED,MAAMmC,gBAAgB,GAAGA,CAAA,KAAM;IAC7BS,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;IAE7C,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IAC/C,MAAMC,OAAO,GAAG;MACd,cAAc,EAAE;IAClB,CAAC;IACD,IAAIH,KAAK,EAAE;MACTG,OAAO,CAAC,eAAe,CAAC,GAAG,UAAUH,KAAK,EAAE;IAC9C;IAEAI,KAAK,CAAC,wCAAwC,EAAE;MAC9CC,MAAM,EAAE,KAAK;MACbF,OAAO,EAAEA;IACX,CAAC,CAAC,CACCG,IAAI,CAACC,GAAG,IAAI;MACXT,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEQ,GAAG,CAACC,MAAM,CAAC;MAC7D,IAAI,CAACD,GAAG,CAACE,EAAE,EAAE;QACX,MAAM,IAAIC,KAAK,CAAC,gBAAgBH,GAAG,CAACC,MAAM,EAAE,CAAC;MAC/C;MACA,OAAOD,GAAG,CAACI,IAAI,CAAC,CAAC;IACnB,CAAC,CAAC,CACDL,IAAI,CAACM,IAAI,IAAI;MACZd,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEa,IAAI,CAAC;MACjD,IAAIC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,EAAE;QACvBpD,eAAe,CAACoD,IAAI,CAAC;QACrBd,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEa,IAAI,CAACO,MAAM,EAAE,UAAU,CAAC;MAC/D,CAAC,MAAM;QACLrB,OAAO,CAACiB,IAAI,CAAC,kDAAkD,EAAEH,IAAI,CAAC;QACtEpD,eAAe,CAAC,EAAE,CAAC;MACrB;IACF,CAAC,CAAC,CACDwD,KAAK,CAACC,GAAG,IAAI;MACZnB,OAAO,CAAC3C,KAAK,CAAC,6CAA6C,EAAE8D,GAAG,CAAC;MACjEzD,eAAe,CAAC,EAAE,CAAC;IACrB,CAAC,CAAC;EACN,CAAC;EAED,MAAM8B,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMU,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IAC/C,MAAMC,OAAO,GAAG;MACd,cAAc,EAAE;IAClB,CAAC;IACD,IAAIH,KAAK,EAAE;MACTG,OAAO,CAAC,eAAe,CAAC,GAAG,UAAUH,KAAK,EAAE;IAC9C;IAEAI,KAAK,CAAC,sCAAsC,EAAE;MAC5CC,MAAM,EAAE,KAAK;MACbF,OAAO,EAAEA;IACX,CAAC,CAAC,CACCG,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACE,EAAE,GAAGF,GAAG,CAACI,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,CACrCL,IAAI,CAACM,IAAI,IAAIlD,aAAa,CAACmD,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,GAAGA,IAAI,GAAG,EAAE,CAAC,CAAC,CAC5DI,KAAK,CAACC,GAAG,IAAI;MACZnB,OAAO,CAAC3C,KAAK,CAAC,2CAA2C,EAAE8D,GAAG,CAAC;MAC/DvD,aAAa,CAAC,EAAE,CAAC;IACnB,CAAC,CAAC;EACN,CAAC;EAED,MAAM0D,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC,KAAK;MAAEjD,IAAI;MAAEkD;IAAQ,CAAC,GAAGH,CAAC,CAACI,MAAM;IAC/CrD,WAAW,CAACsD,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACJ,IAAI,GAAGhD,IAAI,KAAK,UAAU,GAAGkD,OAAO,GAAGD;IAC1C,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMI,YAAY,GAAG,MAAON,CAAC,IAAK;IAChCA,CAAC,CAACO,cAAc,CAAC,CAAC;IAClBhE,aAAa,CAAC,IAAI,CAAC;IAEnBkC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEhC,SAAS,CAAC;IACnD+B,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE5B,QAAQ,CAAC;IAEjD,MAAM0D,UAAU,GAAG;MACjB,GAAG1D,QAAQ;MACXO,cAAc,EAAEP,QAAQ,CAACO,cAAc,IAAI;IAC7C,CAAC;IAED,IAAI;MACF,MAAMsB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;MAC/C,MAAMC,OAAO,GAAG;QACd,cAAc,EAAE;MAClB,CAAC;MACD,IAAIH,KAAK,EAAE;QACTG,OAAO,CAAC,eAAe,CAAC,GAAG,UAAUH,KAAK,EAAE;MAC9C;MAEA,MAAM8B,GAAG,GAAG/D,SAAS,GACjB,yCAAyCA,SAAS,EAAE,GACpD,uCAAuC;MAE3C+B,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE+B,GAAG,CAAC;MAChC,MAAMzB,MAAM,GAAGtC,SAAS,GAAG,KAAK,GAAG,MAAM;MAEzC,MAAMgE,QAAQ,GAAG,MAAM3B,KAAK,CAAC0B,GAAG,EAAE;QAChCzB,MAAM,EAAEA,MAAM;QACdF,OAAO,EAAEA,OAAO;QAChB6B,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACL,UAAU;MACjC,CAAC,CAAC;MAEF,IAAI,CAACE,QAAQ,CAACtB,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,gBAAgBqB,QAAQ,CAACvB,MAAM,EAAE,CAAC;MACpD;MAEA,MAAM2B,MAAM,GAAG,MAAMJ,QAAQ,CAACpB,IAAI,CAAC,CAAC;MAEpC,IAAI5C,SAAS,EAAE;QACbjB,cAAc,CAAC4E,IAAI,IAAIA,IAAI,CAACU,GAAG,CAACC,EAAE,IAAIA,EAAE,CAACC,EAAE,KAAKvE,SAAS,GAAGoE,MAAM,GAAGE,EAAE,CAAC,CAAC;QACzErF,sBAAsB,CAAC0E,IAAI,IAAIA,IAAI,CAACU,GAAG,CAACC,EAAE,IAAIA,EAAE,CAACC,EAAE,KAAKvE,SAAS,GAAGoE,MAAM,GAAGE,EAAE,CAAC,CAAC;MACnF,CAAC,MAAM;QACLvF,cAAc,CAAC4E,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAES,MAAM,CAAC,CAAC;QACzCnF,sBAAsB,CAAC0E,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAES,MAAM,CAAC,CAAC;MACnD;MAEA/D,WAAW,CAAC;QACVC,GAAG,EAAE,EAAE;QACPC,IAAI,EAAE,EAAE;QACRC,YAAY,EAAE,EAAE;QAChBC,MAAM,EAAE,YAAY;QACpBC,UAAU,EAAE,EAAE;QACdC,cAAc,EAAE,EAAE;QAClBC,mBAAmB,EAAE,EAAE;QACvBC,iBAAiB,EAAE,EAAE;QACrBC,QAAQ,EAAE,IAAI;QACdC,YAAY,EAAE,CAAC;QACfC,SAAS,EAAE,CAAC;QACZC,SAAS,EAAE,CAAC;QACZC,YAAY,EAAE;MAChB,CAAC,CAAC;MACF3B,YAAY,CAAC,KAAK,CAAC;MACnBU,YAAY,CAAC,IAAI,CAAC;MAElBuE,KAAK,CAACxE,SAAS,GAAG,kCAAkC,GAAG,iCAAiC,CAAC;IAC3F,CAAC,CAAC,OAAOkD,GAAG,EAAE;MACZnB,OAAO,CAAC3C,KAAK,CAAC,6BAA6B,EAAE8D,GAAG,CAAC;MACjDsB,KAAK,CAAC,WAAWtB,GAAG,CAACC,OAAO,EAAE,CAAC;IACjC,CAAC,SAAS;MACRtD,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAM4E,UAAU,GAAI/C,UAAU,IAAK;IAAA,IAAAgD,sBAAA;IACjCzE,YAAY,CAACyB,UAAU,CAAC6C,EAAE,CAAC;IAC3BlE,WAAW,CAAC;MACVC,GAAG,EAAEoB,UAAU,CAACpB,GAAG;MACnBC,IAAI,EAAEmB,UAAU,CAACnB,IAAI;MACrBC,YAAY,EAAEkB,UAAU,CAAClB,YAAY,IAAIkB,UAAU,CAACiD,WAAW,IAAI,EAAE;MACrElE,MAAM,EAAEiB,UAAU,CAACjB,MAAM;MACzBC,UAAU,EAAEgB,UAAU,CAAChB,UAAU,IAAI,EAAE;MACvCC,cAAc,EAAE,EAAA+D,sBAAA,GAAAhD,UAAU,CAACI,WAAW,cAAA4C,sBAAA,uBAAtBA,sBAAA,CAAwBH,EAAE,KAAI7C,UAAU,CAACf,cAAc,IAAI,EAAE;MAC7EC,mBAAmB,EAAEc,UAAU,CAACd,mBAAmB,IAAI,EAAE;MACzDC,iBAAiB,EAAEa,UAAU,CAACb,iBAAiB,IAAI,EAAE;MACrDC,QAAQ,EAAEY,UAAU,CAACZ,QAAQ,KAAK8D,SAAS,GAAGlD,UAAU,CAACZ,QAAQ,GAAG,IAAI;MACxEC,YAAY,EAAEW,UAAU,CAACX,YAAY,IAAI,CAAC;MAC1CC,SAAS,EAAEU,UAAU,CAACV,SAAS,IAAI,CAAC;MACpCC,SAAS,EAAES,UAAU,CAACT,SAAS,IAAI,CAAC;MACpCC,YAAY,EAAEQ,UAAU,CAACR,YAAY,IAAI;IAC3C,CAAC,CAAC;IACF3B,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMsF,YAAY,GAAG,MAAON,EAAE,IAAK;IACjC,IAAI,CAACO,MAAM,CAACC,OAAO,CAAC,qDAAqD,CAAC,EAAE;MAC1E;IACF;IAEA,IAAI;MACF,MAAM9C,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;MAC/C,MAAMC,OAAO,GAAG;QACd,cAAc,EAAE;MAClB,CAAC;MACD,IAAIH,KAAK,EAAE;QACTG,OAAO,CAAC,eAAe,CAAC,GAAG,UAAUH,KAAK,EAAE;MAC9C;MAEA,MAAM+B,QAAQ,GAAG,MAAM3B,KAAK,CAAC,yCAAyCkC,EAAE,EAAE,EAAE;QAC1EjC,MAAM,EAAE,QAAQ;QAChBF,OAAO,EAAEA;MACX,CAAC,CAAC;MAEF,IAAI,CAAC4B,QAAQ,CAACtB,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,gBAAgBqB,QAAQ,CAACvB,MAAM,EAAE,CAAC;MACpD;MAEA1D,cAAc,CAAC4E,IAAI,IAAIA,IAAI,CAAClC,MAAM,CAAC6C,EAAE,IAAIA,EAAE,CAACC,EAAE,KAAKA,EAAE,CAAC,CAAC;MACvDtF,sBAAsB,CAAC0E,IAAI,IAAIA,IAAI,CAAClC,MAAM,CAAC6C,EAAE,IAAIA,EAAE,CAACC,EAAE,KAAKA,EAAE,CAAC,CAAC;MAE/DC,KAAK,CAAC,mCAAmC,CAAC;IAC5C,CAAC,CAAC,OAAOtB,GAAG,EAAE;MACZnB,OAAO,CAAC3C,KAAK,CAAC,gCAAgC,EAAE8D,GAAG,CAAC;MACpDsB,KAAK,CAAC,kCAAkCtB,GAAG,CAACC,OAAO,EAAE,CAAC;IACxD;EACF,CAAC;EAED,MAAM6B,kBAAkB,GAAI1B,CAAC,IAAK;IAChCvD,aAAa,CAACuD,CAAC,CAACI,MAAM,CAACF,KAAK,CAAC;EAC/B,CAAC;EAED,MAAMyB,UAAU,GAAIC,SAAS,IAAK;IAChC,IAAI,CAACA,SAAS,IAAIA,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAKN,SAAS,IAAIM,SAAS,KAAK,EAAE,EAAE;MACnF,OAAO,KAAK;IACd;IAEA,IAAI;MACF,MAAMC,OAAO,GAAG,IAAIC,IAAI,CAACF,SAAS,CAAC;MACnC,IAAI,CAACG,KAAK,CAACF,OAAO,CAACG,OAAO,CAAC,CAAC,CAAC,EAAE;QAC7B,OAAOH,OAAO,CAACI,kBAAkB,CAAC,OAAO,CAAC;MAC5C;MACA,OAAO,KAAK;IACd,CAAC,CAAC,OAAOjC,CAAC,EAAE;MACVvB,OAAO,CAAC3C,KAAK,CAAC,6BAA6B,EAAEkE,CAAC,EAAE,iBAAiB,EAAE4B,SAAS,CAAC;MAC7E,OAAO,KAAK;IACd;EACF,CAAC;EAED,MAAMM,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI,CAAC1C,KAAK,CAACC,OAAO,CAAC/D,mBAAmB,CAAC,IAAIA,mBAAmB,CAACoE,MAAM,KAAK,CAAC,EAAE;MAC3EoB,KAAK,CAAC,0BAA0B,CAAC;MACjC;IACF;IAEA,MAAMiB,OAAO,GAAG,CACd,KAAK,EACL,MAAM,EACN,iBAAiB,EACjB,QAAQ,EACR,cAAc,EACd,mBAAmB,EACnB,aAAa,EACb,SAAS,CACV;IACD,MAAMC,IAAI,GAAG1G,mBAAmB,CAACqF,GAAG,CAACC,EAAE;MAAA,IAAAqB,eAAA;MAAA,OAAI,CACzCrB,EAAE,CAAChE,GAAG,IAAI,EAAE,EACZgE,EAAE,CAAC/D,IAAI,IAAI,EAAE,EACb+D,EAAE,CAAC9D,YAAY,IAAI8D,EAAE,CAACK,WAAW,IAAI,EAAE,EACvCL,EAAE,CAAC7D,MAAM,IAAI,EAAE,EACfwE,UAAU,CAACX,EAAE,CAACsB,SAAS,IAAItB,EAAE,CAAC5D,UAAU,CAAC,IAAI,EAAE,EAC/CuE,UAAU,CAACX,EAAE,CAACuB,eAAe,IAAIvB,EAAE,CAACzD,iBAAiB,CAAC,IAAI,EAAE,EAC5D,EAAA8E,eAAA,GAAArB,EAAE,CAACxC,WAAW,cAAA6D,eAAA,uBAAdA,eAAA,CAAgBrF,GAAG,KAAI,EAAE,EACzB,EAAE,CACH;IAAA,EAAC;IACF,MAAMwF,SAAS,GAAGlI,IAAI,CAACmI,KAAK,CAACC,YAAY,CAAC,CAACP,OAAO,EAAE,GAAGC,IAAI,CAAC,CAAC;IAC7D,MAAMO,QAAQ,GAAGrI,IAAI,CAACmI,KAAK,CAACG,QAAQ,CAAC,CAAC;IACtCtI,IAAI,CAACmI,KAAK,CAACI,iBAAiB,CAACF,QAAQ,EAAEH,SAAS,EAAE,aAAa,CAAC;IAChElI,IAAI,CAACwI,SAAS,CAACH,QAAQ,EAAE,kBAAkB,CAAC;EAC9C,CAAC;EAED,MAAMI,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAACvD,KAAK,CAACC,OAAO,CAAC/D,mBAAmB,CAAC,IAAIA,mBAAmB,CAACoE,MAAM,KAAK,CAAC,EAAE;MAC3EoB,KAAK,CAAC,0BAA0B,CAAC;MACjC;IACF;IAEA,MAAM8B,GAAG,GAAG,IAAI5I,KAAK,CAAC,CAAC;IACvB4I,GAAG,CAACC,IAAI,CAAC,uBAAuB,EAAE,EAAE,EAAE,EAAE,CAAC;IACzC,MAAMd,OAAO,GAAG,CACd,KAAK,EACL,MAAM,EACN,iBAAiB,EACjB,QAAQ,EACR,cAAc,EACd,mBAAmB,EACnB,aAAa,CACd;IACD,MAAMC,IAAI,GAAG1G,mBAAmB,CAACqF,GAAG,CAACC,EAAE;MAAA,IAAAkC,gBAAA;MAAA,OAAI,CACzClC,EAAE,CAAChE,GAAG,IAAI,EAAE,EACZgE,EAAE,CAAC/D,IAAI,IAAI,EAAE,EACb+D,EAAE,CAAC9D,YAAY,IAAI8D,EAAE,CAACK,WAAW,IAAI,EAAE,EACvCL,EAAE,CAAC7D,MAAM,IAAI,EAAE,EACfwE,UAAU,CAACX,EAAE,CAACsB,SAAS,IAAItB,EAAE,CAAC5D,UAAU,CAAC,IAAI,EAAE,EAC/CuE,UAAU,CAACX,EAAE,CAACuB,eAAe,IAAIvB,EAAE,CAACzD,iBAAiB,CAAC,IAAI,EAAE,EAC5D,EAAA2F,gBAAA,GAAAlC,EAAE,CAACxC,WAAW,cAAA0E,gBAAA,uBAAdA,gBAAA,CAAgBlG,GAAG,KAAI,EAAE,CAC1B;IAAA,EAAC;IACF3C,SAAS,CAAC2I,GAAG,EAAE;MACbG,IAAI,EAAE,CAAChB,OAAO,CAAC;MACfxB,IAAI,EAAEyB,IAAI;MACVgB,MAAM,EAAE,EAAE;MACVC,MAAM,EAAE;QAAEC,QAAQ,EAAE;MAAG;IACzB,CAAC,CAAC;IACFN,GAAG,CAACO,IAAI,CAAC,iBAAiB,CAAC;EAC7B,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxBhC,MAAM,CAACiC,KAAK,CAAC,CAAC;EAChB,CAAC;;EAED;EACA,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,YAAY,GAAG,CAAC,CAAC;IACvBjI,mBAAmB,CAACkI,OAAO,CAAC5C,EAAE,IAAI;MAChC,MAAM7D,MAAM,GAAG6D,EAAE,CAAC7D,MAAM,IAAI,YAAY;MACxCwG,YAAY,CAACxG,MAAM,CAAC,GAAG,CAACwG,YAAY,CAACxG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;IACxD,CAAC,CAAC;IACF,OAAOwG,YAAY;EACrB,CAAC;EAED,MAAME,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,UAAU,GAAG,CAAC,CAAC;IACrBpI,mBAAmB,CAACkI,OAAO,CAAC5C,EAAE,IAAI;MAChC,MAAM/D,IAAI,GAAG+D,EAAE,CAAC/D,IAAI,IAAI,YAAY;MACpC6G,UAAU,CAAC7G,IAAI,CAAC,GAAG,CAAC6G,UAAU,CAAC7G,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;IAChD,CAAC,CAAC;IACF,OAAO6G,UAAU;EACnB,CAAC;;EAED;EACA,MAAMC,WAAW,GAAGL,cAAc,CAAC,CAAC;EACpC,MAAMM,SAAS,GAAGH,YAAY,CAAC,CAAC;EAEhC,MAAMI,eAAe,GAAG;IACtBC,MAAM,EAAEC,MAAM,CAACC,IAAI,CAACL,WAAW,CAAC;IAChCM,QAAQ,EAAE,CAAC;MACTC,KAAK,EAAE,uBAAuB;MAC9B/E,IAAI,EAAE4E,MAAM,CAACI,MAAM,CAACR,WAAW,CAAC;MAChCS,eAAe,EAAE,CACf,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACV;MACDC,WAAW,EAAE,CACX,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACV;MACDC,WAAW,EAAE;IACf,CAAC;EACH,CAAC;EAED,MAAMC,aAAa,GAAG;IACpBT,MAAM,EAAEC,MAAM,CAACC,IAAI,CAACJ,SAAS,CAAC;IAC9BK,QAAQ,EAAE,CAAC;MACT9E,IAAI,EAAE4E,MAAM,CAACI,MAAM,CAACP,SAAS,CAAC;MAC9BQ,eAAe,EAAE,CACf,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACV;MACDE,WAAW,EAAE,CAAC;MACdD,WAAW,EAAE;IACf,CAAC;EACH,CAAC;EAED,MAAMG,YAAY,GAAG;IACnBC,UAAU,EAAE,IAAI;IAChBC,OAAO,EAAE;MACPC,MAAM,EAAE;QACNC,QAAQ,EAAE;MACZ,CAAC;MACDC,KAAK,EAAE;QACLC,OAAO,EAAE,IAAI;QACbjC,IAAI,EAAE;MACR;IACF,CAAC;IACDkC,MAAM,EAAE;MACNC,CAAC,EAAE;QACDC,WAAW,EAAE,IAAI;QACjBC,KAAK,EAAE;UACLC,QAAQ,EAAE;QACZ;MACF;IACF;EACF,CAAC;EAED,MAAMC,eAAe,GAAG;IACtBX,UAAU,EAAE,IAAI;IAChBC,OAAO,EAAE;MACPC,MAAM,EAAE;QACNC,QAAQ,EAAE;MACZ,CAAC;MACDC,KAAK,EAAE;QACLC,OAAO,EAAE,IAAI;QACbjC,IAAI,EAAE;MACR;IACF;EACF,CAAC;EAED,oBACE9H,OAAA;IAAKsK,SAAS,EAAC,uBAAuB;IAAAC,QAAA,gBAEpCvK,OAAA;MAAKsK,SAAS,EAAC,SAAS;MAAAC,QAAA,gBACtBvK,OAAA;QAAKsK,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3BvK,OAAA;UAAKwK,GAAG,EAAEvM,IAAK;UAACwM,GAAG,EAAC,iBAAiB;UAACH,SAAS,EAAC;QAAc;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC,eACN7K,OAAA;QAAKsK,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBvK,OAAA;UAAAuK,QAAA,gBACEvK,OAAA;YAAAuK,QAAA,eACEvK,OAAA,CAACjC,IAAI;cAAC+M,EAAE,EAAC,GAAG;cAACR,SAAS,EAAElK,QAAQ,CAAC2K,QAAQ,KAAK,GAAG,GAAG,oBAAoB,GAAG,EAAG;cAAAR,QAAA,gBAC5EvK,OAAA,CAAC9B,eAAe;gBAAC8M,IAAI,EAAE7M,QAAS;gBAAC8M,KAAK,EAAE;kBAACC,WAAW,EAAC;gBAAC;cAAE;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,YAC7D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACL7K,OAAA;YAAAuK,QAAA,eACEvK,OAAA,CAACjC,IAAI;cAAC+M,EAAE,EAAC,cAAc;cAACR,SAAS,EAAElK,QAAQ,CAAC2K,QAAQ,KAAK,cAAc,GAAG,QAAQ,GAAG,EAAG;cAAAR,QAAA,gBACtFvK,OAAA,CAAC9B,eAAe;gBAAC8M,IAAI,EAAE7M,QAAS;gBAAC8M,KAAK,EAAE;kBAACC,WAAW,EAAC;gBAAC;cAAE;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gCAC7D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACL7K,OAAA;YAAAuK,QAAA,eACEvK,OAAA,CAACjC,IAAI;cAAC+M,EAAE,EAAC,WAAW;cAACR,SAAS,EAAElK,QAAQ,CAAC2K,QAAQ,KAAK,WAAW,GAAG,QAAQ,GAAG,EAAG;cAAAR,QAAA,gBAChFvK,OAAA,CAAC9B,eAAe;gBAAC8M,IAAI,EAAE5M,OAAQ;gBAAC6M,KAAK,EAAE;kBAACC,WAAW,EAAC;gBAAC;cAAE;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,6BAC5D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACL7K,OAAA;YAAAuK,QAAA,eACEvK,OAAA,CAACjC,IAAI;cAAC+M,EAAE,EAAC,eAAe;cAACR,SAAS,EAAElK,QAAQ,CAAC2K,QAAQ,KAAK,eAAe,GAAG,QAAQ,GAAG,EAAG;cAAAR,QAAA,gBACxFvK,OAAA,CAAC9B,eAAe;gBAAC8M,IAAI,EAAE3M,aAAc;gBAAC4M,KAAK,EAAE;kBAACC,WAAW,EAAC;gBAAC;cAAE;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,2BAClE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACL7K,OAAA;YAAAuK,QAAA,eACEvK,OAAA,CAACjC,IAAI;cAAC+M,EAAE,EAAC,eAAe;cAACR,SAAS,EAAElK,QAAQ,CAAC2K,QAAQ,KAAK,eAAe,GAAG,QAAQ,GAAG,EAAG;cAAAR,QAAA,gBACxFvK,OAAA,CAAC9B,eAAe;gBAAC8M,IAAI,EAAE1M,OAAQ;gBAAC2M,KAAK,EAAE;kBAACC,WAAW,EAAC;gBAAC;cAAE;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,8BAC5D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACN7K,OAAA;QAAKsK,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BvK,OAAA,CAACjC,IAAI;UAAC+M,EAAE,EAAC,SAAS;UAAAP,QAAA,gBAChBvK,OAAA,CAAC9B,eAAe;YAAC8M,IAAI,EAAEzM,SAAU;YAAC0M,KAAK,EAAE;cAACC,WAAW,EAAC;YAAC;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,+BAC9D;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN7K,OAAA;QAAKiL,KAAK,EAAE;UAAEE,SAAS,EAAE,MAAM;UAAEC,OAAO,EAAE,YAAY;UAAEC,SAAS,EAAE;QAAS,CAAE;QAAAd,QAAA,eAC5EvK,OAAA;UACEsK,SAAS,EAAC,YAAY;UACtBgB,OAAO,EAAEA,CAAA,KAAM;YACb7H,YAAY,CAAC8H,UAAU,CAAC,WAAW,CAAC;YACpClF,MAAM,CAACjG,QAAQ,CAACoL,IAAI,GAAG,GAAG;UAC5B,CAAE;UAAAjB,QAAA,gBAEFvK,OAAA,CAAC9B,eAAe;YAAC8M,IAAI,EAAExM,YAAa;YAACyM,KAAK,EAAE;cAACC,WAAW,EAAC;YAAC;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,mBACjE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7K,OAAA;MAAKsK,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAElCvK,OAAA;QAAKsK,SAAS,EAAC,sBAAsB;QAACW,KAAK,EAAE;UAAElB,OAAO,EAAE,MAAM;UAAE0B,UAAU,EAAE,QAAQ;UAAEC,cAAc,EAAE,eAAe;UAAEC,YAAY,EAAE;QAAG,CAAE;QAAApB,QAAA,gBACxIvK,OAAA;UAAKsK,SAAS,EAAC,YAAY;UAACW,KAAK,EAAE;YAAEW,IAAI,EAAE,CAAC;YAAEC,QAAQ,EAAE,GAAG;YAAEX,WAAW,EAAE,EAAE;YAAErB,QAAQ,EAAE;UAAW,CAAE;UAAAU,QAAA,gBACnGvK,OAAA,CAAC9B,eAAe;YAAC8M,IAAI,EAAEvM,QAAS;YAACwM,KAAK,EAAE;cAAEpB,QAAQ,EAAE,UAAU;cAAEiC,IAAI,EAAE,EAAE;cAAEC,GAAG,EAAE,KAAK;cAAEC,SAAS,EAAE,kBAAkB;cAAEC,KAAK,EAAE,SAAS;cAAE9D,QAAQ,EAAE,QAAQ;cAAE+D,OAAO,EAAE;YAAI;UAAE;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7K7K,OAAA;YACE8B,IAAI,EAAC,MAAM;YACXqK,WAAW,EAAC,0BAAuB;YACnCpH,KAAK,EAAE1D,UAAW;YAClB+K,QAAQ,EAAE7F,kBAAmB;YAC7B0E,KAAK,EAAE;cAAEoB,KAAK,EAAE,MAAM;cAAEjB,OAAO,EAAE,qBAAqB;cAAEkB,YAAY,EAAE,CAAC;cAAEC,MAAM,EAAE,mBAAmB;cAAEpE,QAAQ,EAAE,MAAM;cAAEqE,SAAS,EAAE;YAAkC;UAAE;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN7K,OAAA;UAAKsK,SAAS,EAAC,eAAe;UAACW,KAAK,EAAE;YAAElB,OAAO,EAAE,MAAM;YAAE0B,UAAU,EAAE,QAAQ;YAAEgB,GAAG,EAAE;UAAG,CAAE;UAAAlC,QAAA,gBACvFvK,OAAA;YAAKsK,SAAS,EAAC,mBAAmB;YAACW,KAAK,EAAE;cAAEpB,QAAQ,EAAE,UAAU;cAAEqB,WAAW,EAAE,CAAC;cAAEwB,MAAM,EAAE;YAAU,CAAE;YAAAnC,QAAA,gBACpGvK,OAAA,CAAC9B,eAAe;cAAC8M,IAAI,EAAEtM,MAAO;cAACuM,KAAK,EAAE;gBAAE9C,QAAQ,EAAE,QAAQ;gBAAE8D,KAAK,EAAE;cAAU;YAAE;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClF7K,OAAA;cAAMiL,KAAK,EAAE;gBAAEpB,QAAQ,EAAE,UAAU;gBAAEkC,GAAG,EAAE,CAAC,CAAC;gBAAEY,KAAK,EAAE,CAAC,CAAC;gBAAEC,UAAU,EAAE,SAAS;gBAAEX,KAAK,EAAE,MAAM;gBAAEK,YAAY,EAAE,KAAK;gBAAEnE,QAAQ,EAAE,QAAQ;gBAAEiD,OAAO,EAAE,SAAS;gBAAEyB,UAAU,EAAE;cAAI,CAAE;cAAAtC,QAAA,EAAC;YAAC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpL,CAAC,eACN7K,OAAA;YAAMiL,KAAK,EAAE;cAAE4B,UAAU,EAAE,GAAG;cAAEZ,KAAK,EAAE,SAAS;cAAE9D,QAAQ,EAAE,MAAM;cAAE+C,WAAW,EAAE;YAAE,CAAE;YAAAX,QAAA,EAAC;UAAc;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3G7K,OAAA,CAAC9B,eAAe;YAAC8M,IAAI,EAAEzM,SAAU;YAAC0M,KAAK,EAAE;cAAE9C,QAAQ,EAAE,QAAQ;cAAE8D,KAAK,EAAE,SAAS;cAAEW,UAAU,EAAE,MAAM;cAAEN,YAAY,EAAE,KAAK;cAAElB,OAAO,EAAE,CAAC;cAAEoB,SAAS,EAAE;YAAkC;UAAE;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN7K,OAAA;QAAIsK,SAAS,EAAC,mBAAmB;QAAAC,QAAA,GAAC,4BACT,EAAChK,mBAAmB,CAACoE,MAAM,EAAC,GACrD;MAAA;QAAA+F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAEL7K,OAAA;QAAKiL,KAAK,EAAE;UAAElB,OAAO,EAAE,MAAM;UAAE0C,GAAG,EAAE,EAAE;UAAEd,YAAY,EAAE;QAAG,CAAE;QAAApB,QAAA,gBACzDvK,OAAA;UACEsK,SAAS,EAAC,iBAAiB;UAC3BW,KAAK,EAAE;YAAE4B,UAAU,EAAE,GAAG;YAAE1E,QAAQ,EAAE,MAAM;YAAEmE,YAAY,EAAE,EAAE;YAAEM,UAAU,EAAE,kDAAkD;YAAEX,KAAK,EAAE,MAAM;YAAEO,SAAS,EAAE;UAAmC,CAAE;UAC7LlB,OAAO,EAAEA,CAAA,KAAM;YACb9J,YAAY,CAAC,IAAI,CAAC;YAClBV,YAAY,CAAC,IAAI,CAAC;UACpB,CAAE;UAAAyJ,QAAA,gBAEFvK,OAAA,CAAC9B,eAAe;YAAC8M,IAAI,EAAEpM,MAAO;YAACqM,KAAK,EAAE;cAACC,WAAW,EAAE;YAAC;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,4BAE5D;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT7K,OAAA;UAAQsK,SAAS,EAAC,iBAAiB;UAACW,KAAK,EAAE;YAAE4B,UAAU,EAAE,GAAG;YAAE1E,QAAQ,EAAE,MAAM;YAAEmE,YAAY,EAAE,EAAE;YAAEM,UAAU,EAAE,kDAAkD;YAAEX,KAAK,EAAE,MAAM;YAAEO,SAAS,EAAE;UAAkC,CAAE;UAAClB,OAAO,EAAEvE,iBAAkB;UAAAwD,QAAA,EAAC;QAE7P;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT7K,OAAA;UAAQsK,SAAS,EAAC,iBAAiB;UAACW,KAAK,EAAE;YAAE4B,UAAU,EAAE,GAAG;YAAE1E,QAAQ,EAAE,MAAM;YAAEmE,YAAY,EAAE,EAAE;YAAEM,UAAU,EAAE,kDAAkD;YAAEX,KAAK,EAAE,MAAM;YAAEO,SAAS,EAAE;UAAkC,CAAE;UAAClB,OAAO,EAAE1D,eAAgB;UAAA2C,QAAA,EAAC;QAE3P;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT7K,OAAA;UAAQsK,SAAS,EAAC,iBAAiB;UAACW,KAAK,EAAE;YAAE4B,UAAU,EAAE,GAAG;YAAE1E,QAAQ,EAAE,MAAM;YAAEmE,YAAY,EAAE,EAAE;YAAEM,UAAU,EAAE,kDAAkD;YAAEX,KAAK,EAAE,MAAM;YAAEO,SAAS,EAAE;UAAkC,CAAE;UAAClB,OAAO,EAAEjD,WAAY;UAAAkC,QAAA,EAAC;QAEvP;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT7K,OAAA;UACEsK,SAAS,EAAC,iBAAiB;UAC3BW,KAAK,EAAE;YAAE4B,UAAU,EAAE,GAAG;YAAE1E,QAAQ,EAAE,MAAM;YAAEmE,YAAY,EAAE,EAAE;YAAEM,UAAU,EAAE,kDAAkD;YAAEX,KAAK,EAAE,MAAM;YAAEO,SAAS,EAAE;UAAmC,CAAE;UAC7LlB,OAAO,EAAEA,CAAA,KAAM5J,YAAY,CAAC,CAACD,SAAS,CAAE;UAAA8I,QAAA,GAEvC9I,SAAS,GAAG,SAAS,GAAG,UAAU,EAAC,mBACtC;QAAA;UAAAiJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGLpJ,SAAS,iBACRzB,OAAA;QAAKiL,KAAK,EAAE;UACV2B,UAAU,EAAE,MAAM;UAClBN,YAAY,EAAE,EAAE;UAChBlB,OAAO,EAAE,EAAE;UACXO,YAAY,EAAE,EAAE;UAChBa,SAAS,EAAE,4BAA4B;UACvCD,MAAM,EAAE;QACV,CAAE;QAAAhC,QAAA,gBACAvK,OAAA;UAAIiL,KAAK,EAAE;YACTgB,KAAK,EAAE,SAAS;YAChBN,YAAY,EAAE,EAAE;YAChBxD,QAAQ,EAAE,QAAQ;YAClB0E,UAAU,EAAE,GAAG;YACfxB,SAAS,EAAE;UACb,CAAE;UAAAd,QAAA,EAAC;QAEH;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEL7K,OAAA;UAAKiL,KAAK,EAAE;YAAElB,OAAO,EAAE,MAAM;YAAE+C,mBAAmB,EAAE,SAAS;YAAEL,GAAG,EAAE,EAAE;YAAEhB,UAAU,EAAE;UAAS,CAAE;UAAAlB,QAAA,gBAE7FvK,OAAA;YAAKiL,KAAK,EAAE;cACV2B,UAAU,EAAE,SAAS;cACrBxB,OAAO,EAAE,EAAE;cACXkB,YAAY,EAAE,CAAC;cACfC,MAAM,EAAE,mBAAmB;cAC3BQ,MAAM,EAAE;YACV,CAAE;YAAAxC,QAAA,eACAvK,OAAA,CAACH,GAAG;cAACuE,IAAI,EAAE0E,eAAgB;cAACkE,OAAO,EAAEvD;YAAa;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eAGN7K,OAAA;YAAKiL,KAAK,EAAE;cACV2B,UAAU,EAAE,SAAS;cACrBxB,OAAO,EAAE,EAAE;cACXkB,YAAY,EAAE,CAAC;cACfC,MAAM,EAAE,mBAAmB;cAC3BQ,MAAM,EAAE;YACV,CAAE;YAAAxC,QAAA,eACAvK,OAAA,CAACF,QAAQ;cAACsE,IAAI,EAAEoF,aAAc;cAACwD,OAAO,EAAE3C;YAAgB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN7K,OAAA;UAAKiL,KAAK,EAAE;YACVE,SAAS,EAAE,EAAE;YACbC,OAAO,EAAE,EAAE;YACXwB,UAAU,EAAE,SAAS;YACrBN,YAAY,EAAE,CAAC;YACfC,MAAM,EAAE;UACV,CAAE;UAAAhC,QAAA,gBACAvK,OAAA;YAAIiL,KAAK,EAAE;cAAEgB,KAAK,EAAE,SAAS;cAAEN,YAAY,EAAE,EAAE;cAAExD,QAAQ,EAAE;YAAS,CAAE;YAAAoC,QAAA,EAAC;UAAS;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrF7K,OAAA;YAAKiL,KAAK,EAAE;cAAElB,OAAO,EAAE,MAAM;cAAE+C,mBAAmB,EAAE,sCAAsC;cAAEL,GAAG,EAAE;YAAG,CAAE;YAAAlC,QAAA,gBACpGvK,OAAA;cAAKiL,KAAK,EAAE;gBAAEI,SAAS,EAAE,QAAQ;gBAAED,OAAO,EAAE,EAAE;gBAAEwB,UAAU,EAAE,MAAM;gBAAEN,YAAY,EAAE,CAAC;gBAAEC,MAAM,EAAE;cAAoB,CAAE;cAAAhC,QAAA,gBACjHvK,OAAA;gBAAKiL,KAAK,EAAE;kBAAE9C,QAAQ,EAAE,MAAM;kBAAE0E,UAAU,EAAE,MAAM;kBAAEZ,KAAK,EAAE;gBAAU,CAAE;gBAAA1B,QAAA,EACpEhK,mBAAmB,CAACoE;cAAM;gBAAA+F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC,eACN7K,OAAA;gBAAKiL,KAAK,EAAE;kBAAEgB,KAAK,EAAE,SAAS;kBAAE9D,QAAQ,EAAE;gBAAS,CAAE;gBAAAoC,QAAA,EAAC;cAAiB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1E,CAAC,eACN7K,OAAA;cAAKiL,KAAK,EAAE;gBAAEI,SAAS,EAAE,QAAQ;gBAAED,OAAO,EAAE,EAAE;gBAAEwB,UAAU,EAAE,MAAM;gBAAEN,YAAY,EAAE,CAAC;gBAAEC,MAAM,EAAE;cAAoB,CAAE;cAAAhC,QAAA,gBACjHvK,OAAA;gBAAKiL,KAAK,EAAE;kBAAE9C,QAAQ,EAAE,MAAM;kBAAE0E,UAAU,EAAE,MAAM;kBAAEZ,KAAK,EAAE;gBAAU,CAAE;gBAAA1B,QAAA,EACpE3B,WAAW,CAAC,YAAY,CAAC,IAAI;cAAC;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,eACN7K,OAAA;gBAAKiL,KAAK,EAAE;kBAAEgB,KAAK,EAAE,SAAS;kBAAE9D,QAAQ,EAAE;gBAAS,CAAE;gBAAAoC,QAAA,EAAC;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpE,CAAC,eACN7K,OAAA;cAAKiL,KAAK,EAAE;gBAAEI,SAAS,EAAE,QAAQ;gBAAED,OAAO,EAAE,EAAE;gBAAEwB,UAAU,EAAE,MAAM;gBAAEN,YAAY,EAAE,CAAC;gBAAEC,MAAM,EAAE;cAAoB,CAAE;cAAAhC,QAAA,gBACjHvK,OAAA;gBAAKiL,KAAK,EAAE;kBAAE9C,QAAQ,EAAE,MAAM;kBAAE0E,UAAU,EAAE,MAAM;kBAAEZ,KAAK,EAAE;gBAAU,CAAE;gBAAA1B,QAAA,EACpE3B,WAAW,CAAC,gBAAgB,CAAC,IAAI;cAAC;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,eACN7K,OAAA;gBAAKiL,KAAK,EAAE;kBAAEgB,KAAK,EAAE,SAAS;kBAAE9D,QAAQ,EAAE;gBAAS,CAAE;gBAAAoC,QAAA,EAAC;cAAc;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvE,CAAC,eACN7K,OAAA;cAAKiL,KAAK,EAAE;gBAAEI,SAAS,EAAE,QAAQ;gBAAED,OAAO,EAAE,EAAE;gBAAEwB,UAAU,EAAE,MAAM;gBAAEN,YAAY,EAAE,CAAC;gBAAEC,MAAM,EAAE;cAAoB,CAAE;cAAAhC,QAAA,gBACjHvK,OAAA;gBAAKiL,KAAK,EAAE;kBAAE9C,QAAQ,EAAE,MAAM;kBAAE0E,UAAU,EAAE,MAAM;kBAAEZ,KAAK,EAAE;gBAAU,CAAE;gBAAA1B,QAAA,EACpE3B,WAAW,CAAC,cAAc,CAAC,IAAI;cAAC;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC,eACN7K,OAAA;gBAAKiL,KAAK,EAAE;kBAAEgB,KAAK,EAAE,SAAS;kBAAE9D,QAAQ,EAAE;gBAAS,CAAE;gBAAAoC,QAAA,EAAC;cAAY;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAhK,SAAS,iBACRb,OAAA;QAAKiL,KAAK,EAAE;UACVpB,QAAQ,EAAE,OAAO;UACjBkC,GAAG,EAAE,CAAC;UACND,IAAI,EAAE,CAAC;UACPa,KAAK,EAAE,CAAC;UACRM,MAAM,EAAE,CAAC;UACT5D,eAAe,EAAE,oBAAoB;UACrCU,OAAO,EAAE,MAAM;UACf0B,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBwB,MAAM,EAAE;QACV,CAAE;QAAA3C,QAAA,eACAvK,OAAA;UAAKiL,KAAK,EAAE;YACV5B,eAAe,EAAE,OAAO;YACxBiD,YAAY,EAAE,EAAE;YAChBlB,OAAO,EAAE,EAAE;YACXS,QAAQ,EAAE,GAAG;YACbQ,KAAK,EAAE,KAAK;YACZc,SAAS,EAAE,MAAM;YACjBC,SAAS,EAAE,MAAM;YACjBZ,SAAS,EAAE;UACb,CAAE;UAAAjC,QAAA,gBACAvK,OAAA;YAAKiL,KAAK,EAAE;cAAElB,OAAO,EAAE,MAAM;cAAE2B,cAAc,EAAE,eAAe;cAAED,UAAU,EAAE,QAAQ;cAAEE,YAAY,EAAE;YAAG,CAAE;YAAApB,QAAA,gBACvGvK,OAAA;cAAIiL,KAAK,EAAE;gBAAEoC,MAAM,EAAE,CAAC;gBAAEpB,KAAK,EAAE,SAAS;gBAAE9D,QAAQ,EAAE,QAAQ;gBAAE0E,UAAU,EAAE;cAAI,CAAE;cAAAtC,QAAA,EAC7EhJ,SAAS,GAAG,wBAAwB,GAAG;YAA8B;cAAAmJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpE,CAAC,eACL7K,OAAA;cACEsL,OAAO,EAAEA,CAAA,KAAM;gBACbxK,YAAY,CAAC,KAAK,CAAC;gBACnBU,YAAY,CAAC,IAAI,CAAC;cACpB,CAAE;cACFyJ,KAAK,EAAE;gBACL2B,UAAU,EAAE,MAAM;gBAClBL,MAAM,EAAE,MAAM;gBACdpE,QAAQ,EAAE,QAAQ;gBAClB8D,KAAK,EAAE,MAAM;gBACbS,MAAM,EAAE,SAAS;gBACjBtB,OAAO,EAAE,CAAC;gBACVkB,YAAY,EAAE;cAChB,CAAE;cAAA/B,QAAA,eAEFvK,OAAA,CAAC9B,eAAe;gBAAC8M,IAAI,EAAErM;cAAQ;gBAAA+L,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN7K,OAAA;YAAMsN,QAAQ,EAAEnI,YAAa;YAAAoF,QAAA,gBAC3BvK,OAAA;cAAKiL,KAAK,EAAE;gBAAElB,OAAO,EAAE,MAAM;gBAAE+C,mBAAmB,EAAE,SAAS;gBAAEL,GAAG,EAAE,EAAE;gBAAEd,YAAY,EAAE;cAAG,CAAE;cAAApB,QAAA,gBACzFvK,OAAA;gBAAAuK,QAAA,gBACEvK,OAAA;kBAAOiL,KAAK,EAAE;oBAAElB,OAAO,EAAE,OAAO;oBAAE4B,YAAY,EAAE,CAAC;oBAAEkB,UAAU,EAAE,GAAG;oBAAEZ,KAAK,EAAE;kBAAO,CAAE;kBAAA1B,QAAA,EAAC;gBAErF;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR7K,OAAA;kBACE8B,IAAI,EAAC,MAAM;kBACXgD,IAAI,EAAC,KAAK;kBACVC,KAAK,EAAEpD,QAAQ,CAACE,GAAI;kBACpBuK,QAAQ,EAAExH,iBAAkB;kBAC5B2I,QAAQ;kBACRtC,KAAK,EAAE;oBACLoB,KAAK,EAAE,MAAM;oBACbjB,OAAO,EAAE,MAAM;oBACfmB,MAAM,EAAE,mBAAmB;oBAC3BD,YAAY,EAAE,CAAC;oBACfnE,QAAQ,EAAE,MAAM;oBAChBqF,UAAU,EAAE;kBACd,CAAE;kBACFC,OAAO,EAAG5I,CAAC,IAAKA,CAAC,CAACI,MAAM,CAACgG,KAAK,CAAC3B,WAAW,GAAG,SAAU;kBACvDoE,MAAM,EAAG7I,CAAC,IAAKA,CAAC,CAACI,MAAM,CAACgG,KAAK,CAAC3B,WAAW,GAAG;gBAAU;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN7K,OAAA;gBAAAuK,QAAA,gBACEvK,OAAA;kBAAOiL,KAAK,EAAE;oBAAElB,OAAO,EAAE,OAAO;oBAAE4B,YAAY,EAAE,CAAC;oBAAEkB,UAAU,EAAE,GAAG;oBAAEZ,KAAK,EAAE;kBAAO,CAAE;kBAAA1B,QAAA,EAAC;gBAErF;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR7K,OAAA;kBACE8E,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAEpD,QAAQ,CAACG,IAAK;kBACrBsK,QAAQ,EAAExH,iBAAkB;kBAC5B2I,QAAQ;kBACRtC,KAAK,EAAE;oBACLoB,KAAK,EAAE,MAAM;oBACbjB,OAAO,EAAE,MAAM;oBACfmB,MAAM,EAAE,mBAAmB;oBAC3BD,YAAY,EAAE,CAAC;oBACfnE,QAAQ,EAAE;kBACZ,CAAE;kBAAAoC,QAAA,gBAEFvK,OAAA;oBAAQ+E,KAAK,EAAC,EAAE;oBAAAwF,QAAA,EAAC;kBAAoB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EAC7ClI,WAAW,CAACiD,GAAG,CAAC9D,IAAI,iBACnB9B,OAAA;oBAAmB+E,KAAK,EAAEjD,IAAK;oBAAAyI,QAAA,EAAEzI;kBAAI,GAAxBA,IAAI;oBAAA4I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAA6B,CAC/C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN7K,OAAA;cAAKiL,KAAK,EAAE;gBAAElB,OAAO,EAAE,MAAM;gBAAE+C,mBAAmB,EAAE,SAAS;gBAAEL,GAAG,EAAE,EAAE;gBAAEd,YAAY,EAAE;cAAG,CAAE;cAAApB,QAAA,gBACzFvK,OAAA;gBAAAuK,QAAA,gBACEvK,OAAA;kBAAOiL,KAAK,EAAE;oBAAElB,OAAO,EAAE,OAAO;oBAAE4B,YAAY,EAAE,CAAC;oBAAEkB,UAAU,EAAE,GAAG;oBAAEZ,KAAK,EAAE;kBAAO,CAAE;kBAAA1B,QAAA,EAAC;gBAErF;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR7K,OAAA;kBACE8B,IAAI,EAAC,MAAM;kBACXgD,IAAI,EAAC,cAAc;kBACnBC,KAAK,EAAEpD,QAAQ,CAACI,YAAa;kBAC7BqK,QAAQ,EAAExH,iBAAkB;kBAC5BqG,KAAK,EAAE;oBACLoB,KAAK,EAAE,MAAM;oBACbjB,OAAO,EAAE,MAAM;oBACfmB,MAAM,EAAE,mBAAmB;oBAC3BD,YAAY,EAAE,CAAC;oBACfnE,QAAQ,EAAE;kBACZ;gBAAE;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN7K,OAAA;gBAAAuK,QAAA,gBACEvK,OAAA;kBAAOiL,KAAK,EAAE;oBAAElB,OAAO,EAAE,OAAO;oBAAE4B,YAAY,EAAE,CAAC;oBAAEkB,UAAU,EAAE,GAAG;oBAAEZ,KAAK,EAAE;kBAAO,CAAE;kBAAA1B,QAAA,EAAC;gBAErF;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR7K,OAAA;kBACE8E,IAAI,EAAC,QAAQ;kBACbC,KAAK,EAAEpD,QAAQ,CAACK,MAAO;kBACvBoK,QAAQ,EAAExH,iBAAkB;kBAC5B2I,QAAQ;kBACRtC,KAAK,EAAE;oBACLoB,KAAK,EAAE,MAAM;oBACbjB,OAAO,EAAE,MAAM;oBACfmB,MAAM,EAAE,mBAAmB;oBAC3BD,YAAY,EAAE,CAAC;oBACfnE,QAAQ,EAAE;kBACZ,CAAE;kBAAAoC,QAAA,EAED7H,aAAa,CAACkD,GAAG,CAAC5D,MAAM,iBACvBhC,OAAA;oBAAqB+E,KAAK,EAAE/C,MAAO;oBAAAuI,QAAA,EAAEvI;kBAAM,GAA9BA,MAAM;oBAAA0I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAiC,CACrD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN7K,OAAA;cAAKiL,KAAK,EAAE;gBAAElB,OAAO,EAAE,MAAM;gBAAE+C,mBAAmB,EAAE,SAAS;gBAAEL,GAAG,EAAE,EAAE;gBAAEd,YAAY,EAAE;cAAG,CAAE;cAAApB,QAAA,gBACzFvK,OAAA;gBAAAuK,QAAA,gBACEvK,OAAA;kBAAOiL,KAAK,EAAE;oBAAElB,OAAO,EAAE,OAAO;oBAAE4B,YAAY,EAAE,CAAC;oBAAEkB,UAAU,EAAE,GAAG;oBAAEZ,KAAK,EAAE;kBAAO,CAAE;kBAAA1B,QAAA,EAAC;gBAErF;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR7K,OAAA;kBACE8B,IAAI,EAAC,MAAM;kBACXgD,IAAI,EAAC,YAAY;kBACjBC,KAAK,EAAEpD,QAAQ,CAACM,UAAW;kBAC3BmK,QAAQ,EAAExH,iBAAkB;kBAC5BqG,KAAK,EAAE;oBACLoB,KAAK,EAAE,MAAM;oBACbjB,OAAO,EAAE,MAAM;oBACfmB,MAAM,EAAE,mBAAmB;oBAC3BD,YAAY,EAAE,CAAC;oBACfnE,QAAQ,EAAE;kBACZ;gBAAE;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN7K,OAAA;gBAAAuK,QAAA,gBACEvK,OAAA;kBAAOiL,KAAK,EAAE;oBAAElB,OAAO,EAAE,OAAO;oBAAE4B,YAAY,EAAE,CAAC;oBAAEkB,UAAU,EAAE,GAAG;oBAAEZ,KAAK,EAAE;kBAAO,CAAE;kBAAA1B,QAAA,EAAC;gBAErF;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR7K,OAAA;kBACE8E,IAAI,EAAC,gBAAgB;kBACrBC,KAAK,EAAEpD,QAAQ,CAACO,cAAe;kBAC/BkK,QAAQ,EAAExH,iBAAkB;kBAC5BqG,KAAK,EAAE;oBACLoB,KAAK,EAAE,MAAM;oBACbjB,OAAO,EAAE,MAAM;oBACfmB,MAAM,EAAE,mBAAmB;oBAC3BD,YAAY,EAAE,CAAC;oBACfnE,QAAQ,EAAE;kBACZ,CAAE;kBAAAoC,QAAA,gBAEFvK,OAAA;oBAAQ+E,KAAK,EAAC,EAAE;oBAAAwF,QAAA,EAAC;kBAA2B;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACpD9J,YAAY,CAAC6E,GAAG,CAACvC,WAAW,iBAC3BrD,OAAA;oBAA6B+E,KAAK,EAAE1B,WAAW,CAACyC,EAAG;oBAAAyE,QAAA,EAChDlH,WAAW,CAACxB;kBAAG,GADLwB,WAAW,CAACyC,EAAE;oBAAA4E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEnB,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN7K,OAAA;cAAKiL,KAAK,EAAE;gBAAElB,OAAO,EAAE,MAAM;gBAAE+C,mBAAmB,EAAE,SAAS;gBAAEL,GAAG,EAAE,EAAE;gBAAEd,YAAY,EAAE;cAAG,CAAE;cAAApB,QAAA,gBACzFvK,OAAA;gBAAAuK,QAAA,gBACEvK,OAAA;kBAAOiL,KAAK,EAAE;oBAAElB,OAAO,EAAE,OAAO;oBAAE4B,YAAY,EAAE,CAAC;oBAAEkB,UAAU,EAAE,GAAG;oBAAEZ,KAAK,EAAE;kBAAO,CAAE;kBAAA1B,QAAA,EAAC;gBAErF;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR7K,OAAA;kBACE8B,IAAI,EAAC,MAAM;kBACXgD,IAAI,EAAC,qBAAqB;kBAC1BC,KAAK,EAAEpD,QAAQ,CAACQ,mBAAoB;kBACpCiK,QAAQ,EAAExH,iBAAkB;kBAC5BqG,KAAK,EAAE;oBACLoB,KAAK,EAAE,MAAM;oBACbjB,OAAO,EAAE,MAAM;oBACfmB,MAAM,EAAE,mBAAmB;oBAC3BD,YAAY,EAAE,CAAC;oBACfnE,QAAQ,EAAE;kBACZ;gBAAE;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN7K,OAAA;gBAAAuK,QAAA,gBACEvK,OAAA;kBAAOiL,KAAK,EAAE;oBAAElB,OAAO,EAAE,OAAO;oBAAE4B,YAAY,EAAE,CAAC;oBAAEkB,UAAU,EAAE,GAAG;oBAAEZ,KAAK,EAAE;kBAAO,CAAE;kBAAA1B,QAAA,EAAC;gBAErF;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR7K,OAAA;kBACE8B,IAAI,EAAC,MAAM;kBACXgD,IAAI,EAAC,mBAAmB;kBACxBC,KAAK,EAAEpD,QAAQ,CAACS,iBAAkB;kBAClCgK,QAAQ,EAAExH,iBAAkB;kBAC5BqG,KAAK,EAAE;oBACLoB,KAAK,EAAE,MAAM;oBACbjB,OAAO,EAAE,MAAM;oBACfmB,MAAM,EAAE,mBAAmB;oBAC3BD,YAAY,EAAE,CAAC;oBACfnE,QAAQ,EAAE;kBACZ;gBAAE;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN7K,OAAA;cAAKiL,KAAK,EAAE;gBAAElB,OAAO,EAAE,MAAM;gBAAE+C,mBAAmB,EAAE,aAAa;gBAAEL,GAAG,EAAE,EAAE;gBAAEd,YAAY,EAAE;cAAG,CAAE;cAAApB,QAAA,gBAC7FvK,OAAA;gBAAAuK,QAAA,gBACEvK,OAAA;kBAAOiL,KAAK,EAAE;oBAAElB,OAAO,EAAE,OAAO;oBAAE4B,YAAY,EAAE,CAAC;oBAAEkB,UAAU,EAAE,GAAG;oBAAEZ,KAAK,EAAE;kBAAO,CAAE;kBAAA1B,QAAA,EAAC;gBAErF;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR7K,OAAA;kBACE8B,IAAI,EAAC,QAAQ;kBACbgD,IAAI,EAAC,cAAc;kBACnBC,KAAK,EAAEpD,QAAQ,CAACW,YAAa;kBAC7B8J,QAAQ,EAAExH,iBAAkB;kBAC5B+I,GAAG,EAAC,GAAG;kBACP1C,KAAK,EAAE;oBACLoB,KAAK,EAAE,MAAM;oBACbjB,OAAO,EAAE,MAAM;oBACfmB,MAAM,EAAE,mBAAmB;oBAC3BD,YAAY,EAAE,CAAC;oBACfnE,QAAQ,EAAE;kBACZ;gBAAE;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN7K,OAAA;gBAAAuK,QAAA,gBACEvK,OAAA;kBAAOiL,KAAK,EAAE;oBAAElB,OAAO,EAAE,OAAO;oBAAE4B,YAAY,EAAE,CAAC;oBAAEkB,UAAU,EAAE,GAAG;oBAAEZ,KAAK,EAAE;kBAAO,CAAE;kBAAA1B,QAAA,EAAC;gBAErF;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR7K,OAAA;kBACE8B,IAAI,EAAC,QAAQ;kBACbgD,IAAI,EAAC,WAAW;kBAChBC,KAAK,EAAEpD,QAAQ,CAACY,SAAU;kBAC1B6J,QAAQ,EAAExH,iBAAkB;kBAC5B+I,GAAG,EAAC,GAAG;kBACP1C,KAAK,EAAE;oBACLoB,KAAK,EAAE,MAAM;oBACbjB,OAAO,EAAE,MAAM;oBACfmB,MAAM,EAAE,mBAAmB;oBAC3BD,YAAY,EAAE,CAAC;oBACfnE,QAAQ,EAAE;kBACZ;gBAAE;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN7K,OAAA;gBAAAuK,QAAA,gBACEvK,OAAA;kBAAOiL,KAAK,EAAE;oBAAElB,OAAO,EAAE,OAAO;oBAAE4B,YAAY,EAAE,CAAC;oBAAEkB,UAAU,EAAE,GAAG;oBAAEZ,KAAK,EAAE;kBAAO,CAAE;kBAAA1B,QAAA,EAAC;gBAErF;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR7K,OAAA;kBACE8B,IAAI,EAAC,QAAQ;kBACbgD,IAAI,EAAC,WAAW;kBAChBC,KAAK,EAAEpD,QAAQ,CAACa,SAAU;kBAC1B4J,QAAQ,EAAExH,iBAAkB;kBAC5B+I,GAAG,EAAC,GAAG;kBACP1C,KAAK,EAAE;oBACLoB,KAAK,EAAE,MAAM;oBACbjB,OAAO,EAAE,MAAM;oBACfmB,MAAM,EAAE,mBAAmB;oBAC3BD,YAAY,EAAE,CAAC;oBACfnE,QAAQ,EAAE;kBACZ;gBAAE;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN7K,OAAA;cAAKiL,KAAK,EAAE;gBAAEU,YAAY,EAAE;cAAG,CAAE;cAAApB,QAAA,eAC/BvK,OAAA;gBAAOiL,KAAK,EAAE;kBAAElB,OAAO,EAAE,MAAM;kBAAE0B,UAAU,EAAE,QAAQ;kBAAEoB,UAAU,EAAE,GAAG;kBAAEZ,KAAK,EAAE,MAAM;kBAAES,MAAM,EAAE;gBAAU,CAAE;gBAAAnC,QAAA,gBACzGvK,OAAA;kBACE8B,IAAI,EAAC,UAAU;kBACfgD,IAAI,EAAC,UAAU;kBACfE,OAAO,EAAErD,QAAQ,CAACU,QAAS;kBAC3B+J,QAAQ,EAAExH,iBAAkB;kBAC5BqG,KAAK,EAAE;oBAAEC,WAAW,EAAE,CAAC;oBAAEc,SAAS,EAAE;kBAAa;gBAAE;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC,0BAEJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAEN7K,OAAA;cAAKiL,KAAK,EAAE;gBAAElB,OAAO,EAAE,MAAM;gBAAE0C,GAAG,EAAE,EAAE;gBAAEf,cAAc,EAAE;cAAW,CAAE;cAAAnB,QAAA,gBACnEvK,OAAA;gBACE8B,IAAI,EAAC,QAAQ;gBACbwJ,OAAO,EAAEA,CAAA,KAAM;kBACbxK,YAAY,CAAC,KAAK,CAAC;kBACnBU,YAAY,CAAC,IAAI,CAAC;gBACpB,CAAE;gBACFyJ,KAAK,EAAE;kBACLG,OAAO,EAAE,WAAW;kBACpBmB,MAAM,EAAE,mBAAmB;kBAC3BD,YAAY,EAAE,CAAC;kBACfM,UAAU,EAAE,OAAO;kBACnBX,KAAK,EAAE,MAAM;kBACb9D,QAAQ,EAAE,MAAM;kBAChB0E,UAAU,EAAE,GAAG;kBACfH,MAAM,EAAE;gBACV,CAAE;gBAAAnC,QAAA,EACH;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT7K,OAAA;gBACE8B,IAAI,EAAC,QAAQ;gBACb8L,QAAQ,EAAEzM,UAAW;gBACrB8J,KAAK,EAAE;kBACLG,OAAO,EAAE,WAAW;kBACpBmB,MAAM,EAAE,MAAM;kBACdD,YAAY,EAAE,CAAC;kBACfM,UAAU,EAAEzL,UAAU,GAAG,MAAM,GAAG,kDAAkD;kBACpF8K,KAAK,EAAE,OAAO;kBACd9D,QAAQ,EAAE,MAAM;kBAChB0E,UAAU,EAAE,GAAG;kBACfH,MAAM,EAAEvL,UAAU,GAAG,aAAa,GAAG,SAAS;kBAC9CqL,SAAS,EAAE;gBACb,CAAE;gBAAAjC,QAAA,EAEDpJ,UAAU,GACNI,SAAS,GAAG,0BAA0B,GAAG,mBAAmB,GAC5DA,SAAS,GAAG,wBAAwB,GAAG;cAAwB;gBAAAmJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGApK,OAAO,gBACNT,OAAA;QAAKiL,KAAK,EAAE;UAAEI,SAAS,EAAE,QAAQ;UAAED,OAAO,EAAE;QAAO,CAAE;QAAAb,QAAA,eACnDvK,OAAA;UAAAuK,QAAA,EAAG;QAA6B;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,GACJlK,KAAK,gBACPX,OAAA;QAAKiL,KAAK,EAAE;UAAEI,SAAS,EAAE,QAAQ;UAAED,OAAO,EAAE,MAAM;UAAEa,KAAK,EAAE;QAAU,CAAE;QAAA1B,QAAA,gBACrEvK,OAAA;UAAAuK,QAAA,GAAG,8BAA4B,EAAC5J,KAAK;QAAA;UAAA+J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1C7K,OAAA;UACEsL,OAAO,EAAEA,CAAA,KAAMjF,MAAM,CAACjG,QAAQ,CAACyN,MAAM,CAAC,CAAE;UACxC5C,KAAK,EAAE;YAAEE,SAAS,EAAE,MAAM;YAAEC,OAAO,EAAE,UAAU;YAAEwB,UAAU,EAAE,SAAS;YAAEX,KAAK,EAAE,OAAO;YAAEM,MAAM,EAAE,MAAM;YAAED,YAAY,EAAE,KAAK;YAAEI,MAAM,EAAE;UAAU,CAAE;UAAAnC,QAAA,EAClJ;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,GACJ,CAACxG,KAAK,CAACC,OAAO,CAAC/D,mBAAmB,CAAC,IAAIA,mBAAmB,CAACoE,MAAM,KAAK,CAAC,gBACzE3E,OAAA;QAAKiL,KAAK,EAAE;UAAEI,SAAS,EAAE,QAAQ;UAAED,OAAO,EAAE;QAAO,CAAE;QAAAb,QAAA,eACnDvK,OAAA;UAAAuK,QAAA,EAAIlJ,UAAU,GAAG,uBAAuB,GAAG;QAAyB;UAAAqJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtE,CAAC,gBAEN7K,OAAA;QAAOsK,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAClCvK,OAAA;UAAAuK,QAAA,eACEvK,OAAA;YAAAuK,QAAA,gBACEvK,OAAA;cAAAuK,QAAA,EAAI;YAAG;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACZ7K,OAAA;cAAAuK,QAAA,EAAI;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACb7K,OAAA;cAAAuK,QAAA,EAAI;YAAe;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxB7K,OAAA;cAAAuK,QAAA,EAAI;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACf7K,OAAA;cAAAuK,QAAA,EAAI;YAAY;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrB7K,OAAA;cAAAuK,QAAA,EAAI;YAAiB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1B7K,OAAA;cAAAuK,QAAA,EAAI;YAAW;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpB7K,OAAA;cAAAuK,QAAA,EAAI;YAAO;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACR7K,OAAA;UAAAuK,QAAA,EACGhK,mBAAmB,CAACqF,GAAG,CAAC,CAACC,EAAE,EAAEiI,KAAK,KAAK;YAAA,IAAAC,gBAAA;YACtC,MAAM7H,WAAW,GAAGL,EAAE,CAAC9D,YAAY,IAAI8D,EAAE,CAACK,WAAW,IAAI,KAAK;YAC9D,MAAMiB,SAAS,GAAGX,UAAU,CAACX,EAAE,CAACsB,SAAS,IAAItB,EAAE,CAAC5D,UAAU,CAAC;YAC3D,MAAMmF,eAAe,GAAGZ,UAAU,CAACX,EAAE,CAACuB,eAAe,IAAIvB,EAAE,CAACzD,iBAAiB,CAAC;YAC9E,MAAMiB,WAAW,GAAG,EAAA0K,gBAAA,GAAAlI,EAAE,CAACxC,WAAW,cAAA0K,gBAAA,uBAAdA,gBAAA,CAAgBlM,GAAG,KAAIgE,EAAE,CAAC3D,cAAc,IAAI,cAAc;YAE9E,oBACElC,OAAA;cAAAuK,QAAA,gBACEvK,OAAA;gBAAAuK,QAAA,EAAK1E,EAAE,CAAChE,GAAG,IAAI;cAAK;gBAAA6I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC1B7K,OAAA;gBAAAuK,QAAA,EAAK1E,EAAE,CAAC/D,IAAI,IAAI;cAAK;gBAAA4I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC3B7K,OAAA;gBAAAuK,QAAA,EAAKrE;cAAW;gBAAAwE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtB7K,OAAA;gBAAAuK,QAAA,EAAK1E,EAAE,CAAC7D,MAAM,IAAI;cAAK;gBAAA0I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC7B7K,OAAA;gBAAAuK,QAAA,EAAKpD;cAAS;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACpB7K,OAAA;gBAAAuK,QAAA,EAAKnD;cAAe;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC1B7K,OAAA;gBAAAuK,QAAA,EAAKlH;cAAW;gBAAAqH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtB7K,OAAA;gBAAAuK,QAAA,eACEvK,OAAA;kBAAKiL,KAAK,EAAE;oBAAElB,OAAO,EAAE,MAAM;oBAAE0C,GAAG,EAAE;kBAAM,CAAE;kBAAAlC,QAAA,gBAC1CvK,OAAA;oBACEsL,OAAO,EAAEA,CAAA,KAAMtF,UAAU,CAACH,EAAE,CAAE;oBAC9BoF,KAAK,EAAE;sBACL2B,UAAU,EAAE,SAAS;sBACrBX,KAAK,EAAE,OAAO;sBACdM,MAAM,EAAE,MAAM;sBACdD,YAAY,EAAE,KAAK;sBACnBlB,OAAO,EAAE,UAAU;sBACnBsB,MAAM,EAAE,SAAS;sBACjB3C,OAAO,EAAE,MAAM;sBACf0B,UAAU,EAAE,QAAQ;sBACpBgB,GAAG,EAAE;oBACP,CAAE;oBAAAlC,QAAA,gBAEFvK,OAAA,CAAC9B,eAAe;sBAAC8M,IAAI,EAAEnM;oBAAO;sBAAA6L,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACjC7K,OAAA;sBAAAuK,QAAA,EAAM;oBAAQ;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,eACT7K,OAAA;oBACEsL,OAAO,EAAEA,CAAA,KAAMlF,YAAY,CAACP,EAAE,CAACC,EAAE,CAAE;oBACnCmF,KAAK,EAAE;sBACL2B,UAAU,EAAE,SAAS;sBACrBX,KAAK,EAAE,OAAO;sBACdM,MAAM,EAAE,MAAM;sBACdD,YAAY,EAAE,KAAK;sBACnBlB,OAAO,EAAE,UAAU;sBACnBsB,MAAM,EAAE,SAAS;sBACjB3C,OAAO,EAAE,MAAM;sBACf0B,UAAU,EAAE,QAAQ;sBACpBgB,GAAG,EAAE;oBACP,CAAE;oBAAAlC,QAAA,gBAEFvK,OAAA,CAAC9B,eAAe;sBAAC8M,IAAI,EAAElM;oBAAQ;sBAAA4L,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAClC7K,OAAA;sBAAAuK,QAAA,EAAM;oBAAS;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GA7CEhF,EAAE,CAACC,EAAE,IAAIgI,KAAK;cAAApD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA8CnB,CAAC;UAET,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC1K,EAAA,CApmCuBD,WAAW;EAAA,QAChBlC,WAAW;AAAA;AAAAgQ,EAAA,GADN9N,WAAW;AAAA,IAAA8N,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}