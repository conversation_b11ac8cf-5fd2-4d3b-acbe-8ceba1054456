[{"C:\\Users\\<USER>\\gestion-equipement-frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\gestion-equipement-frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\gestion-equipement-frontend\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\gestion-equipement-frontend\\src\\Authentification\\ResetPassword.js": "4", "C:\\Users\\<USER>\\gestion-equipement-frontend\\src\\Authentification\\AuthContext.js": "5", "C:\\Users\\<USER>\\gestion-equipement-frontend\\src\\Authentification\\Login.js": "6", "C:\\Users\\<USER>\\gestion-equipement-frontend\\src\\Authentification\\ForgotPassword.js": "7", "C:\\Users\\<USER>\\gestion-equipement-frontend\\src\\pages\\Equipements.js": "8", "C:\\Users\\<USER>\\gestion-equipement-frontend\\src\\pages\\Dashboard.js": "9"}, {"size": 535, "mtime": 1752603426911, "results": "10", "hashOfConfig": "11"}, {"size": 937, "mtime": 1753134110693, "results": "12", "hashOfConfig": "11"}, {"size": 362, "mtime": 1752603426912, "results": "13", "hashOfConfig": "11"}, {"size": 1313, "mtime": 1753020186254, "results": "14", "hashOfConfig": "11"}, {"size": 2825, "mtime": 1753169843770, "results": "15", "hashOfConfig": "11"}, {"size": 2211, "mtime": 1753125624051, "results": "16", "hashOfConfig": "11"}, {"size": 1349, "mtime": 1753028437769, "results": "17", "hashOfConfig": "11"}, {"size": 42367, "mtime": 1753377369857, "results": "18", "hashOfConfig": "11"}, {"size": 12796, "mtime": 1753300866222, "results": "19", "hashOfConfig": "11"}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1f7lrac", {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\gestion-equipement-frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\gestion-equipement-frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\gestion-equipement-frontend\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\gestion-equipement-frontend\\src\\Authentification\\ResetPassword.js", [], [], "C:\\Users\\<USER>\\gestion-equipement-frontend\\src\\Authentification\\AuthContext.js", [], [], "C:\\Users\\<USER>\\gestion-equipement-frontend\\src\\Authentification\\Login.js", [], [], "C:\\Users\\<USER>\\gestion-equipement-frontend\\src\\Authentification\\ForgotPassword.js", [], [], "C:\\Users\\<USER>\\gestion-equipement-frontend\\src\\pages\\Equipements.js", ["47"], [], "C:\\Users\\<USER>\\gestion-equipement-frontend\\src\\pages\\Dashboard.js", [], [], {"ruleId": "48", "severity": 1, "message": "49", "line": 23, "column": 10, "nodeType": "50", "messageId": "51", "endLine": 23, "endColumn": 20}, "no-unused-vars", "'categories' is assigned a value but never used.", "Identifier", "unusedVar"]