import { useState } from "react";
import { useLocation } from "react-router-dom";

export default function ResetPassword() {
  const [newPassword, setNewPassword] = useState("");
  const [message, setMessage] = useState("");
  const query = new URLSearchParams(useLocation().search);
  const token = query.get("token");

  const handleSubmit = async (e) => {
    e.preventDefault();
    setMessage("");
    const response = await fetch("http://localhost:8080/api/auth/reset-password", {
      method: "POST",
      headers: { "Content-Type": "application/x-www-form-urlencoded" },
      body: new URLSearchParams({ token, newPassword }),
    });
    const text = await response.text();
    setMessage(text);
  };

  if (!token) {
    return <div>Token de réinitialisation manquant ou invalide.</div>;
  }

  return (
    <div className="reset-password-container">
      <h2>Réinitialisation du mot de passe</h2>
      <form onSubmit={handleSubmit}>
        <input
          type="password"
          placeholder="Nouveau mot de passe"
          value={newPassword}
          onChange={e => setNewPassword(e.target.value)}
          required
        />
        <button type="submit">Valider</button>
      </form>
      {message && <div>{message}</div>}
    </div>
  );
}