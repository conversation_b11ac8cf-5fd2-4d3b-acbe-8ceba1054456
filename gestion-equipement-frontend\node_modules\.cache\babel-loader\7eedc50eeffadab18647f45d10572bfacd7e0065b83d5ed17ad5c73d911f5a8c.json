{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\gestion-equipement-frontend\\\\src\\\\pages\\\\Equipements.js\",\n  _s = $RefreshSig$();\nimport './Equipements.css';\nimport { Link, useLocation } from 'react-router-dom';\nimport logo from '../assets/images/asment logo.jpg';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { faLaptop, faUsers, faExchangeAlt, faTruck, faUserCog, faSignOutAlt, faSearch, faBell, faTimes, faPlus, faEdit, faTrash } from '@fortawesome/free-solid-svg-icons';\nimport { useEffect, useState } from \"react\";\nimport jsPDF from \"jspdf\";\nimport \"jspdf-autotable\";\nimport * as XLSX from \"xlsx\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function Equipements() {\n  _s();\n  const location = useLocation();\n  const [equipements, setEquipements] = useState([]);\n  const [filteredEquipements, setFilteredEquipements] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [showModal, setShowModal] = useState(false);\n  const [fournisseurs, setFournisseurs] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [submitting, setSubmitting] = useState(false);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [editingId, setEditingId] = useState(null);\n\n  // État du formulaire\n  const [formData, setFormData] = useState({\n    nom: '',\n    type: '',\n    numero_serie: '',\n    statut: 'DISPONIBLE',\n    date_achat: '',\n    fournisseur_id: '',\n    date_debut_garantie: '',\n    date_fin_garantie: '',\n    en_stock: true,\n    stock_actuel: 1,\n    stock_max: 1,\n    stock_min: 1,\n    categorie_id: ''\n  });\n\n  // Options pour les statuts et types\n  const statutOptions = ['DISPONIBLE', 'EN_MAINTENANCE', 'OCCUPE', 'HORS_SERVICE'];\n  const typeOptions = ['ORDINATEUR', 'IMPRIMANTE', 'SCANNER', 'TELEPHONE', 'TABLETTE', 'SERVEUR', 'AUTRE'];\n  useEffect(() => {\n    loadEquipements();\n    loadFournisseurs();\n    loadCategories();\n  }, []);\n  useEffect(() => {\n    if (searchTerm === '') {\n      setFilteredEquipements(equipements);\n    } else {\n      const filtered = equipements.filter(equipement => {\n        var _equipement$fournisse;\n        return equipement.nom.toLowerCase().includes(searchTerm.toLowerCase()) || equipement.type.toLowerCase().includes(searchTerm.toLowerCase()) || equipement.numero_serie && equipement.numero_serie.toLowerCase().includes(searchTerm.toLowerCase()) || equipement.statut.toLowerCase().includes(searchTerm.toLowerCase()) || ((_equipement$fournisse = equipement.fournisseur) === null || _equipement$fournisse === void 0 ? void 0 : _equipement$fournisse.nom) && equipement.fournisseur.nom.toLowerCase().includes(searchTerm.toLowerCase());\n      });\n      setFilteredEquipements(filtered);\n    }\n  }, [searchTerm, equipements]);\n  const loadEquipements = () => {\n    console.log(\"Chargement des équipements...\");\n    const token = localStorage.getItem('authToken');\n    const headers = {\n      'Content-Type': 'application/json'\n    };\n    if (token) {\n      headers['Authorization'] = `Bearer ${token}`;\n    }\n    fetch(\"http://localhost:8081/api/equipements\", {\n      method: 'GET',\n      headers: headers\n    }).then(res => {\n      console.log(\"Status de la réponse équipements:\", res.status);\n      if (!res.ok) {\n        throw new Error(`Erreur HTTP: ${res.status}`);\n      }\n      return res.json();\n    }).then(data => {\n      console.log(\"Données équipements reçues:\", data);\n      if (Array.isArray(data)) {\n        setEquipements(data);\n        setFilteredEquipements(data);\n        setError(null);\n      } else {\n        console.warn(\"Les données équipements ne sont pas un tableau:\", data);\n        setEquipements([]);\n        setFilteredEquipements([]);\n        setError(\"Format de données incorrect\");\n      }\n      setLoading(false);\n    }).catch(err => {\n      console.error(\"Erreur lors du chargement des équipements:\", err);\n      setEquipements([]);\n      setFilteredEquipements([]);\n      setError(err.message || \"Erreur de chargement\");\n      setLoading(false);\n    });\n  };\n  const loadFournisseurs = () => {\n    const token = localStorage.getItem('authToken');\n    const headers = {\n      'Content-Type': 'application/json'\n    };\n    if (token) {\n      headers['Authorization'] = `Bearer ${token}`;\n    }\n    fetch(\"http://localhost:8081/api/fournisseurs\", {\n      method: 'GET',\n      headers: headers\n    }).then(res => res.ok ? res.json() : []).then(data => setFournisseurs(Array.isArray(data) ? data : [])).catch(err => {\n      console.error(\"Erreur lors du chargement des fournisseurs:\", err);\n      setFournisseurs([]);\n    });\n  };\n  const loadCategories = () => {\n    const token = localStorage.getItem('authToken');\n    const headers = {\n      'Content-Type': 'application/json'\n    };\n    if (token) {\n      headers['Authorization'] = `Bearer ${token}`;\n    }\n    fetch(\"http://localhost:8081/api/categories\", {\n      method: 'GET',\n      headers: headers\n    }).then(res => res.ok ? res.json() : []).then(data => setCategories(Array.isArray(data) ? data : [])).catch(err => {\n      console.error(\"Erreur lors du chargement des catégories:\", err);\n      setCategories([]);\n    });\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value,\n      type,\n      checked\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setSubmitting(true);\n    const dataToSend = {\n      ...formData,\n      fournisseur_id: formData.fournisseur_id || null\n    };\n    try {\n      const token = localStorage.getItem('authToken');\n      const headers = {\n        'Content-Type': 'application/json'\n      };\n      if (token) {\n        headers['Authorization'] = `Bearer ${token}`;\n      }\n      const url = editingId ? `http://localhost:8081/api/equipements/${editingId}` : \"http://localhost:8081/api/equipements\";\n      const method = editingId ? 'PUT' : 'POST';\n      const response = await fetch(url, {\n        method: method,\n        headers: headers,\n        body: JSON.stringify(dataToSend)\n      });\n      if (!response.ok) {\n        throw new Error(`Erreur HTTP: ${response.status}`);\n      }\n      const result = await response.json();\n      if (editingId) {\n        setEquipements(prev => prev.map(eq => eq.id === editingId ? result : eq));\n        setFilteredEquipements(prev => prev.map(eq => eq.id === editingId ? result : eq));\n      } else {\n        setEquipements(prev => [...prev, result]);\n        setFilteredEquipements(prev => [...prev, result]);\n      }\n      setFormData({\n        nom: '',\n        type: '',\n        numero_serie: '',\n        statut: 'DISPONIBLE',\n        date_achat: '',\n        fournisseur_id: '',\n        date_debut_garantie: '',\n        date_fin_garantie: '',\n        en_stock: true,\n        stock_actuel: 1,\n        stock_max: 1,\n        stock_min: 1,\n        categorie_id: ''\n      });\n      setShowModal(false);\n      setEditingId(null);\n      alert(editingId ? 'Équipement modifié avec succès !' : 'Équipement ajouté avec succès !');\n    } catch (err) {\n      console.error(\"Erreur lors de l'opération:\", err);\n      alert(`Erreur: ${err.message}`);\n    } finally {\n      setSubmitting(false);\n    }\n  };\n  const handleEdit = equipement => {\n    var _equipement$fournisse2;\n    setEditingId(equipement.id);\n    setFormData({\n      nom: equipement.nom,\n      type: equipement.type,\n      numero_serie: equipement.numero_serie || equipement.numeroSerie || '',\n      statut: equipement.statut,\n      date_achat: equipement.date_achat || '',\n      fournisseur_id: ((_equipement$fournisse2 = equipement.fournisseur) === null || _equipement$fournisse2 === void 0 ? void 0 : _equipement$fournisse2.id) || equipement.fournisseur_id || '',\n      date_debut_garantie: equipement.date_debut_garantie || '',\n      date_fin_garantie: equipement.date_fin_garantie || '',\n      en_stock: equipement.en_stock !== undefined ? equipement.en_stock : true,\n      stock_actuel: equipement.stock_actuel || 1,\n      stock_max: equipement.stock_max || 1,\n      stock_min: equipement.stock_min || 1,\n      categorie_id: equipement.categorie_id || ''\n    });\n    setShowModal(true);\n  };\n  const handleDelete = async id => {\n    if (!window.confirm(\"Êtes-vous sûr de vouloir supprimer cet équipement ?\")) {\n      return;\n    }\n    try {\n      const token = localStorage.getItem('authToken');\n      const headers = {\n        'Content-Type': 'application/json'\n      };\n      if (token) {\n        headers['Authorization'] = `Bearer ${token}`;\n      }\n      const response = await fetch(`http://localhost:8081/api/equipements/${id}`, {\n        method: 'DELETE',\n        headers: headers\n      });\n      if (!response.ok) {\n        throw new Error(`Erreur HTTP: ${response.status}`);\n      }\n      setEquipements(prev => prev.filter(eq => eq.id !== id));\n      setFilteredEquipements(prev => prev.filter(eq => eq.id !== id));\n      alert('Équipement supprimé avec succès !');\n    } catch (err) {\n      console.error(\"Erreur lors de la suppression:\", err);\n      alert(`Erreur lors de la suppression: ${err.message}`);\n    }\n  };\n  const handleSearchChange = e => {\n    setSearchTerm(e.target.value);\n  };\n  const formatDate = dateInput => {\n    if (!dateInput) return 'N/A';\n    try {\n      // Si c'est une string au format ISO (YYYY-MM-DD)\n      if (typeof dateInput === 'string' && dateInput.match(/^\\d{4}-\\d{2}-\\d{2}$/)) {\n        const [year, month, day] = dateInput.split('-');\n        return `${day}/${month}/${year}`;\n      }\n\n      // Si c'est un objet Date ou timestamp\n      const dateObj = new Date(dateInput);\n      if (!isNaN(dateObj.getTime())) {\n        return dateObj.toLocaleDateString('fr-FR');\n      }\n      return dateInput; // Retourne la valeur originale si on ne sait pas la formater\n    } catch (e) {\n      console.error(\"Erreur de formatage de date\", e);\n      return 'N/A';\n    }\n  };\n  const handleExportExcel = () => {\n    if (!Array.isArray(filteredEquipements) || filteredEquipements.length === 0) {\n      alert(\"Aucune donnée à exporter\");\n      return;\n    }\n    const columns = [\"Nom\", \"Type\", \"Numéro de série\", \"Statut\", \"Date d'achat\", \"Date fin garantie\", \"Fournisseur\", \"Actions\"];\n    const rows = filteredEquipements.map(eq => {\n      var _eq$fournisseur;\n      return [eq.nom || \"\", eq.type || \"\", eq.numero_serie || \"\", eq.statut || \"\", formatDate(eq.date_achat) || \"\", formatDate(eq.date_fin_garantie) || \"\", ((_eq$fournisseur = eq.fournisseur) === null || _eq$fournisseur === void 0 ? void 0 : _eq$fournisseur.nom) || \"\", \"\"];\n    });\n    const worksheet = XLSX.utils.aoa_to_sheet([columns, ...rows]);\n    const workbook = XLSX.utils.book_new();\n    XLSX.utils.book_append_sheet(workbook, worksheet, \"Equipements\");\n    XLSX.writeFile(workbook, \"equipements.xlsx\");\n  };\n  const handleExportPDF = () => {\n    if (!Array.isArray(filteredEquipements) || filteredEquipements.length === 0) {\n      alert(\"Aucune donnée à exporter\");\n      return;\n    }\n    const doc = new jsPDF();\n    doc.text(\"Liste des équipements\", 14, 16);\n    const columns = [\"Nom\", \"Type\", \"Numéro de série\", \"Statut\", \"Date d'achat\", \"Fournisseur\"];\n    const rows = filteredEquipements.map(eq => {\n      var _eq$fournisseur2;\n      return [eq.nom || \"\", eq.type || \"\", eq.numero_serie || \"\", eq.statut || \"\", formatDate(eq.date_achat) || \"\", ((_eq$fournisseur2 = eq.fournisseur) === null || _eq$fournisseur2 === void 0 ? void 0 : _eq$fournisseur2.nom) || \"\"];\n    });\n    doc.autoTable({\n      head: [columns],\n      body: rows,\n      startY: 22,\n      styles: {\n        fontSize: 10\n      }\n    });\n    doc.save(\"equipements.pdf\");\n  };\n  const handlePrint = () => {\n    window.print();\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"equipements-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sidebar\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"logo-section\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: logo,\n          alt: \"Logo Entreprise\",\n          className: \"company-logo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 385,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"main-menu\",\n        children: /*#__PURE__*/_jsxDEV(\"ul\", {\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/\",\n              className: location.pathname === '/' ? 'home-active active' : '',\n              children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: faLaptop,\n                style: {\n                  marginRight: 8\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 392,\n                columnNumber: 17\n              }, this), \" Accueil\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/equipements\",\n              className: location.pathname === '/equipements' ? 'active' : '',\n              children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: faLaptop,\n                style: {\n                  marginRight: 8\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 397,\n                columnNumber: 17\n              }, this), \" G\\xE9rer les \\xE9quipements\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 395,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/employes\",\n              className: location.pathname === '/employes' ? 'active' : '',\n              children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: faUsers,\n                style: {\n                  marginRight: 8\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 402,\n                columnNumber: 17\n              }, this), \" G\\xE9rer les employ\\xE9s\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/affectations\",\n              className: location.pathname === '/affectations' ? 'active' : '',\n              children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: faExchangeAlt,\n                style: {\n                  marginRight: 8\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 407,\n                columnNumber: 17\n              }, this), \" Suivi des affectations\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 406,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 405,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/fournisseurs\",\n              className: location.pathname === '/fournisseurs' ? 'active' : '',\n              children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: faTruck,\n                style: {\n                  marginRight: 8\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 412,\n                columnNumber: 17\n              }, this), \" G\\xE9rer les fournisseurs\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 410,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 389,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 388,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"secondary-links\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/profil\",\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faUserCog,\n            style: {\n              marginRight: 8\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 13\n          }, this), \" Mon profil & param\\xE8tres\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 417,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: 'auto',\n          padding: '20px 0 0 0',\n          textAlign: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"logout-btn\",\n          onClick: () => {\n            localStorage.removeItem('authToken');\n            window.location.href = '/';\n          },\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faSignOutAlt,\n            style: {\n              marginRight: 8\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 13\n          }, this), \" D\\xE9connexion\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 423,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 422,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 384,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"equipements-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-header-row\",\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between',\n          marginBottom: 32\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-bar\",\n          style: {\n            flex: 1,\n            maxWidth: 480,\n            marginRight: 24,\n            position: 'relative'\n          },\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faSearch,\n            style: {\n              position: 'absolute',\n              left: 14,\n              top: '50%',\n              transform: 'translateY(-50%)',\n              color: '#1976d2',\n              fontSize: '1.1rem',\n              opacity: 0.8\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 440,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Recherche par mot cl\\xE9\",\n            value: searchTerm,\n            onChange: handleSearchChange,\n            style: {\n              width: '100%',\n              padding: '10px 16px 10px 38px',\n              borderRadius: 8,\n              border: '1px solid #dbeafe',\n              fontSize: '1rem',\n              boxShadow: '0 2px 8px rgba(21,101,192,0.06)'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 439,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"profile-block\",\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 20\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"notification-icon\",\n            style: {\n              position: 'relative',\n              marginRight: 8,\n              cursor: 'pointer'\n            },\n            children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n              icon: faBell,\n              style: {\n                fontSize: '1.3rem',\n                color: '#1976d2'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 451,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                position: 'absolute',\n                top: -6,\n                right: -6,\n                background: '#e74a3b',\n                color: '#fff',\n                borderRadius: '50%',\n                fontSize: '0.7rem',\n                padding: '2px 6px',\n                fontWeight: 600\n              },\n              children: \"3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 452,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              fontWeight: 500,\n              color: '#2e3a4e',\n              fontSize: '1rem',\n              marginRight: 8\n            },\n            children: \"Responsable IT\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 454,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faUserCog,\n            style: {\n              fontSize: '1.5rem',\n              color: '#1976d2',\n              background: '#fff',\n              borderRadius: '50%',\n              padding: 6,\n              boxShadow: '0 2px 8px rgba(21,101,192,0.10)'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 455,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 449,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 438,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"equipements-title\",\n        children: [\"Liste des \\xE9quipements (\", filteredEquipements.length, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 459,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: 16,\n          marginBottom: 24\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          style: {\n            fontWeight: 700,\n            fontSize: '1rem',\n            borderRadius: 10,\n            background: 'linear-gradient(90deg, #1976d2 0%, #42a5f5 100%)',\n            color: '#fff',\n            boxShadow: '0 4px 16px rgba(21,101,192,0.18)'\n          },\n          onClick: () => {\n            setEditingId(null);\n            setShowModal(true);\n          },\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faPlus,\n            style: {\n              marginRight: 8\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 472,\n            columnNumber: 13\n          }, this), \"Ajouter un \\xE9quipement\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 464,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          style: {\n            fontWeight: 700,\n            fontSize: '1rem',\n            borderRadius: 10,\n            background: 'linear-gradient(90deg, #43a047 0%, #66bb6a 100%)',\n            color: '#fff',\n            boxShadow: '0 4px 16px rgba(67,160,71,0.18)'\n          },\n          onClick: handleExportExcel,\n          children: \"Exporter Excel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 475,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          style: {\n            fontWeight: 700,\n            fontSize: '1rem',\n            borderRadius: 10,\n            background: 'linear-gradient(90deg, #d32f2f 0%, #e57373 100%)',\n            color: '#fff',\n            boxShadow: '0 4px 16px rgba(211,47,47,0.18)'\n          },\n          onClick: handleExportPDF,\n          children: \"Exporter PDF\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 478,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          style: {\n            fontWeight: 700,\n            fontSize: '1rem',\n            borderRadius: 10,\n            background: 'linear-gradient(90deg, #ffa000 0%, #ffd54f 100%)',\n            color: '#fff',\n            boxShadow: '0 4px 16px rgba(255,160,0,0.18)'\n          },\n          onClick: handlePrint,\n          children: \"Imprimer\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 481,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 463,\n        columnNumber: 9\n      }, this), showModal && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          backgroundColor: 'rgba(0, 0, 0, 0.5)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 1000\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            backgroundColor: 'white',\n            borderRadius: 12,\n            padding: 32,\n            maxWidth: 600,\n            width: '90%',\n            maxHeight: '90vh',\n            overflowY: 'auto',\n            boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              marginBottom: 24\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                margin: 0,\n                color: '#1976d2',\n                fontSize: '1.5rem',\n                fontWeight: 700\n              },\n              children: editingId ? 'Modifier un équipement' : 'Ajouter un nouvel équipement'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 511,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                setShowModal(false);\n                setEditingId(null);\n              },\n              style: {\n                background: 'none',\n                border: 'none',\n                fontSize: '1.5rem',\n                color: '#666',\n                cursor: 'pointer',\n                padding: 8,\n                borderRadius: '50%'\n              },\n              children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: faTimes\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 529,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 514,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 510,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSubmit,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: '1fr 1fr',\n                gap: 16,\n                marginBottom: 16\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: 8,\n                    fontWeight: 600,\n                    color: '#333'\n                  },\n                  children: \"Nom de l'\\xE9quipement *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 536,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"nom\",\n                  value: formData.nom,\n                  onChange: handleInputChange,\n                  required: true,\n                  style: {\n                    width: '100%',\n                    padding: '12px',\n                    border: '2px solid #e0e0e0',\n                    borderRadius: 8,\n                    fontSize: '1rem',\n                    transition: 'border-color 0.2s'\n                  },\n                  onFocus: e => e.target.style.borderColor = '#1976d2',\n                  onBlur: e => e.target.style.borderColor = '#e0e0e0'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 539,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 535,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: 8,\n                    fontWeight: 600,\n                    color: '#333'\n                  },\n                  children: \"Type *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 559,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"type\",\n                  value: formData.type,\n                  onChange: handleInputChange,\n                  required: true,\n                  style: {\n                    width: '100%',\n                    padding: '12px',\n                    border: '2px solid #e0e0e0',\n                    borderRadius: 8,\n                    fontSize: '1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"S\\xE9lectionner un type\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 575,\n                    columnNumber: 23\n                  }, this), typeOptions.map(type => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: type,\n                    children: type\n                  }, type, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 577,\n                    columnNumber: 25\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 562,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 558,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 534,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: '1fr 1fr',\n                gap: 16,\n                marginBottom: 16\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: 8,\n                    fontWeight: 600,\n                    color: '#333'\n                  },\n                  children: \"Num\\xE9ro de s\\xE9rie\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 585,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"numero_serie\",\n                  value: formData.numero_serie,\n                  onChange: handleInputChange,\n                  style: {\n                    width: '100%',\n                    padding: '12px',\n                    border: '2px solid #e0e0e0',\n                    borderRadius: 8,\n                    fontSize: '1rem'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 588,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 584,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: 8,\n                    fontWeight: 600,\n                    color: '#333'\n                  },\n                  children: \"Statut *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 604,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"statut\",\n                  value: formData.statut,\n                  onChange: handleInputChange,\n                  required: true,\n                  style: {\n                    width: '100%',\n                    padding: '12px',\n                    border: '2px solid #e0e0e0',\n                    borderRadius: 8,\n                    fontSize: '1rem'\n                  },\n                  children: statutOptions.map(statut => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: statut,\n                    children: statut\n                  }, statut, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 621,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 607,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 603,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 583,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: '1fr 1fr',\n                gap: 16,\n                marginBottom: 16\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: 8,\n                    fontWeight: 600,\n                    color: '#333'\n                  },\n                  children: \"Date d'achat\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 629,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  name: \"date_achat\",\n                  value: formData.date_achat,\n                  onChange: handleInputChange,\n                  style: {\n                    width: '100%',\n                    padding: '12px',\n                    border: '2px solid #e0e0e0',\n                    borderRadius: 8,\n                    fontSize: '1rem'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 632,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 628,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: 8,\n                    fontWeight: 600,\n                    color: '#333'\n                  },\n                  children: \"Fournisseur\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 648,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"fournisseur_id\",\n                  value: formData.fournisseur_id,\n                  onChange: handleInputChange,\n                  style: {\n                    width: '100%',\n                    padding: '12px',\n                    border: '2px solid #e0e0e0',\n                    borderRadius: 8,\n                    fontSize: '1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"S\\xE9lectionner un fournisseur\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 663,\n                    columnNumber: 23\n                  }, this), fournisseurs.map(fournisseur => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: fournisseur.id,\n                    children: fournisseur.nom\n                  }, fournisseur.id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 665,\n                    columnNumber: 25\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 651,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 647,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 627,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: '1fr 1fr',\n                gap: 16,\n                marginBottom: 16\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: 8,\n                    fontWeight: 600,\n                    color: '#333'\n                  },\n                  children: \"D\\xE9but de garantie\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 675,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  name: \"date_debut_garantie\",\n                  value: formData.date_debut_garantie,\n                  onChange: handleInputChange,\n                  style: {\n                    width: '100%',\n                    padding: '12px',\n                    border: '2px solid #e0e0e0',\n                    borderRadius: 8,\n                    fontSize: '1rem'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 678,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 674,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: 8,\n                    fontWeight: 600,\n                    color: '#333'\n                  },\n                  children: \"Fin de garantie\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 694,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  name: \"date_fin_garantie\",\n                  value: formData.date_fin_garantie,\n                  onChange: handleInputChange,\n                  style: {\n                    width: '100%',\n                    padding: '12px',\n                    border: '2px solid #e0e0e0',\n                    borderRadius: 8,\n                    fontSize: '1rem'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 697,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 693,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 673,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: '1fr 1fr 1fr',\n                gap: 16,\n                marginBottom: 16\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: 8,\n                    fontWeight: 600,\n                    color: '#333'\n                  },\n                  children: \"Stock actuel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 715,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  name: \"stock_actuel\",\n                  value: formData.stock_actuel,\n                  onChange: handleInputChange,\n                  min: \"0\",\n                  style: {\n                    width: '100%',\n                    padding: '12px',\n                    border: '2px solid #e0e0e0',\n                    borderRadius: 8,\n                    fontSize: '1rem'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 718,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 714,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: 8,\n                    fontWeight: 600,\n                    color: '#333'\n                  },\n                  children: \"Stock maximum\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 735,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  name: \"stock_max\",\n                  value: formData.stock_max,\n                  onChange: handleInputChange,\n                  min: \"1\",\n                  style: {\n                    width: '100%',\n                    padding: '12px',\n                    border: '2px solid #e0e0e0',\n                    borderRadius: 8,\n                    fontSize: '1rem'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 738,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 734,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: 8,\n                    fontWeight: 600,\n                    color: '#333'\n                  },\n                  children: \"Stock minimum\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 755,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  name: \"stock_min\",\n                  value: formData.stock_min,\n                  onChange: handleInputChange,\n                  min: \"0\",\n                  style: {\n                    width: '100%',\n                    padding: '12px',\n                    border: '2px solid #e0e0e0',\n                    borderRadius: 8,\n                    fontSize: '1rem'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 758,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 754,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 713,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: 24\n              },\n              children: /*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  fontWeight: 600,\n                  color: '#333',\n                  cursor: 'pointer'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  name: \"en_stock\",\n                  checked: formData.en_stock,\n                  onChange: handleInputChange,\n                  style: {\n                    marginRight: 8,\n                    transform: 'scale(1.2)'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 777,\n                  columnNumber: 21\n                }, this), \"\\xC9quipement en stock\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 776,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 775,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                gap: 12,\n                justifyContent: 'flex-end'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => {\n                  setShowModal(false);\n                  setEditingId(null);\n                },\n                style: {\n                  padding: '12px 24px',\n                  border: '2px solid #e0e0e0',\n                  borderRadius: 8,\n                  background: 'white',\n                  color: '#666',\n                  fontSize: '1rem',\n                  fontWeight: 600,\n                  cursor: 'pointer'\n                },\n                children: \"Annuler\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 789,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                disabled: submitting,\n                style: {\n                  padding: '12px 24px',\n                  border: 'none',\n                  borderRadius: 8,\n                  background: submitting ? '#ccc' : 'linear-gradient(90deg, #1976d2 0%, #42a5f5 100%)',\n                  color: 'white',\n                  fontSize: '1rem',\n                  fontWeight: 600,\n                  cursor: submitting ? 'not-allowed' : 'pointer',\n                  boxShadow: '0 4px 16px rgba(21,101,192,0.18)'\n                },\n                children: submitting ? editingId ? 'Modification en cours...' : 'Ajout en cours...' : editingId ? 'Modifier l\\'équipement' : 'Ajouter l\\'équipement'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 808,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 788,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 533,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 500,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 488,\n        columnNumber: 11\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '40px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Chargement des \\xE9quipements...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 836,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 835,\n        columnNumber: 11\n      }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '40px',\n          color: '#e74a3b'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"Erreur lors du chargement : \", error]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 840,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.location.reload(),\n          style: {\n            marginTop: '10px',\n            padding: '8px 16px',\n            background: '#1976d2',\n            color: 'white',\n            border: 'none',\n            borderRadius: '4px',\n            cursor: 'pointer'\n          },\n          children: \"Actualiser la page\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 841,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 839,\n        columnNumber: 11\n      }, this) : !Array.isArray(filteredEquipements) || filteredEquipements.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '40px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: searchTerm ? 'Aucun résultat trouvé' : 'Aucun équipement trouvé'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 850,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 849,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"table\", {\n        className: \"equipements-table\",\n        children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Nom\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 856,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Type\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 857,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Num\\xE9ro de s\\xE9rie\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 858,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Statut\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 859,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Date d'achat\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 860,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Date fin garantie\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 861,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Fournisseur\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 862,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Actions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 863,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 855,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 854,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n          children: filteredEquipements.map((eq, index) => {\n            var _eq$fournisseur3;\n            const numeroSerie = eq.numero_serie || eq.numeroSerie || 'N/A';\n            const dateAchat = formatDate(eq.date_achat);\n            const dateFinGarantie = formatDate(eq.date_fin_garantie);\n            const fournisseur = ((_eq$fournisseur3 = eq.fournisseur) === null || _eq$fournisseur3 === void 0 ? void 0 : _eq$fournisseur3.nom) || eq.fournisseur_id || 'Non spécifié';\n            return /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                children: eq.nom || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 875,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: eq.type || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 876,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: numeroSerie\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 877,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: eq.statut || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 878,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: dateAchat\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 879,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: dateFinGarantie\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 880,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: fournisseur\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 881,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    gap: '8px'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleEdit(eq),\n                    style: {\n                      background: '#1976d2',\n                      color: 'white',\n                      border: 'none',\n                      borderRadius: '4px',\n                      padding: '6px 12px',\n                      cursor: 'pointer',\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '4px'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                      icon: faEdit\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 898,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Modifier\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 899,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 884,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleDelete(eq.id),\n                    style: {\n                      background: '#e74a3b',\n                      color: 'white',\n                      border: 'none',\n                      borderRadius: '4px',\n                      padding: '6px 12px',\n                      cursor: 'pointer',\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '4px'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                      icon: faTrash\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 915,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Supprimer\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 916,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 901,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 883,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 882,\n                columnNumber: 21\n              }, this)]\n            }, eq.id || index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 874,\n              columnNumber: 19\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 866,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 853,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 436,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 382,\n    columnNumber: 5\n  }, this);\n}\n_s(Equipements, \"IXCZP/kYjKeSixnhzfzhA9k6w4o=\", false, function () {\n  return [useLocation];\n});\n_c = Equipements;\nvar _c;\n$RefreshReg$(_c, \"Equipements\");", "map": {"version": 3, "names": ["Link", "useLocation", "logo", "FontAwesomeIcon", "faLaptop", "faUsers", "faExchangeAlt", "faTruck", "faUserCog", "faSignOutAlt", "faSearch", "faBell", "faTimes", "faPlus", "faEdit", "faTrash", "useEffect", "useState", "jsPDF", "XLSX", "jsxDEV", "_jsxDEV", "Equipements", "_s", "location", "equipements", "setEquipements", "filteredEquipements", "setFilteredEquipements", "loading", "setLoading", "error", "setError", "showModal", "setShowModal", "fournisseurs", "setFournisseurs", "categories", "setCategories", "submitting", "setSubmitting", "searchTerm", "setSearchTerm", "editingId", "setEditingId", "formData", "setFormData", "nom", "type", "numero_serie", "statut", "date_achat", "fournisseur_id", "date_debut_garantie", "date_fin_garantie", "en_stock", "stock_actuel", "stock_max", "stock_min", "categorie_id", "statutOptions", "typeOptions", "loadEquipements", "loadFournisseurs", "loadCategories", "filtered", "filter", "equipement", "_equipement$fournisse", "toLowerCase", "includes", "<PERSON><PERSON><PERSON><PERSON>", "console", "log", "token", "localStorage", "getItem", "headers", "fetch", "method", "then", "res", "status", "ok", "Error", "json", "data", "Array", "isArray", "warn", "catch", "err", "message", "handleInputChange", "e", "name", "value", "checked", "target", "prev", "handleSubmit", "preventDefault", "dataToSend", "url", "response", "body", "JSON", "stringify", "result", "map", "eq", "id", "alert", "handleEdit", "_equipement$fournisse2", "numeroSerie", "undefined", "handleDelete", "window", "confirm", "handleSearchChange", "formatDate", "dateInput", "match", "year", "month", "day", "split", "date<PERSON><PERSON>j", "Date", "isNaN", "getTime", "toLocaleDateString", "handleExportExcel", "length", "columns", "rows", "_eq$fournisseur", "worksheet", "utils", "aoa_to_sheet", "workbook", "book_new", "book_append_sheet", "writeFile", "handleExportPDF", "doc", "text", "_eq$fournisseur2", "autoTable", "head", "startY", "styles", "fontSize", "save", "handlePrint", "print", "className", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "pathname", "icon", "style", "marginRight", "marginTop", "padding", "textAlign", "onClick", "removeItem", "href", "display", "alignItems", "justifyContent", "marginBottom", "flex", "max<PERSON><PERSON><PERSON>", "position", "left", "top", "transform", "color", "opacity", "placeholder", "onChange", "width", "borderRadius", "border", "boxShadow", "gap", "cursor", "right", "background", "fontWeight", "bottom", "backgroundColor", "zIndex", "maxHeight", "overflowY", "margin", "onSubmit", "gridTemplateColumns", "required", "transition", "onFocus", "borderColor", "onBlur", "min", "disabled", "reload", "index", "_eq$fournisseur3", "dateAchat", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/gestion-equipement-frontend/src/pages/Equipements.js"], "sourcesContent": ["import './Equipements.css';\nimport { Link, useLocation } from 'react-router-dom';\nimport logo from '../assets/images/asment logo.jpg';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { faLaptop, faUsers, faExchangeAlt, faTruck, faUserCog, faSignOutAlt, faSearch, faBell, faTimes, faPlus, faEdit, faTrash } from '@fortawesome/free-solid-svg-icons';\nimport { useEffect, useState } from \"react\";\nimport jsPDF from \"jspdf\";\nimport \"jspdf-autotable\";\nimport * as XLSX from \"xlsx\";\n\nexport default function Equipements() {\n  const location = useLocation();\n  const [equipements, setEquipements] = useState([]);\n  const [filteredEquipements, setFilteredEquipements] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [showModal, setShowModal] = useState(false);\n  const [fournisseurs, setFournisseurs] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [submitting, setSubmitting] = useState(false);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [editingId, setEditingId] = useState(null);\n\n  // État du formulaire\n  const [formData, setFormData] = useState({\n    nom: '',\n    type: '',\n    numero_serie: '',\n    statut: 'DISPONIBLE',\n    date_achat: '',\n    fournisseur_id: '',\n    date_debut_garantie: '',\n    date_fin_garantie: '',\n    en_stock: true,\n    stock_actuel: 1,\n    stock_max: 1,\n    stock_min: 1,\n    categorie_id: ''\n  });\n\n  // Options pour les statuts et types\n  const statutOptions = ['DISPONIBLE', 'EN_MAINTENANCE', 'OCCUPE', 'HORS_SERVICE'];\n  const typeOptions = ['ORDINATEUR', 'IMPRIMANTE', 'SCANNER', 'TELEPHONE', 'TABLETTE', 'SERVEUR', 'AUTRE'];\n\n  useEffect(() => {\n    loadEquipements();\n    loadFournisseurs();\n    loadCategories();\n  }, []);\n\n  useEffect(() => {\n    if (searchTerm === '') {\n      setFilteredEquipements(equipements);\n    } else {\n      const filtered = equipements.filter(equipement =>\n        equipement.nom.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        equipement.type.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        (equipement.numero_serie && equipement.numero_serie.toLowerCase().includes(searchTerm.toLowerCase())) ||\n        equipement.statut.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        (equipement.fournisseur?.nom && equipement.fournisseur.nom.toLowerCase().includes(searchTerm.toLowerCase()))\n      );\n      setFilteredEquipements(filtered);\n    }\n  }, [searchTerm, equipements]);\n\n  const loadEquipements = () => {\n    console.log(\"Chargement des équipements...\");\n    \n    const token = localStorage.getItem('authToken');\n    const headers = {\n      'Content-Type': 'application/json',\n    };\n    if (token) {\n      headers['Authorization'] = `Bearer ${token}`;\n    }\n\n    fetch(\"http://localhost:8081/api/equipements\", {\n      method: 'GET',\n      headers: headers\n    })\n      .then(res => {\n        console.log(\"Status de la réponse équipements:\", res.status);\n        if (!res.ok) {\n          throw new Error(`Erreur HTTP: ${res.status}`);\n        }\n        return res.json();\n      })\n      .then(data => {\n        console.log(\"Données équipements reçues:\", data);\n        if (Array.isArray(data)) {\n          setEquipements(data);\n          setFilteredEquipements(data);\n          setError(null);\n        } else {\n          console.warn(\"Les données équipements ne sont pas un tableau:\", data);\n          setEquipements([]);\n          setFilteredEquipements([]);\n          setError(\"Format de données incorrect\");\n        }\n        setLoading(false);\n      })\n      .catch(err => {\n        console.error(\"Erreur lors du chargement des équipements:\", err);\n        setEquipements([]);\n        setFilteredEquipements([]);\n        setError(err.message || \"Erreur de chargement\");\n        setLoading(false);\n      });\n  };\n\n  const loadFournisseurs = () => {\n    const token = localStorage.getItem('authToken');\n    const headers = {\n      'Content-Type': 'application/json',\n    };\n    if (token) {\n      headers['Authorization'] = `Bearer ${token}`;\n    }\n\n    fetch(\"http://localhost:8081/api/fournisseurs\", {\n      method: 'GET',\n      headers: headers\n    })\n      .then(res => res.ok ? res.json() : [])\n      .then(data => setFournisseurs(Array.isArray(data) ? data : []))\n      .catch(err => {\n        console.error(\"Erreur lors du chargement des fournisseurs:\", err);\n        setFournisseurs([]);\n      });\n  };\n\n  const loadCategories = () => {\n    const token = localStorage.getItem('authToken');\n    const headers = {\n      'Content-Type': 'application/json',\n    };\n    if (token) {\n      headers['Authorization'] = `Bearer ${token}`;\n    }\n\n    fetch(\"http://localhost:8081/api/categories\", {\n      method: 'GET',\n      headers: headers\n    })\n      .then(res => res.ok ? res.json() : [])\n      .then(data => setCategories(Array.isArray(data) ? data : []))\n      .catch(err => {\n        console.error(\"Erreur lors du chargement des catégories:\", err);\n        setCategories([]);\n      });\n  };\n\n  const handleInputChange = (e) => {\n    const { name, value, type, checked } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setSubmitting(true);\n\n    const dataToSend = {\n      ...formData,\n      fournisseur_id: formData.fournisseur_id || null\n    };\n\n    try {\n      const token = localStorage.getItem('authToken');\n      const headers = {\n        'Content-Type': 'application/json',\n      };\n      if (token) {\n        headers['Authorization'] = `Bearer ${token}`;\n      }\n\n      const url = editingId \n        ? `http://localhost:8081/api/equipements/${editingId}`\n        : \"http://localhost:8081/api/equipements\";\n      \n      const method = editingId ? 'PUT' : 'POST';\n\n      const response = await fetch(url, {\n        method: method,\n        headers: headers,\n        body: JSON.stringify(dataToSend)\n      });\n\n      if (!response.ok) {\n        throw new Error(`Erreur HTTP: ${response.status}`);\n      }\n\n      const result = await response.json();\n      \n      if (editingId) {\n        setEquipements(prev => prev.map(eq => eq.id === editingId ? result : eq));\n        setFilteredEquipements(prev => prev.map(eq => eq.id === editingId ? result : eq));\n      } else {\n        setEquipements(prev => [...prev, result]);\n        setFilteredEquipements(prev => [...prev, result]);\n      }\n      \n      setFormData({\n        nom: '',\n        type: '',\n        numero_serie: '',\n        statut: 'DISPONIBLE',\n        date_achat: '',\n        fournisseur_id: '',\n        date_debut_garantie: '',\n        date_fin_garantie: '',\n        en_stock: true,\n        stock_actuel: 1,\n        stock_max: 1,\n        stock_min: 1,\n        categorie_id: ''\n      });\n      setShowModal(false);\n      setEditingId(null);\n      \n      alert(editingId ? 'Équipement modifié avec succès !' : 'Équipement ajouté avec succès !');\n    } catch (err) {\n      console.error(\"Erreur lors de l'opération:\", err);\n      alert(`Erreur: ${err.message}`);\n    } finally {\n      setSubmitting(false);\n    }\n  };\n\n  const handleEdit = (equipement) => {\n    setEditingId(equipement.id);\n    setFormData({\n      nom: equipement.nom,\n      type: equipement.type,\n      numero_serie: equipement.numero_serie || equipement.numeroSerie || '',\n      statut: equipement.statut,\n      date_achat: equipement.date_achat || '',\n      fournisseur_id: equipement.fournisseur?.id || equipement.fournisseur_id || '',\n      date_debut_garantie: equipement.date_debut_garantie || '',\n      date_fin_garantie: equipement.date_fin_garantie || '',\n      en_stock: equipement.en_stock !== undefined ? equipement.en_stock : true,\n      stock_actuel: equipement.stock_actuel || 1,\n      stock_max: equipement.stock_max || 1,\n      stock_min: equipement.stock_min || 1,\n      categorie_id: equipement.categorie_id || ''\n    });\n    setShowModal(true);\n  };\n\n  const handleDelete = async (id) => {\n    if (!window.confirm(\"Êtes-vous sûr de vouloir supprimer cet équipement ?\")) {\n      return;\n    }\n\n    try {\n      const token = localStorage.getItem('authToken');\n      const headers = {\n        'Content-Type': 'application/json',\n      };\n      if (token) {\n        headers['Authorization'] = `Bearer ${token}`;\n      }\n\n      const response = await fetch(`http://localhost:8081/api/equipements/${id}`, {\n        method: 'DELETE',\n        headers: headers\n      });\n\n      if (!response.ok) {\n        throw new Error(`Erreur HTTP: ${response.status}`);\n      }\n\n      setEquipements(prev => prev.filter(eq => eq.id !== id));\n      setFilteredEquipements(prev => prev.filter(eq => eq.id !== id));\n      \n      alert('Équipement supprimé avec succès !');\n    } catch (err) {\n      console.error(\"Erreur lors de la suppression:\", err);\n      alert(`Erreur lors de la suppression: ${err.message}`);\n    }\n  };\n\n  const handleSearchChange = (e) => {\n    setSearchTerm(e.target.value);\n  };\n\n  const formatDate = (dateInput) => {\n    if (!dateInput) return 'N/A';\n    \n    try {\n      // Si c'est une string au format ISO (YYYY-MM-DD)\n      if (typeof dateInput === 'string' && dateInput.match(/^\\d{4}-\\d{2}-\\d{2}$/)) {\n        const [year, month, day] = dateInput.split('-');\n        return `${day}/${month}/${year}`;\n      }\n      \n      // Si c'est un objet Date ou timestamp\n      const dateObj = new Date(dateInput);\n      if (!isNaN(dateObj.getTime())) {\n        return dateObj.toLocaleDateString('fr-FR');\n      }\n      \n      return dateInput; // Retourne la valeur originale si on ne sait pas la formater\n    } catch (e) {\n      console.error(\"Erreur de formatage de date\", e);\n      return 'N/A';\n    }\n  };\n\n  const handleExportExcel = () => {\n    if (!Array.isArray(filteredEquipements) || filteredEquipements.length === 0) {\n      alert(\"Aucune donnée à exporter\");\n      return;\n    }\n    \n    const columns = [\n      \"Nom\",\n      \"Type\",\n      \"Numéro de série\",\n      \"Statut\",\n      \"Date d'achat\",\n      \"Date fin garantie\",\n      \"Fournisseur\",\n      \"Actions\"\n    ];\n    const rows = filteredEquipements.map(eq => [\n      eq.nom || \"\",\n      eq.type || \"\",\n      eq.numero_serie || \"\",\n      eq.statut || \"\",\n      formatDate(eq.date_achat) || \"\",\n      formatDate(eq.date_fin_garantie) || \"\",\n      eq.fournisseur?.nom || \"\",\n      \"\"\n    ]);\n    const worksheet = XLSX.utils.aoa_to_sheet([columns, ...rows]);\n    const workbook = XLSX.utils.book_new();\n    XLSX.utils.book_append_sheet(workbook, worksheet, \"Equipements\");\n    XLSX.writeFile(workbook, \"equipements.xlsx\");\n  };\n\n  const handleExportPDF = () => {\n    if (!Array.isArray(filteredEquipements) || filteredEquipements.length === 0) {\n      alert(\"Aucune donnée à exporter\");\n      return;\n    }\n    \n    const doc = new jsPDF();\n    doc.text(\"Liste des équipements\", 14, 16);\n    const columns = [\n      \"Nom\",\n      \"Type\",\n      \"Numéro de série\",\n      \"Statut\",\n      \"Date d'achat\",\n      \"Fournisseur\"\n    ];\n    const rows = filteredEquipements.map(eq => [\n      eq.nom || \"\",\n      eq.type || \"\",\n      eq.numero_serie || \"\",\n      eq.statut || \"\",\n      formatDate(eq.date_achat) || \"\",\n      eq.fournisseur?.nom || \"\"\n    ]);\n    doc.autoTable({\n      head: [columns],\n      body: rows,\n      startY: 22,\n      styles: { fontSize: 10 }\n    });\n    doc.save(\"equipements.pdf\");\n  };\n\n  const handlePrint = () => {\n    window.print();\n  };\n\n  return (\n    <div className=\"equipements-container\">\n      {/* Sidebar */}\n      <div className=\"sidebar\">\n        <div className=\"logo-section\">\n          <img src={logo} alt=\"Logo Entreprise\" className=\"company-logo\" />\n        </div>\n        <nav className=\"main-menu\">\n          <ul>\n            <li>\n              <Link to=\"/\" className={location.pathname === '/' ? 'home-active active' : ''}>\n                <FontAwesomeIcon icon={faLaptop} style={{marginRight:8}} /> Accueil\n              </Link>\n            </li>\n            <li>\n              <Link to=\"/equipements\" className={location.pathname === '/equipements' ? 'active' : ''}>\n                <FontAwesomeIcon icon={faLaptop} style={{marginRight:8}} /> Gérer les équipements\n              </Link>\n            </li>\n            <li>\n              <Link to=\"/employes\" className={location.pathname === '/employes' ? 'active' : ''}>\n                <FontAwesomeIcon icon={faUsers} style={{marginRight:8}} /> Gérer les employés\n              </Link>\n            </li>\n            <li>\n              <Link to=\"/affectations\" className={location.pathname === '/affectations' ? 'active' : ''}>\n                <FontAwesomeIcon icon={faExchangeAlt} style={{marginRight:8}} /> Suivi des affectations\n              </Link>\n            </li>\n            <li>\n              <Link to=\"/fournisseurs\" className={location.pathname === '/fournisseurs' ? 'active' : ''}>\n                <FontAwesomeIcon icon={faTruck} style={{marginRight:8}} /> Gérer les fournisseurs\n              </Link>\n            </li>\n          </ul>\n        </nav>\n        <div className=\"secondary-links\">\n          <Link to=\"/profil\">\n            <FontAwesomeIcon icon={faUserCog} style={{marginRight:8}} /> Mon profil & paramètres\n          </Link>\n        </div>\n        <div style={{ marginTop: 'auto', padding: '20px 0 0 0', textAlign: 'center' }}>\n          <button\n            className=\"logout-btn\"\n            onClick={() => {\n              localStorage.removeItem('authToken');\n              window.location.href = '/';\n            }}\n          >\n            <FontAwesomeIcon icon={faSignOutAlt} style={{marginRight:8}} /> Déconnexion\n          </button>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"equipements-content\">\n        {/* Dashboard header row */}\n        <div className=\"dashboard-header-row\" style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: 32 }}>\n          <div className=\"search-bar\" style={{ flex: 1, maxWidth: 480, marginRight: 24, position: 'relative' }}>\n            <FontAwesomeIcon icon={faSearch} style={{ position: 'absolute', left: 14, top: '50%', transform: 'translateY(-50%)', color: '#1976d2', fontSize: '1.1rem', opacity: 0.8 }} />\n            <input\n              type=\"text\"\n              placeholder=\"Recherche par mot clé\"\n              value={searchTerm}\n              onChange={handleSearchChange}\n              style={{ width: '100%', padding: '10px 16px 10px 38px', borderRadius: 8, border: '1px solid #dbeafe', fontSize: '1rem', boxShadow: '0 2px 8px rgba(21,101,192,0.06)' }}\n            />\n          </div>\n          <div className=\"profile-block\" style={{ display: 'flex', alignItems: 'center', gap: 20 }}>\n            <div className=\"notification-icon\" style={{ position: 'relative', marginRight: 8, cursor: 'pointer' }}>\n              <FontAwesomeIcon icon={faBell} style={{ fontSize: '1.3rem', color: '#1976d2' }} />\n              <span style={{ position: 'absolute', top: -6, right: -6, background: '#e74a3b', color: '#fff', borderRadius: '50%', fontSize: '0.7rem', padding: '2px 6px', fontWeight: 600 }}>3</span>\n            </div>\n            <span style={{ fontWeight: 500, color: '#2e3a4e', fontSize: '1rem', marginRight: 8 }}>Responsable IT</span>\n            <FontAwesomeIcon icon={faUserCog} style={{ fontSize: '1.5rem', color: '#1976d2', background: '#fff', borderRadius: '50%', padding: 6, boxShadow: '0 2px 8px rgba(21,101,192,0.10)' }} />\n          </div>\n        </div>\n\n        <h2 className=\"equipements-title\">\n          Liste des équipements ({filteredEquipements.length})\n        </h2>\n        \n        <div style={{ display: 'flex', gap: 16, marginBottom: 24 }}>\n          <button \n            className=\"btn btn-primary\" \n            style={{ fontWeight: 700, fontSize: '1rem', borderRadius: 10, background: 'linear-gradient(90deg, #1976d2 0%, #42a5f5 100%)', color: '#fff', boxShadow: '0 4px 16px rgba(21,101,192,0.18)' }}\n            onClick={() => {\n              setEditingId(null);\n              setShowModal(true);\n            }}\n          >\n            <FontAwesomeIcon icon={faPlus} style={{marginRight: 8}} />\n            Ajouter un équipement\n          </button>\n          <button className=\"btn btn-primary\" style={{ fontWeight: 700, fontSize: '1rem', borderRadius: 10, background: 'linear-gradient(90deg, #43a047 0%, #66bb6a 100%)', color: '#fff', boxShadow: '0 4px 16px rgba(67,160,71,0.18)' }} onClick={handleExportExcel}>\n            Exporter Excel\n          </button>\n          <button className=\"btn btn-primary\" style={{ fontWeight: 700, fontSize: '1rem', borderRadius: 10, background: 'linear-gradient(90deg, #d32f2f 0%, #e57373 100%)', color: '#fff', boxShadow: '0 4px 16px rgba(211,47,47,0.18)' }} onClick={handleExportPDF}>\n            Exporter PDF\n          </button>\n          <button className=\"btn btn-primary\" style={{ fontWeight: 700, fontSize: '1rem', borderRadius: 10, background: 'linear-gradient(90deg, #ffa000 0%, #ffd54f 100%)', color: '#fff', boxShadow: '0 4px 16px rgba(255,160,0,0.18)' }} onClick={handlePrint}>\n            Imprimer\n          </button>\n        </div>\n\n        {/* Modal d'ajout/modification d'équipement */}\n        {showModal && (\n          <div style={{\n            position: 'fixed',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            backgroundColor: 'rgba(0, 0, 0, 0.5)',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            zIndex: 1000\n          }}>\n            <div style={{\n              backgroundColor: 'white',\n              borderRadius: 12,\n              padding: 32,\n              maxWidth: 600,\n              width: '90%',\n              maxHeight: '90vh',\n              overflowY: 'auto',\n              boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)'\n            }}>\n              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>\n                <h3 style={{ margin: 0, color: '#1976d2', fontSize: '1.5rem', fontWeight: 700 }}>\n                  {editingId ? 'Modifier un équipement' : 'Ajouter un nouvel équipement'}\n                </h3>\n                <button\n                  onClick={() => {\n                    setShowModal(false);\n                    setEditingId(null);\n                  }}\n                  style={{\n                    background: 'none',\n                    border: 'none',\n                    fontSize: '1.5rem',\n                    color: '#666',\n                    cursor: 'pointer',\n                    padding: 8,\n                    borderRadius: '50%'\n                  }}\n                >\n                  <FontAwesomeIcon icon={faTimes} />\n                </button>\n              </div>\n\n              <form onSubmit={handleSubmit}>\n                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 16, marginBottom: 16 }}>\n                  <div>\n                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>\n                      Nom de l'équipement *\n                    </label>\n                    <input\n                      type=\"text\"\n                      name=\"nom\"\n                      value={formData.nom}\n                      onChange={handleInputChange}\n                      required\n                      style={{\n                        width: '100%',\n                        padding: '12px',\n                        border: '2px solid #e0e0e0',\n                        borderRadius: 8,\n                        fontSize: '1rem',\n                        transition: 'border-color 0.2s'\n                      }}\n                      onFocus={(e) => e.target.style.borderColor = '#1976d2'}\n                      onBlur={(e) => e.target.style.borderColor = '#e0e0e0'}\n                    />\n                  </div>\n\n                  <div>\n                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>\n                      Type *\n                    </label>\n                    <select\n                      name=\"type\"\n                      value={formData.type}\n                      onChange={handleInputChange}\n                      required\n                      style={{\n                        width: '100%',\n                        padding: '12px',\n                        border: '2px solid #e0e0e0',\n                        borderRadius: 8,\n                        fontSize: '1rem'\n                      }}\n                    >\n                      <option value=\"\">Sélectionner un type</option>\n                      {typeOptions.map(type => (\n                        <option key={type} value={type}>{type}</option>\n                      ))}\n                    </select>\n                  </div>\n                </div>\n\n                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 16, marginBottom: 16 }}>\n                  <div>\n                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>\n                      Numéro de série\n                    </label>\n                    <input\n                      type=\"text\"\n                      name=\"numero_serie\"\n                      value={formData.numero_serie}\n                      onChange={handleInputChange}\n                      style={{\n                        width: '100%',\n                        padding: '12px',\n                        border: '2px solid #e0e0e0',\n                        borderRadius: 8,\n                        fontSize: '1rem'\n                      }}\n                    />\n                  </div>\n\n                  <div>\n                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>\n                      Statut *\n                    </label>\n                    <select\n                      name=\"statut\"\n                      value={formData.statut}\n                      onChange={handleInputChange}\n                      required\n                      style={{\n                        width: '100%',\n                        padding: '12px',\n                        border: '2px solid #e0e0e0',\n                        borderRadius: 8,\n                        fontSize: '1rem'\n                      }}\n                    >\n                      {statutOptions.map(statut => (\n                        <option key={statut} value={statut}>{statut}</option>\n                      ))}\n                    </select>\n                  </div>\n                </div>\n\n                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 16, marginBottom: 16 }}>\n                  <div>\n                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>\n                      Date d'achat\n                    </label>\n                    <input\n                      type=\"date\"\n                      name=\"date_achat\"\n                      value={formData.date_achat}\n                      onChange={handleInputChange}\n                      style={{\n                        width: '100%',\n                        padding: '12px',\n                        border: '2px solid #e0e0e0',\n                        borderRadius: 8,\n                        fontSize: '1rem'\n                      }}\n                    />\n                  </div>\n\n                  <div>\n                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>\n                      Fournisseur\n                    </label>\n                    <select\n                      name=\"fournisseur_id\"\n                      value={formData.fournisseur_id}\n                      onChange={handleInputChange}\n                      style={{\n                        width: '100%',\n                        padding: '12px',\n                        border: '2px solid #e0e0e0',\n                        borderRadius: 8,\n                        fontSize: '1rem'\n                      }}\n                    >\n                      <option value=\"\">Sélectionner un fournisseur</option>\n                      {fournisseurs.map(fournisseur => (\n                        <option key={fournisseur.id} value={fournisseur.id}>\n                          {fournisseur.nom}\n                        </option>\n                      ))}\n                    </select>\n                  </div>\n                </div>\n\n                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 16, marginBottom: 16 }}>\n                  <div>\n                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>\n                      Début de garantie\n                    </label>\n                    <input\n                      type=\"date\"\n                      name=\"date_debut_garantie\"\n                      value={formData.date_debut_garantie}\n                      onChange={handleInputChange}\n                      style={{\n                        width: '100%',\n                        padding: '12px',\n                        border: '2px solid #e0e0e0',\n                        borderRadius: 8,\n                        fontSize: '1rem'\n                      }}\n                    />\n                  </div>\n\n                  <div>\n                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>\n                      Fin de garantie\n                    </label>\n                    <input\n                      type=\"date\"\n                      name=\"date_fin_garantie\"\n                      value={formData.date_fin_garantie}\n                      onChange={handleInputChange}\n                      style={{\n                        width: '100%',\n                        padding: '12px',\n                        border: '2px solid #e0e0e0',\n                        borderRadius: 8,\n                        fontSize: '1rem'\n                      }}\n                    />\n                  </div>\n                </div>\n\n                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: 16, marginBottom: 16 }}>\n                  <div>\n                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>\n                      Stock actuel\n                    </label>\n                    <input\n                      type=\"number\"\n                      name=\"stock_actuel\"\n                      value={formData.stock_actuel}\n                      onChange={handleInputChange}\n                      min=\"0\"\n                      style={{\n                        width: '100%',\n                        padding: '12px',\n                        border: '2px solid #e0e0e0',\n                        borderRadius: 8,\n                        fontSize: '1rem'\n                      }}\n                    />\n                  </div>\n\n                  <div>\n                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>\n                      Stock maximum\n                    </label>\n                    <input\n                      type=\"number\"\n                      name=\"stock_max\"\n                      value={formData.stock_max}\n                      onChange={handleInputChange}\n                      min=\"1\"\n                      style={{\n                        width: '100%',\n                        padding: '12px',\n                        border: '2px solid #e0e0e0',\n                        borderRadius: 8,\n                        fontSize: '1rem'\n                      }}\n                    />\n                  </div>\n\n                  <div>\n                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>\n                      Stock minimum\n                    </label>\n                    <input\n                      type=\"number\"\n                      name=\"stock_min\"\n                      value={formData.stock_min}\n                      onChange={handleInputChange}\n                      min=\"0\"\n                      style={{\n                        width: '100%',\n                        padding: '12px',\n                        border: '2px solid #e0e0e0',\n                        borderRadius: 8,\n                        fontSize: '1rem'\n                      }}\n                    />\n                  </div>\n                </div>\n\n                <div style={{ marginBottom: 24 }}>\n                  <label style={{ display: 'flex', alignItems: 'center', fontWeight: 600, color: '#333', cursor: 'pointer' }}>\n                    <input\n                      type=\"checkbox\"\n                      name=\"en_stock\"\n                      checked={formData.en_stock}\n                      onChange={handleInputChange}\n                      style={{ marginRight: 8, transform: 'scale(1.2)' }}\n                    />\n                    Équipement en stock\n                  </label>\n                </div>\n\n                <div style={{ display: 'flex', gap: 12, justifyContent: 'flex-end' }}>\n                  <button\n                    type=\"button\"\n                    onClick={() => {\n                      setShowModal(false);\n                      setEditingId(null);\n                    }}\n                    style={{\n                      padding: '12px 24px',\n                      border: '2px solid #e0e0e0',\n                      borderRadius: 8,\n                      background: 'white',\n                      color: '#666',\n                      fontSize: '1rem',\n                      fontWeight: 600,\n                      cursor: 'pointer'\n                    }}\n                  >\n                    Annuler\n                  </button>\n                  <button\n                    type=\"submit\"\n                    disabled={submitting}\n                    style={{\n                      padding: '12px 24px',\n                      border: 'none',\n                      borderRadius: 8,\n                      background: submitting ? '#ccc' : 'linear-gradient(90deg, #1976d2 0%, #42a5f5 100%)',\n                      color: 'white',\n                      fontSize: '1rem',\n                      fontWeight: 600,\n                      cursor: submitting ? 'not-allowed' : 'pointer',\n                      boxShadow: '0 4px 16px rgba(21,101,192,0.18)'\n                    }}\n                  >\n                    {submitting \n                      ? (editingId ? 'Modification en cours...' : 'Ajout en cours...') \n                      : (editingId ? 'Modifier l\\'équipement' : 'Ajouter l\\'équipement')}\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        )}\n\n        {/* Contenu principal */}\n        {loading ? (\n          <div style={{ textAlign: 'center', padding: '40px' }}>\n            <p>Chargement des équipements...</p>\n          </div>\n        ) : error ? (\n          <div style={{ textAlign: 'center', padding: '40px', color: '#e74a3b' }}>\n            <p>Erreur lors du chargement : {error}</p>\n            <button \n              onClick={() => window.location.reload()} \n              style={{ marginTop: '10px', padding: '8px 16px', background: '#1976d2', color: 'white', border: 'none', borderRadius: '4px', cursor: 'pointer' }}\n            >\n              Actualiser la page\n            </button>\n          </div>\n        ) : !Array.isArray(filteredEquipements) || filteredEquipements.length === 0 ? (\n          <div style={{ textAlign: 'center', padding: '40px' }}>\n            <p>{searchTerm ? 'Aucun résultat trouvé' : 'Aucun équipement trouvé'}</p>\n          </div>\n        ) : (\n          <table className=\"equipements-table\">\n            <thead>\n              <tr>\n                <th>Nom</th>\n                <th>Type</th>\n                <th>Numéro de série</th>\n                <th>Statut</th>\n                <th>Date d'achat</th>\n                <th>Date fin garantie</th>\n                <th>Fournisseur</th>\n                <th>Actions</th>\n              </tr>\n            </thead>\n            <tbody>\n              {filteredEquipements.map((eq, index) => {\n                const numeroSerie = eq.numero_serie || eq.numeroSerie || 'N/A';\n                const dateAchat = formatDate(eq.date_achat);\n                const dateFinGarantie = formatDate(eq.date_fin_garantie);\n                const fournisseur = eq.fournisseur?.nom || eq.fournisseur_id || 'Non spécifié';\n\n                return (\n                  <tr key={eq.id || index}>\n                    <td>{eq.nom || 'N/A'}</td>\n                    <td>{eq.type || 'N/A'}</td>\n                    <td>{numeroSerie}</td>\n                    <td>{eq.statut || 'N/A'}</td>\n                    <td>{dateAchat}</td>\n                    <td>{dateFinGarantie}</td>\n                    <td>{fournisseur}</td>\n                    <td>\n                      <div style={{ display: 'flex', gap: '8px' }}>\n                        <button \n                          onClick={() => handleEdit(eq)}\n                          style={{\n                            background: '#1976d2',\n                            color: 'white',\n                            border: 'none',\n                            borderRadius: '4px',\n                            padding: '6px 12px',\n                            cursor: 'pointer',\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '4px'\n                          }}\n                        >\n                          <FontAwesomeIcon icon={faEdit} />\n                          <span>Modifier</span>\n                        </button>\n                        <button \n                          onClick={() => handleDelete(eq.id)}\n                          style={{\n                            background: '#e74a3b',\n                            color: 'white',\n                            border: 'none',\n                            borderRadius: '4px',\n                            padding: '6px 12px',\n                            cursor: 'pointer',\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '4px'\n                          }}\n                        >\n                          <FontAwesomeIcon icon={faTrash} />\n                          <span>Supprimer</span>\n                        </button>\n                      </div>\n                    </td>\n                  </tr>\n                );\n              })}\n            </tbody>\n          </table>\n        )}\n      </div>\n    </div>\n  );\n}"], "mappings": ";;AAAA,OAAO,mBAAmB;AAC1B,SAASA,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,OAAOC,IAAI,MAAM,kCAAkC;AACnD,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,QAAQ,EAAEC,OAAO,EAAEC,aAAa,EAAEC,OAAO,EAAEC,SAAS,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAEC,MAAM,EAAEC,MAAM,EAAEC,OAAO,QAAQ,mCAAmC;AAC1K,SAASC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,iBAAiB;AACxB,OAAO,KAAKC,IAAI,MAAM,MAAM;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,eAAe,SAASC,WAAWA,CAAA,EAAG;EAAAC,EAAA;EACpC,MAAMC,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACwB,WAAW,EAAEC,cAAc,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACU,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAACY,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACc,KAAK,EAAEC,QAAQ,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACgB,SAAS,EAAEC,YAAY,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACkB,YAAY,EAAEC,eAAe,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACoB,UAAU,EAAEC,aAAa,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACsB,UAAU,EAAEC,aAAa,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACwB,UAAU,EAAEC,aAAa,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC0B,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;;EAEhD;EACA,MAAM,CAAC4B,QAAQ,EAAEC,WAAW,CAAC,GAAG7B,QAAQ,CAAC;IACvC8B,GAAG,EAAE,EAAE;IACPC,IAAI,EAAE,EAAE;IACRC,YAAY,EAAE,EAAE;IAChBC,MAAM,EAAE,YAAY;IACpBC,UAAU,EAAE,EAAE;IACdC,cAAc,EAAE,EAAE;IAClBC,mBAAmB,EAAE,EAAE;IACvBC,iBAAiB,EAAE,EAAE;IACrBC,QAAQ,EAAE,IAAI;IACdC,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE,CAAC;IACZC,SAAS,EAAE,CAAC;IACZC,YAAY,EAAE;EAChB,CAAC,CAAC;;EAEF;EACA,MAAMC,aAAa,GAAG,CAAC,YAAY,EAAE,gBAAgB,EAAE,QAAQ,EAAE,cAAc,CAAC;EAChF,MAAMC,WAAW,GAAG,CAAC,YAAY,EAAE,YAAY,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,SAAS,EAAE,OAAO,CAAC;EAExG7C,SAAS,CAAC,MAAM;IACd8C,eAAe,CAAC,CAAC;IACjBC,gBAAgB,CAAC,CAAC;IAClBC,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAENhD,SAAS,CAAC,MAAM;IACd,IAAIyB,UAAU,KAAK,EAAE,EAAE;MACrBb,sBAAsB,CAACH,WAAW,CAAC;IACrC,CAAC,MAAM;MACL,MAAMwC,QAAQ,GAAGxC,WAAW,CAACyC,MAAM,CAACC,UAAU;QAAA,IAAAC,qBAAA;QAAA,OAC5CD,UAAU,CAACpB,GAAG,CAACsB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC7B,UAAU,CAAC4B,WAAW,CAAC,CAAC,CAAC,IAC/DF,UAAU,CAACnB,IAAI,CAACqB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC7B,UAAU,CAAC4B,WAAW,CAAC,CAAC,CAAC,IAC/DF,UAAU,CAAClB,YAAY,IAAIkB,UAAU,CAAClB,YAAY,CAACoB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC7B,UAAU,CAAC4B,WAAW,CAAC,CAAC,CAAE,IACrGF,UAAU,CAACjB,MAAM,CAACmB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC7B,UAAU,CAAC4B,WAAW,CAAC,CAAC,CAAC,IACjE,EAAAD,qBAAA,GAAAD,UAAU,CAACI,WAAW,cAAAH,qBAAA,uBAAtBA,qBAAA,CAAwBrB,GAAG,KAAIoB,UAAU,CAACI,WAAW,CAACxB,GAAG,CAACsB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC7B,UAAU,CAAC4B,WAAW,CAAC,CAAC,CAAE;MAAA,CAC9G,CAAC;MACDzC,sBAAsB,CAACqC,QAAQ,CAAC;IAClC;EACF,CAAC,EAAE,CAACxB,UAAU,EAAEhB,WAAW,CAAC,CAAC;EAE7B,MAAMqC,eAAe,GAAGA,CAAA,KAAM;IAC5BU,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;IAE5C,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IAC/C,MAAMC,OAAO,GAAG;MACd,cAAc,EAAE;IAClB,CAAC;IACD,IAAIH,KAAK,EAAE;MACTG,OAAO,CAAC,eAAe,CAAC,GAAG,UAAUH,KAAK,EAAE;IAC9C;IAEAI,KAAK,CAAC,uCAAuC,EAAE;MAC7CC,MAAM,EAAE,KAAK;MACbF,OAAO,EAAEA;IACX,CAAC,CAAC,CACCG,IAAI,CAACC,GAAG,IAAI;MACXT,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEQ,GAAG,CAACC,MAAM,CAAC;MAC5D,IAAI,CAACD,GAAG,CAACE,EAAE,EAAE;QACX,MAAM,IAAIC,KAAK,CAAC,gBAAgBH,GAAG,CAACC,MAAM,EAAE,CAAC;MAC/C;MACA,OAAOD,GAAG,CAACI,IAAI,CAAC,CAAC;IACnB,CAAC,CAAC,CACDL,IAAI,CAACM,IAAI,IAAI;MACZd,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEa,IAAI,CAAC;MAChD,IAAIC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,EAAE;QACvB5D,cAAc,CAAC4D,IAAI,CAAC;QACpB1D,sBAAsB,CAAC0D,IAAI,CAAC;QAC5BtD,QAAQ,CAAC,IAAI,CAAC;MAChB,CAAC,MAAM;QACLwC,OAAO,CAACiB,IAAI,CAAC,iDAAiD,EAAEH,IAAI,CAAC;QACrE5D,cAAc,CAAC,EAAE,CAAC;QAClBE,sBAAsB,CAAC,EAAE,CAAC;QAC1BI,QAAQ,CAAC,6BAA6B,CAAC;MACzC;MACAF,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,CACD4D,KAAK,CAACC,GAAG,IAAI;MACZnB,OAAO,CAACzC,KAAK,CAAC,4CAA4C,EAAE4D,GAAG,CAAC;MAChEjE,cAAc,CAAC,EAAE,CAAC;MAClBE,sBAAsB,CAAC,EAAE,CAAC;MAC1BI,QAAQ,CAAC2D,GAAG,CAACC,OAAO,IAAI,sBAAsB,CAAC;MAC/C9D,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC;EACN,CAAC;EAED,MAAMiC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMW,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IAC/C,MAAMC,OAAO,GAAG;MACd,cAAc,EAAE;IAClB,CAAC;IACD,IAAIH,KAAK,EAAE;MACTG,OAAO,CAAC,eAAe,CAAC,GAAG,UAAUH,KAAK,EAAE;IAC9C;IAEAI,KAAK,CAAC,wCAAwC,EAAE;MAC9CC,MAAM,EAAE,KAAK;MACbF,OAAO,EAAEA;IACX,CAAC,CAAC,CACCG,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACE,EAAE,GAAGF,GAAG,CAACI,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,CACrCL,IAAI,CAACM,IAAI,IAAIlD,eAAe,CAACmD,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,GAAGA,IAAI,GAAG,EAAE,CAAC,CAAC,CAC9DI,KAAK,CAACC,GAAG,IAAI;MACZnB,OAAO,CAACzC,KAAK,CAAC,6CAA6C,EAAE4D,GAAG,CAAC;MACjEvD,eAAe,CAAC,EAAE,CAAC;IACrB,CAAC,CAAC;EACN,CAAC;EAED,MAAM4B,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMU,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IAC/C,MAAMC,OAAO,GAAG;MACd,cAAc,EAAE;IAClB,CAAC;IACD,IAAIH,KAAK,EAAE;MACTG,OAAO,CAAC,eAAe,CAAC,GAAG,UAAUH,KAAK,EAAE;IAC9C;IAEAI,KAAK,CAAC,sCAAsC,EAAE;MAC5CC,MAAM,EAAE,KAAK;MACbF,OAAO,EAAEA;IACX,CAAC,CAAC,CACCG,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACE,EAAE,GAAGF,GAAG,CAACI,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,CACrCL,IAAI,CAACM,IAAI,IAAIhD,aAAa,CAACiD,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,GAAGA,IAAI,GAAG,EAAE,CAAC,CAAC,CAC5DI,KAAK,CAACC,GAAG,IAAI;MACZnB,OAAO,CAACzC,KAAK,CAAC,2CAA2C,EAAE4D,GAAG,CAAC;MAC/DrD,aAAa,CAAC,EAAE,CAAC;IACnB,CAAC,CAAC;EACN,CAAC;EAED,MAAMuD,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC,KAAK;MAAEhD,IAAI;MAAEiD;IAAQ,CAAC,GAAGH,CAAC,CAACI,MAAM;IAC/CpD,WAAW,CAACqD,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACJ,IAAI,GAAG/C,IAAI,KAAK,UAAU,GAAGiD,OAAO,GAAGD;IAC1C,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMI,YAAY,GAAG,MAAON,CAAC,IAAK;IAChCA,CAAC,CAACO,cAAc,CAAC,CAAC;IAClB7D,aAAa,CAAC,IAAI,CAAC;IAEnB,MAAM8D,UAAU,GAAG;MACjB,GAAGzD,QAAQ;MACXO,cAAc,EAAEP,QAAQ,CAACO,cAAc,IAAI;IAC7C,CAAC;IAED,IAAI;MACF,MAAMsB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;MAC/C,MAAMC,OAAO,GAAG;QACd,cAAc,EAAE;MAClB,CAAC;MACD,IAAIH,KAAK,EAAE;QACTG,OAAO,CAAC,eAAe,CAAC,GAAG,UAAUH,KAAK,EAAE;MAC9C;MAEA,MAAM6B,GAAG,GAAG5D,SAAS,GACjB,yCAAyCA,SAAS,EAAE,GACpD,uCAAuC;MAE3C,MAAMoC,MAAM,GAAGpC,SAAS,GAAG,KAAK,GAAG,MAAM;MAEzC,MAAM6D,QAAQ,GAAG,MAAM1B,KAAK,CAACyB,GAAG,EAAE;QAChCxB,MAAM,EAAEA,MAAM;QACdF,OAAO,EAAEA,OAAO;QAChB4B,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACL,UAAU;MACjC,CAAC,CAAC;MAEF,IAAI,CAACE,QAAQ,CAACrB,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,gBAAgBoB,QAAQ,CAACtB,MAAM,EAAE,CAAC;MACpD;MAEA,MAAM0B,MAAM,GAAG,MAAMJ,QAAQ,CAACnB,IAAI,CAAC,CAAC;MAEpC,IAAI1C,SAAS,EAAE;QACbjB,cAAc,CAACyE,IAAI,IAAIA,IAAI,CAACU,GAAG,CAACC,EAAE,IAAIA,EAAE,CAACC,EAAE,KAAKpE,SAAS,GAAGiE,MAAM,GAAGE,EAAE,CAAC,CAAC;QACzElF,sBAAsB,CAACuE,IAAI,IAAIA,IAAI,CAACU,GAAG,CAACC,EAAE,IAAIA,EAAE,CAACC,EAAE,KAAKpE,SAAS,GAAGiE,MAAM,GAAGE,EAAE,CAAC,CAAC;MACnF,CAAC,MAAM;QACLpF,cAAc,CAACyE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAES,MAAM,CAAC,CAAC;QACzChF,sBAAsB,CAACuE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAES,MAAM,CAAC,CAAC;MACnD;MAEA9D,WAAW,CAAC;QACVC,GAAG,EAAE,EAAE;QACPC,IAAI,EAAE,EAAE;QACRC,YAAY,EAAE,EAAE;QAChBC,MAAM,EAAE,YAAY;QACpBC,UAAU,EAAE,EAAE;QACdC,cAAc,EAAE,EAAE;QAClBC,mBAAmB,EAAE,EAAE;QACvBC,iBAAiB,EAAE,EAAE;QACrBC,QAAQ,EAAE,IAAI;QACdC,YAAY,EAAE,CAAC;QACfC,SAAS,EAAE,CAAC;QACZC,SAAS,EAAE,CAAC;QACZC,YAAY,EAAE;MAChB,CAAC,CAAC;MACFzB,YAAY,CAAC,KAAK,CAAC;MACnBU,YAAY,CAAC,IAAI,CAAC;MAElBoE,KAAK,CAACrE,SAAS,GAAG,kCAAkC,GAAG,iCAAiC,CAAC;IAC3F,CAAC,CAAC,OAAOgD,GAAG,EAAE;MACZnB,OAAO,CAACzC,KAAK,CAAC,6BAA6B,EAAE4D,GAAG,CAAC;MACjDqB,KAAK,CAAC,WAAWrB,GAAG,CAACC,OAAO,EAAE,CAAC;IACjC,CAAC,SAAS;MACRpD,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMyE,UAAU,GAAI9C,UAAU,IAAK;IAAA,IAAA+C,sBAAA;IACjCtE,YAAY,CAACuB,UAAU,CAAC4C,EAAE,CAAC;IAC3BjE,WAAW,CAAC;MACVC,GAAG,EAAEoB,UAAU,CAACpB,GAAG;MACnBC,IAAI,EAAEmB,UAAU,CAACnB,IAAI;MACrBC,YAAY,EAAEkB,UAAU,CAAClB,YAAY,IAAIkB,UAAU,CAACgD,WAAW,IAAI,EAAE;MACrEjE,MAAM,EAAEiB,UAAU,CAACjB,MAAM;MACzBC,UAAU,EAAEgB,UAAU,CAAChB,UAAU,IAAI,EAAE;MACvCC,cAAc,EAAE,EAAA8D,sBAAA,GAAA/C,UAAU,CAACI,WAAW,cAAA2C,sBAAA,uBAAtBA,sBAAA,CAAwBH,EAAE,KAAI5C,UAAU,CAACf,cAAc,IAAI,EAAE;MAC7EC,mBAAmB,EAAEc,UAAU,CAACd,mBAAmB,IAAI,EAAE;MACzDC,iBAAiB,EAAEa,UAAU,CAACb,iBAAiB,IAAI,EAAE;MACrDC,QAAQ,EAAEY,UAAU,CAACZ,QAAQ,KAAK6D,SAAS,GAAGjD,UAAU,CAACZ,QAAQ,GAAG,IAAI;MACxEC,YAAY,EAAEW,UAAU,CAACX,YAAY,IAAI,CAAC;MAC1CC,SAAS,EAAEU,UAAU,CAACV,SAAS,IAAI,CAAC;MACpCC,SAAS,EAAES,UAAU,CAACT,SAAS,IAAI,CAAC;MACpCC,YAAY,EAAEQ,UAAU,CAACR,YAAY,IAAI;IAC3C,CAAC,CAAC;IACFzB,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMmF,YAAY,GAAG,MAAON,EAAE,IAAK;IACjC,IAAI,CAACO,MAAM,CAACC,OAAO,CAAC,qDAAqD,CAAC,EAAE;MAC1E;IACF;IAEA,IAAI;MACF,MAAM7C,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;MAC/C,MAAMC,OAAO,GAAG;QACd,cAAc,EAAE;MAClB,CAAC;MACD,IAAIH,KAAK,EAAE;QACTG,OAAO,CAAC,eAAe,CAAC,GAAG,UAAUH,KAAK,EAAE;MAC9C;MAEA,MAAM8B,QAAQ,GAAG,MAAM1B,KAAK,CAAC,yCAAyCiC,EAAE,EAAE,EAAE;QAC1EhC,MAAM,EAAE,QAAQ;QAChBF,OAAO,EAAEA;MACX,CAAC,CAAC;MAEF,IAAI,CAAC2B,QAAQ,CAACrB,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,gBAAgBoB,QAAQ,CAACtB,MAAM,EAAE,CAAC;MACpD;MAEAxD,cAAc,CAACyE,IAAI,IAAIA,IAAI,CAACjC,MAAM,CAAC4C,EAAE,IAAIA,EAAE,CAACC,EAAE,KAAKA,EAAE,CAAC,CAAC;MACvDnF,sBAAsB,CAACuE,IAAI,IAAIA,IAAI,CAACjC,MAAM,CAAC4C,EAAE,IAAIA,EAAE,CAACC,EAAE,KAAKA,EAAE,CAAC,CAAC;MAE/DC,KAAK,CAAC,mCAAmC,CAAC;IAC5C,CAAC,CAAC,OAAOrB,GAAG,EAAE;MACZnB,OAAO,CAACzC,KAAK,CAAC,gCAAgC,EAAE4D,GAAG,CAAC;MACpDqB,KAAK,CAAC,kCAAkCrB,GAAG,CAACC,OAAO,EAAE,CAAC;IACxD;EACF,CAAC;EAED,MAAM4B,kBAAkB,GAAI1B,CAAC,IAAK;IAChCpD,aAAa,CAACoD,CAAC,CAACI,MAAM,CAACF,KAAK,CAAC;EAC/B,CAAC;EAED,MAAMyB,UAAU,GAAIC,SAAS,IAAK;IAChC,IAAI,CAACA,SAAS,EAAE,OAAO,KAAK;IAE5B,IAAI;MACF;MACA,IAAI,OAAOA,SAAS,KAAK,QAAQ,IAAIA,SAAS,CAACC,KAAK,CAAC,qBAAqB,CAAC,EAAE;QAC3E,MAAM,CAACC,IAAI,EAAEC,KAAK,EAAEC,GAAG,CAAC,GAAGJ,SAAS,CAACK,KAAK,CAAC,GAAG,CAAC;QAC/C,OAAO,GAAGD,GAAG,IAAID,KAAK,IAAID,IAAI,EAAE;MAClC;;MAEA;MACA,MAAMI,OAAO,GAAG,IAAIC,IAAI,CAACP,SAAS,CAAC;MACnC,IAAI,CAACQ,KAAK,CAACF,OAAO,CAACG,OAAO,CAAC,CAAC,CAAC,EAAE;QAC7B,OAAOH,OAAO,CAACI,kBAAkB,CAAC,OAAO,CAAC;MAC5C;MAEA,OAAOV,SAAS,CAAC,CAAC;IACpB,CAAC,CAAC,OAAO5B,CAAC,EAAE;MACVtB,OAAO,CAACzC,KAAK,CAAC,6BAA6B,EAAE+D,CAAC,CAAC;MAC/C,OAAO,KAAK;IACd;EACF,CAAC;EAED,MAAMuC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI,CAAC9C,KAAK,CAACC,OAAO,CAAC7D,mBAAmB,CAAC,IAAIA,mBAAmB,CAAC2G,MAAM,KAAK,CAAC,EAAE;MAC3EtB,KAAK,CAAC,0BAA0B,CAAC;MACjC;IACF;IAEA,MAAMuB,OAAO,GAAG,CACd,KAAK,EACL,MAAM,EACN,iBAAiB,EACjB,QAAQ,EACR,cAAc,EACd,mBAAmB,EACnB,aAAa,EACb,SAAS,CACV;IACD,MAAMC,IAAI,GAAG7G,mBAAmB,CAACkF,GAAG,CAACC,EAAE;MAAA,IAAA2B,eAAA;MAAA,OAAI,CACzC3B,EAAE,CAAC/D,GAAG,IAAI,EAAE,EACZ+D,EAAE,CAAC9D,IAAI,IAAI,EAAE,EACb8D,EAAE,CAAC7D,YAAY,IAAI,EAAE,EACrB6D,EAAE,CAAC5D,MAAM,IAAI,EAAE,EACfuE,UAAU,CAACX,EAAE,CAAC3D,UAAU,CAAC,IAAI,EAAE,EAC/BsE,UAAU,CAACX,EAAE,CAACxD,iBAAiB,CAAC,IAAI,EAAE,EACtC,EAAAmF,eAAA,GAAA3B,EAAE,CAACvC,WAAW,cAAAkE,eAAA,uBAAdA,eAAA,CAAgB1F,GAAG,KAAI,EAAE,EACzB,EAAE,CACH;IAAA,EAAC;IACF,MAAM2F,SAAS,GAAGvH,IAAI,CAACwH,KAAK,CAACC,YAAY,CAAC,CAACL,OAAO,EAAE,GAAGC,IAAI,CAAC,CAAC;IAC7D,MAAMK,QAAQ,GAAG1H,IAAI,CAACwH,KAAK,CAACG,QAAQ,CAAC,CAAC;IACtC3H,IAAI,CAACwH,KAAK,CAACI,iBAAiB,CAACF,QAAQ,EAAEH,SAAS,EAAE,aAAa,CAAC;IAChEvH,IAAI,CAAC6H,SAAS,CAACH,QAAQ,EAAE,kBAAkB,CAAC;EAC9C,CAAC;EAED,MAAMI,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAAC1D,KAAK,CAACC,OAAO,CAAC7D,mBAAmB,CAAC,IAAIA,mBAAmB,CAAC2G,MAAM,KAAK,CAAC,EAAE;MAC3EtB,KAAK,CAAC,0BAA0B,CAAC;MACjC;IACF;IAEA,MAAMkC,GAAG,GAAG,IAAIhI,KAAK,CAAC,CAAC;IACvBgI,GAAG,CAACC,IAAI,CAAC,uBAAuB,EAAE,EAAE,EAAE,EAAE,CAAC;IACzC,MAAMZ,OAAO,GAAG,CACd,KAAK,EACL,MAAM,EACN,iBAAiB,EACjB,QAAQ,EACR,cAAc,EACd,aAAa,CACd;IACD,MAAMC,IAAI,GAAG7G,mBAAmB,CAACkF,GAAG,CAACC,EAAE;MAAA,IAAAsC,gBAAA;MAAA,OAAI,CACzCtC,EAAE,CAAC/D,GAAG,IAAI,EAAE,EACZ+D,EAAE,CAAC9D,IAAI,IAAI,EAAE,EACb8D,EAAE,CAAC7D,YAAY,IAAI,EAAE,EACrB6D,EAAE,CAAC5D,MAAM,IAAI,EAAE,EACfuE,UAAU,CAACX,EAAE,CAAC3D,UAAU,CAAC,IAAI,EAAE,EAC/B,EAAAiG,gBAAA,GAAAtC,EAAE,CAACvC,WAAW,cAAA6E,gBAAA,uBAAdA,gBAAA,CAAgBrG,GAAG,KAAI,EAAE,CAC1B;IAAA,EAAC;IACFmG,GAAG,CAACG,SAAS,CAAC;MACZC,IAAI,EAAE,CAACf,OAAO,CAAC;MACf9B,IAAI,EAAE+B,IAAI;MACVe,MAAM,EAAE,EAAE;MACVC,MAAM,EAAE;QAAEC,QAAQ,EAAE;MAAG;IACzB,CAAC,CAAC;IACFP,GAAG,CAACQ,IAAI,CAAC,iBAAiB,CAAC;EAC7B,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxBrC,MAAM,CAACsC,KAAK,CAAC,CAAC;EAChB,CAAC;EAED,oBACEvI,OAAA;IAAKwI,SAAS,EAAC,uBAAuB;IAAAC,QAAA,gBAEpCzI,OAAA;MAAKwI,SAAS,EAAC,SAAS;MAAAC,QAAA,gBACtBzI,OAAA;QAAKwI,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3BzI,OAAA;UAAK0I,GAAG,EAAE7J,IAAK;UAAC8J,GAAG,EAAC,iBAAiB;UAACH,SAAS,EAAC;QAAc;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC,eACN/I,OAAA;QAAKwI,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBzI,OAAA;UAAAyI,QAAA,gBACEzI,OAAA;YAAAyI,QAAA,eACEzI,OAAA,CAACrB,IAAI;cAACqK,EAAE,EAAC,GAAG;cAACR,SAAS,EAAErI,QAAQ,CAAC8I,QAAQ,KAAK,GAAG,GAAG,oBAAoB,GAAG,EAAG;cAAAR,QAAA,gBAC5EzI,OAAA,CAAClB,eAAe;gBAACoK,IAAI,EAAEnK,QAAS;gBAACoK,KAAK,EAAE;kBAACC,WAAW,EAAC;gBAAC;cAAE;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,YAC7D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACL/I,OAAA;YAAAyI,QAAA,eACEzI,OAAA,CAACrB,IAAI;cAACqK,EAAE,EAAC,cAAc;cAACR,SAAS,EAAErI,QAAQ,CAAC8I,QAAQ,KAAK,cAAc,GAAG,QAAQ,GAAG,EAAG;cAAAR,QAAA,gBACtFzI,OAAA,CAAClB,eAAe;gBAACoK,IAAI,EAAEnK,QAAS;gBAACoK,KAAK,EAAE;kBAACC,WAAW,EAAC;gBAAC;cAAE;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gCAC7D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACL/I,OAAA;YAAAyI,QAAA,eACEzI,OAAA,CAACrB,IAAI;cAACqK,EAAE,EAAC,WAAW;cAACR,SAAS,EAAErI,QAAQ,CAAC8I,QAAQ,KAAK,WAAW,GAAG,QAAQ,GAAG,EAAG;cAAAR,QAAA,gBAChFzI,OAAA,CAAClB,eAAe;gBAACoK,IAAI,EAAElK,OAAQ;gBAACmK,KAAK,EAAE;kBAACC,WAAW,EAAC;gBAAC;cAAE;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,6BAC5D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACL/I,OAAA;YAAAyI,QAAA,eACEzI,OAAA,CAACrB,IAAI;cAACqK,EAAE,EAAC,eAAe;cAACR,SAAS,EAAErI,QAAQ,CAAC8I,QAAQ,KAAK,eAAe,GAAG,QAAQ,GAAG,EAAG;cAAAR,QAAA,gBACxFzI,OAAA,CAAClB,eAAe;gBAACoK,IAAI,EAAEjK,aAAc;gBAACkK,KAAK,EAAE;kBAACC,WAAW,EAAC;gBAAC;cAAE;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,2BAClE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACL/I,OAAA;YAAAyI,QAAA,eACEzI,OAAA,CAACrB,IAAI;cAACqK,EAAE,EAAC,eAAe;cAACR,SAAS,EAAErI,QAAQ,CAAC8I,QAAQ,KAAK,eAAe,GAAG,QAAQ,GAAG,EAAG;cAAAR,QAAA,gBACxFzI,OAAA,CAAClB,eAAe;gBAACoK,IAAI,EAAEhK,OAAQ;gBAACiK,KAAK,EAAE;kBAACC,WAAW,EAAC;gBAAC;cAAE;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,8BAC5D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACN/I,OAAA;QAAKwI,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BzI,OAAA,CAACrB,IAAI;UAACqK,EAAE,EAAC,SAAS;UAAAP,QAAA,gBAChBzI,OAAA,CAAClB,eAAe;YAACoK,IAAI,EAAE/J,SAAU;YAACgK,KAAK,EAAE;cAACC,WAAW,EAAC;YAAC;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,+BAC9D;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN/I,OAAA;QAAKmJ,KAAK,EAAE;UAAEE,SAAS,EAAE,MAAM;UAAEC,OAAO,EAAE,YAAY;UAAEC,SAAS,EAAE;QAAS,CAAE;QAAAd,QAAA,eAC5EzI,OAAA;UACEwI,SAAS,EAAC,YAAY;UACtBgB,OAAO,EAAEA,CAAA,KAAM;YACblG,YAAY,CAACmG,UAAU,CAAC,WAAW,CAAC;YACpCxD,MAAM,CAAC9F,QAAQ,CAACuJ,IAAI,GAAG,GAAG;UAC5B,CAAE;UAAAjB,QAAA,gBAEFzI,OAAA,CAAClB,eAAe;YAACoK,IAAI,EAAE9J,YAAa;YAAC+J,KAAK,EAAE;cAACC,WAAW,EAAC;YAAC;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,mBACjE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN/I,OAAA;MAAKwI,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAElCzI,OAAA;QAAKwI,SAAS,EAAC,sBAAsB;QAACW,KAAK,EAAE;UAAEQ,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,cAAc,EAAE,eAAe;UAAEC,YAAY,EAAE;QAAG,CAAE;QAAArB,QAAA,gBACxIzI,OAAA;UAAKwI,SAAS,EAAC,YAAY;UAACW,KAAK,EAAE;YAAEY,IAAI,EAAE,CAAC;YAAEC,QAAQ,EAAE,GAAG;YAAEZ,WAAW,EAAE,EAAE;YAAEa,QAAQ,EAAE;UAAW,CAAE;UAAAxB,QAAA,gBACnGzI,OAAA,CAAClB,eAAe;YAACoK,IAAI,EAAE7J,QAAS;YAAC8J,KAAK,EAAE;cAAEc,QAAQ,EAAE,UAAU;cAAEC,IAAI,EAAE,EAAE;cAAEC,GAAG,EAAE,KAAK;cAAEC,SAAS,EAAE,kBAAkB;cAAEC,KAAK,EAAE,SAAS;cAAEjC,QAAQ,EAAE,QAAQ;cAAEkC,OAAO,EAAE;YAAI;UAAE;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7K/I,OAAA;YACE2B,IAAI,EAAC,MAAM;YACX4I,WAAW,EAAC,0BAAuB;YACnC5F,KAAK,EAAEvD,UAAW;YAClBoJ,QAAQ,EAAErE,kBAAmB;YAC7BgD,KAAK,EAAE;cAAEsB,KAAK,EAAE,MAAM;cAAEnB,OAAO,EAAE,qBAAqB;cAAEoB,YAAY,EAAE,CAAC;cAAEC,MAAM,EAAE,mBAAmB;cAAEvC,QAAQ,EAAE,MAAM;cAAEwC,SAAS,EAAE;YAAkC;UAAE;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN/I,OAAA;UAAKwI,SAAS,EAAC,eAAe;UAACW,KAAK,EAAE;YAAEQ,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEiB,GAAG,EAAE;UAAG,CAAE;UAAApC,QAAA,gBACvFzI,OAAA;YAAKwI,SAAS,EAAC,mBAAmB;YAACW,KAAK,EAAE;cAAEc,QAAQ,EAAE,UAAU;cAAEb,WAAW,EAAE,CAAC;cAAE0B,MAAM,EAAE;YAAU,CAAE;YAAArC,QAAA,gBACpGzI,OAAA,CAAClB,eAAe;cAACoK,IAAI,EAAE5J,MAAO;cAAC6J,KAAK,EAAE;gBAAEf,QAAQ,EAAE,QAAQ;gBAAEiC,KAAK,EAAE;cAAU;YAAE;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClF/I,OAAA;cAAMmJ,KAAK,EAAE;gBAAEc,QAAQ,EAAE,UAAU;gBAAEE,GAAG,EAAE,CAAC,CAAC;gBAAEY,KAAK,EAAE,CAAC,CAAC;gBAAEC,UAAU,EAAE,SAAS;gBAAEX,KAAK,EAAE,MAAM;gBAAEK,YAAY,EAAE,KAAK;gBAAEtC,QAAQ,EAAE,QAAQ;gBAAEkB,OAAO,EAAE,SAAS;gBAAE2B,UAAU,EAAE;cAAI,CAAE;cAAAxC,QAAA,EAAC;YAAC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpL,CAAC,eACN/I,OAAA;YAAMmJ,KAAK,EAAE;cAAE8B,UAAU,EAAE,GAAG;cAAEZ,KAAK,EAAE,SAAS;cAAEjC,QAAQ,EAAE,MAAM;cAAEgB,WAAW,EAAE;YAAE,CAAE;YAAAX,QAAA,EAAC;UAAc;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3G/I,OAAA,CAAClB,eAAe;YAACoK,IAAI,EAAE/J,SAAU;YAACgK,KAAK,EAAE;cAAEf,QAAQ,EAAE,QAAQ;cAAEiC,KAAK,EAAE,SAAS;cAAEW,UAAU,EAAE,MAAM;cAAEN,YAAY,EAAE,KAAK;cAAEpB,OAAO,EAAE,CAAC;cAAEsB,SAAS,EAAE;YAAkC;UAAE;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN/I,OAAA;QAAIwI,SAAS,EAAC,mBAAmB;QAAAC,QAAA,GAAC,4BACT,EAACnI,mBAAmB,CAAC2G,MAAM,EAAC,GACrD;MAAA;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAEL/I,OAAA;QAAKmJ,KAAK,EAAE;UAAEQ,OAAO,EAAE,MAAM;UAAEkB,GAAG,EAAE,EAAE;UAAEf,YAAY,EAAE;QAAG,CAAE;QAAArB,QAAA,gBACzDzI,OAAA;UACEwI,SAAS,EAAC,iBAAiB;UAC3BW,KAAK,EAAE;YAAE8B,UAAU,EAAE,GAAG;YAAE7C,QAAQ,EAAE,MAAM;YAAEsC,YAAY,EAAE,EAAE;YAAEM,UAAU,EAAE,kDAAkD;YAAEX,KAAK,EAAE,MAAM;YAAEO,SAAS,EAAE;UAAmC,CAAE;UAC7LpB,OAAO,EAAEA,CAAA,KAAM;YACbjI,YAAY,CAAC,IAAI,CAAC;YAClBV,YAAY,CAAC,IAAI,CAAC;UACpB,CAAE;UAAA4H,QAAA,gBAEFzI,OAAA,CAAClB,eAAe;YAACoK,IAAI,EAAE1J,MAAO;YAAC2J,KAAK,EAAE;cAACC,WAAW,EAAE;YAAC;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,4BAE5D;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT/I,OAAA;UAAQwI,SAAS,EAAC,iBAAiB;UAACW,KAAK,EAAE;YAAE8B,UAAU,EAAE,GAAG;YAAE7C,QAAQ,EAAE,MAAM;YAAEsC,YAAY,EAAE,EAAE;YAAEM,UAAU,EAAE,kDAAkD;YAAEX,KAAK,EAAE,MAAM;YAAEO,SAAS,EAAE;UAAkC,CAAE;UAACpB,OAAO,EAAExC,iBAAkB;UAAAyB,QAAA,EAAC;QAE7P;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT/I,OAAA;UAAQwI,SAAS,EAAC,iBAAiB;UAACW,KAAK,EAAE;YAAE8B,UAAU,EAAE,GAAG;YAAE7C,QAAQ,EAAE,MAAM;YAAEsC,YAAY,EAAE,EAAE;YAAEM,UAAU,EAAE,kDAAkD;YAAEX,KAAK,EAAE,MAAM;YAAEO,SAAS,EAAE;UAAkC,CAAE;UAACpB,OAAO,EAAE5B,eAAgB;UAAAa,QAAA,EAAC;QAE3P;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT/I,OAAA;UAAQwI,SAAS,EAAC,iBAAiB;UAACW,KAAK,EAAE;YAAE8B,UAAU,EAAE,GAAG;YAAE7C,QAAQ,EAAE,MAAM;YAAEsC,YAAY,EAAE,EAAE;YAAEM,UAAU,EAAE,kDAAkD;YAAEX,KAAK,EAAE,MAAM;YAAEO,SAAS,EAAE;UAAkC,CAAE;UAACpB,OAAO,EAAElB,WAAY;UAAAG,QAAA,EAAC;QAEvP;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGLnI,SAAS,iBACRZ,OAAA;QAAKmJ,KAAK,EAAE;UACVc,QAAQ,EAAE,OAAO;UACjBE,GAAG,EAAE,CAAC;UACND,IAAI,EAAE,CAAC;UACPa,KAAK,EAAE,CAAC;UACRG,MAAM,EAAE,CAAC;UACTC,eAAe,EAAE,oBAAoB;UACrCxB,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBuB,MAAM,EAAE;QACV,CAAE;QAAA3C,QAAA,eACAzI,OAAA;UAAKmJ,KAAK,EAAE;YACVgC,eAAe,EAAE,OAAO;YACxBT,YAAY,EAAE,EAAE;YAChBpB,OAAO,EAAE,EAAE;YACXU,QAAQ,EAAE,GAAG;YACbS,KAAK,EAAE,KAAK;YACZY,SAAS,EAAE,MAAM;YACjBC,SAAS,EAAE,MAAM;YACjBV,SAAS,EAAE;UACb,CAAE;UAAAnC,QAAA,gBACAzI,OAAA;YAAKmJ,KAAK,EAAE;cAAEQ,OAAO,EAAE,MAAM;cAAEE,cAAc,EAAE,eAAe;cAAED,UAAU,EAAE,QAAQ;cAAEE,YAAY,EAAE;YAAG,CAAE;YAAArB,QAAA,gBACvGzI,OAAA;cAAImJ,KAAK,EAAE;gBAAEoC,MAAM,EAAE,CAAC;gBAAElB,KAAK,EAAE,SAAS;gBAAEjC,QAAQ,EAAE,QAAQ;gBAAE6C,UAAU,EAAE;cAAI,CAAE;cAAAxC,QAAA,EAC7EnH,SAAS,GAAG,wBAAwB,GAAG;YAA8B;cAAAsH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpE,CAAC,eACL/I,OAAA;cACEwJ,OAAO,EAAEA,CAAA,KAAM;gBACb3I,YAAY,CAAC,KAAK,CAAC;gBACnBU,YAAY,CAAC,IAAI,CAAC;cACpB,CAAE;cACF4H,KAAK,EAAE;gBACL6B,UAAU,EAAE,MAAM;gBAClBL,MAAM,EAAE,MAAM;gBACdvC,QAAQ,EAAE,QAAQ;gBAClBiC,KAAK,EAAE,MAAM;gBACbS,MAAM,EAAE,SAAS;gBACjBxB,OAAO,EAAE,CAAC;gBACVoB,YAAY,EAAE;cAChB,CAAE;cAAAjC,QAAA,eAEFzI,OAAA,CAAClB,eAAe;gBAACoK,IAAI,EAAE3J;cAAQ;gBAAAqJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN/I,OAAA;YAAMwL,QAAQ,EAAEzG,YAAa;YAAA0D,QAAA,gBAC3BzI,OAAA;cAAKmJ,KAAK,EAAE;gBAAEQ,OAAO,EAAE,MAAM;gBAAE8B,mBAAmB,EAAE,SAAS;gBAAEZ,GAAG,EAAE,EAAE;gBAAEf,YAAY,EAAE;cAAG,CAAE;cAAArB,QAAA,gBACzFzI,OAAA;gBAAAyI,QAAA,gBACEzI,OAAA;kBAAOmJ,KAAK,EAAE;oBAAEQ,OAAO,EAAE,OAAO;oBAAEG,YAAY,EAAE,CAAC;oBAAEmB,UAAU,EAAE,GAAG;oBAAEZ,KAAK,EAAE;kBAAO,CAAE;kBAAA5B,QAAA,EAAC;gBAErF;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR/I,OAAA;kBACE2B,IAAI,EAAC,MAAM;kBACX+C,IAAI,EAAC,KAAK;kBACVC,KAAK,EAAEnD,QAAQ,CAACE,GAAI;kBACpB8I,QAAQ,EAAEhG,iBAAkB;kBAC5BkH,QAAQ;kBACRvC,KAAK,EAAE;oBACLsB,KAAK,EAAE,MAAM;oBACbnB,OAAO,EAAE,MAAM;oBACfqB,MAAM,EAAE,mBAAmB;oBAC3BD,YAAY,EAAE,CAAC;oBACftC,QAAQ,EAAE,MAAM;oBAChBuD,UAAU,EAAE;kBACd,CAAE;kBACFC,OAAO,EAAGnH,CAAC,IAAKA,CAAC,CAACI,MAAM,CAACsE,KAAK,CAAC0C,WAAW,GAAG,SAAU;kBACvDC,MAAM,EAAGrH,CAAC,IAAKA,CAAC,CAACI,MAAM,CAACsE,KAAK,CAAC0C,WAAW,GAAG;gBAAU;kBAAAjD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN/I,OAAA;gBAAAyI,QAAA,gBACEzI,OAAA;kBAAOmJ,KAAK,EAAE;oBAAEQ,OAAO,EAAE,OAAO;oBAAEG,YAAY,EAAE,CAAC;oBAAEmB,UAAU,EAAE,GAAG;oBAAEZ,KAAK,EAAE;kBAAO,CAAE;kBAAA5B,QAAA,EAAC;gBAErF;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR/I,OAAA;kBACE0E,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAEnD,QAAQ,CAACG,IAAK;kBACrB6I,QAAQ,EAAEhG,iBAAkB;kBAC5BkH,QAAQ;kBACRvC,KAAK,EAAE;oBACLsB,KAAK,EAAE,MAAM;oBACbnB,OAAO,EAAE,MAAM;oBACfqB,MAAM,EAAE,mBAAmB;oBAC3BD,YAAY,EAAE,CAAC;oBACftC,QAAQ,EAAE;kBACZ,CAAE;kBAAAK,QAAA,gBAEFzI,OAAA;oBAAQ2E,KAAK,EAAC,EAAE;oBAAA8D,QAAA,EAAC;kBAAoB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EAC7CvG,WAAW,CAACgD,GAAG,CAAC7D,IAAI,iBACnB3B,OAAA;oBAAmB2E,KAAK,EAAEhD,IAAK;oBAAA8G,QAAA,EAAE9G;kBAAI,GAAxBA,IAAI;oBAAAiH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAA6B,CAC/C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN/I,OAAA;cAAKmJ,KAAK,EAAE;gBAAEQ,OAAO,EAAE,MAAM;gBAAE8B,mBAAmB,EAAE,SAAS;gBAAEZ,GAAG,EAAE,EAAE;gBAAEf,YAAY,EAAE;cAAG,CAAE;cAAArB,QAAA,gBACzFzI,OAAA;gBAAAyI,QAAA,gBACEzI,OAAA;kBAAOmJ,KAAK,EAAE;oBAAEQ,OAAO,EAAE,OAAO;oBAAEG,YAAY,EAAE,CAAC;oBAAEmB,UAAU,EAAE,GAAG;oBAAEZ,KAAK,EAAE;kBAAO,CAAE;kBAAA5B,QAAA,EAAC;gBAErF;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR/I,OAAA;kBACE2B,IAAI,EAAC,MAAM;kBACX+C,IAAI,EAAC,cAAc;kBACnBC,KAAK,EAAEnD,QAAQ,CAACI,YAAa;kBAC7B4I,QAAQ,EAAEhG,iBAAkB;kBAC5B2E,KAAK,EAAE;oBACLsB,KAAK,EAAE,MAAM;oBACbnB,OAAO,EAAE,MAAM;oBACfqB,MAAM,EAAE,mBAAmB;oBAC3BD,YAAY,EAAE,CAAC;oBACftC,QAAQ,EAAE;kBACZ;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN/I,OAAA;gBAAAyI,QAAA,gBACEzI,OAAA;kBAAOmJ,KAAK,EAAE;oBAAEQ,OAAO,EAAE,OAAO;oBAAEG,YAAY,EAAE,CAAC;oBAAEmB,UAAU,EAAE,GAAG;oBAAEZ,KAAK,EAAE;kBAAO,CAAE;kBAAA5B,QAAA,EAAC;gBAErF;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR/I,OAAA;kBACE0E,IAAI,EAAC,QAAQ;kBACbC,KAAK,EAAEnD,QAAQ,CAACK,MAAO;kBACvB2I,QAAQ,EAAEhG,iBAAkB;kBAC5BkH,QAAQ;kBACRvC,KAAK,EAAE;oBACLsB,KAAK,EAAE,MAAM;oBACbnB,OAAO,EAAE,MAAM;oBACfqB,MAAM,EAAE,mBAAmB;oBAC3BD,YAAY,EAAE,CAAC;oBACftC,QAAQ,EAAE;kBACZ,CAAE;kBAAAK,QAAA,EAEDlG,aAAa,CAACiD,GAAG,CAAC3D,MAAM,iBACvB7B,OAAA;oBAAqB2E,KAAK,EAAE9C,MAAO;oBAAA4G,QAAA,EAAE5G;kBAAM,GAA9BA,MAAM;oBAAA+G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAiC,CACrD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN/I,OAAA;cAAKmJ,KAAK,EAAE;gBAAEQ,OAAO,EAAE,MAAM;gBAAE8B,mBAAmB,EAAE,SAAS;gBAAEZ,GAAG,EAAE,EAAE;gBAAEf,YAAY,EAAE;cAAG,CAAE;cAAArB,QAAA,gBACzFzI,OAAA;gBAAAyI,QAAA,gBACEzI,OAAA;kBAAOmJ,KAAK,EAAE;oBAAEQ,OAAO,EAAE,OAAO;oBAAEG,YAAY,EAAE,CAAC;oBAAEmB,UAAU,EAAE,GAAG;oBAAEZ,KAAK,EAAE;kBAAO,CAAE;kBAAA5B,QAAA,EAAC;gBAErF;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR/I,OAAA;kBACE2B,IAAI,EAAC,MAAM;kBACX+C,IAAI,EAAC,YAAY;kBACjBC,KAAK,EAAEnD,QAAQ,CAACM,UAAW;kBAC3B0I,QAAQ,EAAEhG,iBAAkB;kBAC5B2E,KAAK,EAAE;oBACLsB,KAAK,EAAE,MAAM;oBACbnB,OAAO,EAAE,MAAM;oBACfqB,MAAM,EAAE,mBAAmB;oBAC3BD,YAAY,EAAE,CAAC;oBACftC,QAAQ,EAAE;kBACZ;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN/I,OAAA;gBAAAyI,QAAA,gBACEzI,OAAA;kBAAOmJ,KAAK,EAAE;oBAAEQ,OAAO,EAAE,OAAO;oBAAEG,YAAY,EAAE,CAAC;oBAAEmB,UAAU,EAAE,GAAG;oBAAEZ,KAAK,EAAE;kBAAO,CAAE;kBAAA5B,QAAA,EAAC;gBAErF;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR/I,OAAA;kBACE0E,IAAI,EAAC,gBAAgB;kBACrBC,KAAK,EAAEnD,QAAQ,CAACO,cAAe;kBAC/ByI,QAAQ,EAAEhG,iBAAkB;kBAC5B2E,KAAK,EAAE;oBACLsB,KAAK,EAAE,MAAM;oBACbnB,OAAO,EAAE,MAAM;oBACfqB,MAAM,EAAE,mBAAmB;oBAC3BD,YAAY,EAAE,CAAC;oBACftC,QAAQ,EAAE;kBACZ,CAAE;kBAAAK,QAAA,gBAEFzI,OAAA;oBAAQ2E,KAAK,EAAC,EAAE;oBAAA8D,QAAA,EAAC;kBAA2B;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACpDjI,YAAY,CAAC0E,GAAG,CAACtC,WAAW,iBAC3BlD,OAAA;oBAA6B2E,KAAK,EAAEzB,WAAW,CAACwC,EAAG;oBAAA+C,QAAA,EAChDvF,WAAW,CAACxB;kBAAG,GADLwB,WAAW,CAACwC,EAAE;oBAAAkD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEnB,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN/I,OAAA;cAAKmJ,KAAK,EAAE;gBAAEQ,OAAO,EAAE,MAAM;gBAAE8B,mBAAmB,EAAE,SAAS;gBAAEZ,GAAG,EAAE,EAAE;gBAAEf,YAAY,EAAE;cAAG,CAAE;cAAArB,QAAA,gBACzFzI,OAAA;gBAAAyI,QAAA,gBACEzI,OAAA;kBAAOmJ,KAAK,EAAE;oBAAEQ,OAAO,EAAE,OAAO;oBAAEG,YAAY,EAAE,CAAC;oBAAEmB,UAAU,EAAE,GAAG;oBAAEZ,KAAK,EAAE;kBAAO,CAAE;kBAAA5B,QAAA,EAAC;gBAErF;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR/I,OAAA;kBACE2B,IAAI,EAAC,MAAM;kBACX+C,IAAI,EAAC,qBAAqB;kBAC1BC,KAAK,EAAEnD,QAAQ,CAACQ,mBAAoB;kBACpCwI,QAAQ,EAAEhG,iBAAkB;kBAC5B2E,KAAK,EAAE;oBACLsB,KAAK,EAAE,MAAM;oBACbnB,OAAO,EAAE,MAAM;oBACfqB,MAAM,EAAE,mBAAmB;oBAC3BD,YAAY,EAAE,CAAC;oBACftC,QAAQ,EAAE;kBACZ;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN/I,OAAA;gBAAAyI,QAAA,gBACEzI,OAAA;kBAAOmJ,KAAK,EAAE;oBAAEQ,OAAO,EAAE,OAAO;oBAAEG,YAAY,EAAE,CAAC;oBAAEmB,UAAU,EAAE,GAAG;oBAAEZ,KAAK,EAAE;kBAAO,CAAE;kBAAA5B,QAAA,EAAC;gBAErF;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR/I,OAAA;kBACE2B,IAAI,EAAC,MAAM;kBACX+C,IAAI,EAAC,mBAAmB;kBACxBC,KAAK,EAAEnD,QAAQ,CAACS,iBAAkB;kBAClCuI,QAAQ,EAAEhG,iBAAkB;kBAC5B2E,KAAK,EAAE;oBACLsB,KAAK,EAAE,MAAM;oBACbnB,OAAO,EAAE,MAAM;oBACfqB,MAAM,EAAE,mBAAmB;oBAC3BD,YAAY,EAAE,CAAC;oBACftC,QAAQ,EAAE;kBACZ;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN/I,OAAA;cAAKmJ,KAAK,EAAE;gBAAEQ,OAAO,EAAE,MAAM;gBAAE8B,mBAAmB,EAAE,aAAa;gBAAEZ,GAAG,EAAE,EAAE;gBAAEf,YAAY,EAAE;cAAG,CAAE;cAAArB,QAAA,gBAC7FzI,OAAA;gBAAAyI,QAAA,gBACEzI,OAAA;kBAAOmJ,KAAK,EAAE;oBAAEQ,OAAO,EAAE,OAAO;oBAAEG,YAAY,EAAE,CAAC;oBAAEmB,UAAU,EAAE,GAAG;oBAAEZ,KAAK,EAAE;kBAAO,CAAE;kBAAA5B,QAAA,EAAC;gBAErF;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR/I,OAAA;kBACE2B,IAAI,EAAC,QAAQ;kBACb+C,IAAI,EAAC,cAAc;kBACnBC,KAAK,EAAEnD,QAAQ,CAACW,YAAa;kBAC7BqI,QAAQ,EAAEhG,iBAAkB;kBAC5BuH,GAAG,EAAC,GAAG;kBACP5C,KAAK,EAAE;oBACLsB,KAAK,EAAE,MAAM;oBACbnB,OAAO,EAAE,MAAM;oBACfqB,MAAM,EAAE,mBAAmB;oBAC3BD,YAAY,EAAE,CAAC;oBACftC,QAAQ,EAAE;kBACZ;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN/I,OAAA;gBAAAyI,QAAA,gBACEzI,OAAA;kBAAOmJ,KAAK,EAAE;oBAAEQ,OAAO,EAAE,OAAO;oBAAEG,YAAY,EAAE,CAAC;oBAAEmB,UAAU,EAAE,GAAG;oBAAEZ,KAAK,EAAE;kBAAO,CAAE;kBAAA5B,QAAA,EAAC;gBAErF;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR/I,OAAA;kBACE2B,IAAI,EAAC,QAAQ;kBACb+C,IAAI,EAAC,WAAW;kBAChBC,KAAK,EAAEnD,QAAQ,CAACY,SAAU;kBAC1BoI,QAAQ,EAAEhG,iBAAkB;kBAC5BuH,GAAG,EAAC,GAAG;kBACP5C,KAAK,EAAE;oBACLsB,KAAK,EAAE,MAAM;oBACbnB,OAAO,EAAE,MAAM;oBACfqB,MAAM,EAAE,mBAAmB;oBAC3BD,YAAY,EAAE,CAAC;oBACftC,QAAQ,EAAE;kBACZ;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN/I,OAAA;gBAAAyI,QAAA,gBACEzI,OAAA;kBAAOmJ,KAAK,EAAE;oBAAEQ,OAAO,EAAE,OAAO;oBAAEG,YAAY,EAAE,CAAC;oBAAEmB,UAAU,EAAE,GAAG;oBAAEZ,KAAK,EAAE;kBAAO,CAAE;kBAAA5B,QAAA,EAAC;gBAErF;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR/I,OAAA;kBACE2B,IAAI,EAAC,QAAQ;kBACb+C,IAAI,EAAC,WAAW;kBAChBC,KAAK,EAAEnD,QAAQ,CAACa,SAAU;kBAC1BmI,QAAQ,EAAEhG,iBAAkB;kBAC5BuH,GAAG,EAAC,GAAG;kBACP5C,KAAK,EAAE;oBACLsB,KAAK,EAAE,MAAM;oBACbnB,OAAO,EAAE,MAAM;oBACfqB,MAAM,EAAE,mBAAmB;oBAC3BD,YAAY,EAAE,CAAC;oBACftC,QAAQ,EAAE;kBACZ;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN/I,OAAA;cAAKmJ,KAAK,EAAE;gBAAEW,YAAY,EAAE;cAAG,CAAE;cAAArB,QAAA,eAC/BzI,OAAA;gBAAOmJ,KAAK,EAAE;kBAAEQ,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEqB,UAAU,EAAE,GAAG;kBAAEZ,KAAK,EAAE,MAAM;kBAAES,MAAM,EAAE;gBAAU,CAAE;gBAAArC,QAAA,gBACzGzI,OAAA;kBACE2B,IAAI,EAAC,UAAU;kBACf+C,IAAI,EAAC,UAAU;kBACfE,OAAO,EAAEpD,QAAQ,CAACU,QAAS;kBAC3BsI,QAAQ,EAAEhG,iBAAkB;kBAC5B2E,KAAK,EAAE;oBAAEC,WAAW,EAAE,CAAC;oBAAEgB,SAAS,EAAE;kBAAa;gBAAE;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC,0BAEJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAEN/I,OAAA;cAAKmJ,KAAK,EAAE;gBAAEQ,OAAO,EAAE,MAAM;gBAAEkB,GAAG,EAAE,EAAE;gBAAEhB,cAAc,EAAE;cAAW,CAAE;cAAApB,QAAA,gBACnEzI,OAAA;gBACE2B,IAAI,EAAC,QAAQ;gBACb6H,OAAO,EAAEA,CAAA,KAAM;kBACb3I,YAAY,CAAC,KAAK,CAAC;kBACnBU,YAAY,CAAC,IAAI,CAAC;gBACpB,CAAE;gBACF4H,KAAK,EAAE;kBACLG,OAAO,EAAE,WAAW;kBACpBqB,MAAM,EAAE,mBAAmB;kBAC3BD,YAAY,EAAE,CAAC;kBACfM,UAAU,EAAE,OAAO;kBACnBX,KAAK,EAAE,MAAM;kBACbjC,QAAQ,EAAE,MAAM;kBAChB6C,UAAU,EAAE,GAAG;kBACfH,MAAM,EAAE;gBACV,CAAE;gBAAArC,QAAA,EACH;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT/I,OAAA;gBACE2B,IAAI,EAAC,QAAQ;gBACbqK,QAAQ,EAAE9K,UAAW;gBACrBiI,KAAK,EAAE;kBACLG,OAAO,EAAE,WAAW;kBACpBqB,MAAM,EAAE,MAAM;kBACdD,YAAY,EAAE,CAAC;kBACfM,UAAU,EAAE9J,UAAU,GAAG,MAAM,GAAG,kDAAkD;kBACpFmJ,KAAK,EAAE,OAAO;kBACdjC,QAAQ,EAAE,MAAM;kBAChB6C,UAAU,EAAE,GAAG;kBACfH,MAAM,EAAE5J,UAAU,GAAG,aAAa,GAAG,SAAS;kBAC9C0J,SAAS,EAAE;gBACb,CAAE;gBAAAnC,QAAA,EAEDvH,UAAU,GACNI,SAAS,GAAG,0BAA0B,GAAG,mBAAmB,GAC5DA,SAAS,GAAG,wBAAwB,GAAG;cAAwB;gBAAAsH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAvI,OAAO,gBACNR,OAAA;QAAKmJ,KAAK,EAAE;UAAEI,SAAS,EAAE,QAAQ;UAAED,OAAO,EAAE;QAAO,CAAE;QAAAb,QAAA,eACnDzI,OAAA;UAAAyI,QAAA,EAAG;QAA6B;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,GACJrI,KAAK,gBACPV,OAAA;QAAKmJ,KAAK,EAAE;UAAEI,SAAS,EAAE,QAAQ;UAAED,OAAO,EAAE,MAAM;UAAEe,KAAK,EAAE;QAAU,CAAE;QAAA5B,QAAA,gBACrEzI,OAAA;UAAAyI,QAAA,GAAG,8BAA4B,EAAC/H,KAAK;QAAA;UAAAkI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1C/I,OAAA;UACEwJ,OAAO,EAAEA,CAAA,KAAMvD,MAAM,CAAC9F,QAAQ,CAAC8L,MAAM,CAAC,CAAE;UACxC9C,KAAK,EAAE;YAAEE,SAAS,EAAE,MAAM;YAAEC,OAAO,EAAE,UAAU;YAAE0B,UAAU,EAAE,SAAS;YAAEX,KAAK,EAAE,OAAO;YAAEM,MAAM,EAAE,MAAM;YAAED,YAAY,EAAE,KAAK;YAAEI,MAAM,EAAE;UAAU,CAAE;UAAArC,QAAA,EAClJ;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,GACJ,CAAC7E,KAAK,CAACC,OAAO,CAAC7D,mBAAmB,CAAC,IAAIA,mBAAmB,CAAC2G,MAAM,KAAK,CAAC,gBACzEjH,OAAA;QAAKmJ,KAAK,EAAE;UAAEI,SAAS,EAAE,QAAQ;UAAED,OAAO,EAAE;QAAO,CAAE;QAAAb,QAAA,eACnDzI,OAAA;UAAAyI,QAAA,EAAIrH,UAAU,GAAG,uBAAuB,GAAG;QAAyB;UAAAwH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtE,CAAC,gBAEN/I,OAAA;QAAOwI,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAClCzI,OAAA;UAAAyI,QAAA,eACEzI,OAAA;YAAAyI,QAAA,gBACEzI,OAAA;cAAAyI,QAAA,EAAI;YAAG;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACZ/I,OAAA;cAAAyI,QAAA,EAAI;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACb/I,OAAA;cAAAyI,QAAA,EAAI;YAAe;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxB/I,OAAA;cAAAyI,QAAA,EAAI;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACf/I,OAAA;cAAAyI,QAAA,EAAI;YAAY;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrB/I,OAAA;cAAAyI,QAAA,EAAI;YAAiB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1B/I,OAAA;cAAAyI,QAAA,EAAI;YAAW;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpB/I,OAAA;cAAAyI,QAAA,EAAI;YAAO;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACR/I,OAAA;UAAAyI,QAAA,EACGnI,mBAAmB,CAACkF,GAAG,CAAC,CAACC,EAAE,EAAEyG,KAAK,KAAK;YAAA,IAAAC,gBAAA;YACtC,MAAMrG,WAAW,GAAGL,EAAE,CAAC7D,YAAY,IAAI6D,EAAE,CAACK,WAAW,IAAI,KAAK;YAC9D,MAAMsG,SAAS,GAAGhG,UAAU,CAACX,EAAE,CAAC3D,UAAU,CAAC;YAC3C,MAAMuK,eAAe,GAAGjG,UAAU,CAACX,EAAE,CAACxD,iBAAiB,CAAC;YACxD,MAAMiB,WAAW,GAAG,EAAAiJ,gBAAA,GAAA1G,EAAE,CAACvC,WAAW,cAAAiJ,gBAAA,uBAAdA,gBAAA,CAAgBzK,GAAG,KAAI+D,EAAE,CAAC1D,cAAc,IAAI,cAAc;YAE9E,oBACE/B,OAAA;cAAAyI,QAAA,gBACEzI,OAAA;gBAAAyI,QAAA,EAAKhD,EAAE,CAAC/D,GAAG,IAAI;cAAK;gBAAAkH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC1B/I,OAAA;gBAAAyI,QAAA,EAAKhD,EAAE,CAAC9D,IAAI,IAAI;cAAK;gBAAAiH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC3B/I,OAAA;gBAAAyI,QAAA,EAAK3C;cAAW;gBAAA8C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtB/I,OAAA;gBAAAyI,QAAA,EAAKhD,EAAE,CAAC5D,MAAM,IAAI;cAAK;gBAAA+G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC7B/I,OAAA;gBAAAyI,QAAA,EAAK2D;cAAS;gBAAAxD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACpB/I,OAAA;gBAAAyI,QAAA,EAAK4D;cAAe;gBAAAzD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC1B/I,OAAA;gBAAAyI,QAAA,EAAKvF;cAAW;gBAAA0F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtB/I,OAAA;gBAAAyI,QAAA,eACEzI,OAAA;kBAAKmJ,KAAK,EAAE;oBAAEQ,OAAO,EAAE,MAAM;oBAAEkB,GAAG,EAAE;kBAAM,CAAE;kBAAApC,QAAA,gBAC1CzI,OAAA;oBACEwJ,OAAO,EAAEA,CAAA,KAAM5D,UAAU,CAACH,EAAE,CAAE;oBAC9B0D,KAAK,EAAE;sBACL6B,UAAU,EAAE,SAAS;sBACrBX,KAAK,EAAE,OAAO;sBACdM,MAAM,EAAE,MAAM;sBACdD,YAAY,EAAE,KAAK;sBACnBpB,OAAO,EAAE,UAAU;sBACnBwB,MAAM,EAAE,SAAS;sBACjBnB,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpBiB,GAAG,EAAE;oBACP,CAAE;oBAAApC,QAAA,gBAEFzI,OAAA,CAAClB,eAAe;sBAACoK,IAAI,EAAEzJ;oBAAO;sBAAAmJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACjC/I,OAAA;sBAAAyI,QAAA,EAAM;oBAAQ;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,eACT/I,OAAA;oBACEwJ,OAAO,EAAEA,CAAA,KAAMxD,YAAY,CAACP,EAAE,CAACC,EAAE,CAAE;oBACnCyD,KAAK,EAAE;sBACL6B,UAAU,EAAE,SAAS;sBACrBX,KAAK,EAAE,OAAO;sBACdM,MAAM,EAAE,MAAM;sBACdD,YAAY,EAAE,KAAK;sBACnBpB,OAAO,EAAE,UAAU;sBACnBwB,MAAM,EAAE,SAAS;sBACjBnB,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpBiB,GAAG,EAAE;oBACP,CAAE;oBAAApC,QAAA,gBAEFzI,OAAA,CAAClB,eAAe;sBAACoK,IAAI,EAAExJ;oBAAQ;sBAAAkJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAClC/I,OAAA;sBAAAyI,QAAA,EAAM;oBAAS;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GA7CEtD,EAAE,CAACC,EAAE,IAAIwG,KAAK;cAAAtD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA8CnB,CAAC;UAET,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC7I,EAAA,CAt5BuBD,WAAW;EAAA,QAChBrB,WAAW;AAAA;AAAA0N,EAAA,GADNrM,WAAW;AAAA,IAAAqM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}