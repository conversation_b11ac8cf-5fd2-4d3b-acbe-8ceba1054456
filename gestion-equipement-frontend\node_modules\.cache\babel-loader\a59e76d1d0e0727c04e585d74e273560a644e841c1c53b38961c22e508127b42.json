{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\gestion-equipement-frontend\\\\src\\\\pages\\\\Equipements.js\",\n  _s = $RefreshSig$();\nimport './Equipements.css';\nimport { Link, useLocation } from 'react-router-dom';\nimport logo from '../assets/images/asment logo.jpg';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { faLaptop, faUsers, faExchangeAlt, faTruck, faUserCog, faSignOutAlt, faSearch, faBell, faTimes, faPlus } from '@fortawesome/free-solid-svg-icons';\nimport { useEffect, useState } from \"react\";\nimport jsPDF from \"jspdf\";\nimport \"jspdf-autotable\";\nimport * as XLSX from \"xlsx\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function Equipements() {\n  _s();\n  const location = useLocation();\n  const [equipements, setEquipements] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [showModal, setShowModal] = useState(false);\n  const [fournisseurs, setFournisseurs] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [submitting, setSubmitting] = useState(false);\n\n  // État du formulaire\n  const [formData, setFormData] = useState({\n    nom: '',\n    type: '',\n    numero_serie: '',\n    statut: 'DISPONIBLE',\n    date_achat: '',\n    fournisseur_id: '',\n    date_debut_garantie: '',\n    date_fin_garantie: '',\n    en_stock: true,\n    stock_actuel: 1,\n    stock_max: 1,\n    stock_min: 1,\n    categorie_id: ''\n  });\n\n  // Options pour les statuts et types\n  const statutOptions = ['DISPONIBLE', 'EN_MAINTENANCE', 'OCCUPE', 'HORS_SERVICE'];\n  const typeOptions = ['ORDINATEUR', 'IMPRIMANTE', 'SCANNER', 'TELEPHONE', 'TABLETTE', 'SERVEUR', 'AUTRE'];\n  useEffect(() => {\n    loadEquipements();\n    loadFournisseurs();\n    loadCategories();\n  }, []);\n  const loadEquipements = () => {\n    console.log(\"Chargement des équipements...\");\n    const token = localStorage.getItem('authToken');\n    const headers = {\n      'Content-Type': 'application/json'\n    };\n    if (token) {\n      headers['Authorization'] = `Bearer ${token}`;\n    }\n    fetch(\"http://localhost:8081/api/equipements\", {\n      method: 'GET',\n      headers: headers\n    }).then(res => {\n      console.log(\"Status de la réponse équipements:\", res.status);\n      if (!res.ok) {\n        throw new Error(`Erreur HTTP: ${res.status}`);\n      }\n      return res.json();\n    }).then(data => {\n      console.log(\"Données équipements reçues:\", data);\n      if (Array.isArray(data)) {\n        setEquipements(data);\n        setError(null);\n      } else {\n        console.warn(\"Les données équipements ne sont pas un tableau:\", data);\n        setEquipements([]);\n        setError(\"Format de données incorrect\");\n      }\n      setLoading(false);\n    }).catch(err => {\n      console.error(\"Erreur lors du chargement des équipements:\", err);\n      setEquipements([]);\n      setError(err.message || \"Erreur de chargement\");\n      setLoading(false);\n    });\n  };\n  const loadFournisseurs = () => {\n    const token = localStorage.getItem('authToken');\n    const headers = {\n      'Content-Type': 'application/json'\n    };\n    if (token) {\n      headers['Authorization'] = `Bearer ${token}`;\n    }\n    fetch(\"http://localhost:8081/api/fournisseurs\", {\n      method: 'GET',\n      headers: headers\n    }).then(res => res.ok ? res.json() : []).then(data => setFournisseurs(Array.isArray(data) ? data : [])).catch(err => {\n      console.error(\"Erreur lors du chargement des fournisseurs:\", err);\n      setFournisseurs([]);\n    });\n  };\n  const loadCategories = () => {\n    const token = localStorage.getItem('authToken');\n    const headers = {\n      'Content-Type': 'application/json'\n    };\n    if (token) {\n      headers['Authorization'] = `Bearer ${token}`;\n    }\n    fetch(\"http://localhost:8081/api/categories\", {\n      method: 'GET',\n      headers: headers\n    }).then(res => res.ok ? res.json() : []).then(data => setCategories(Array.isArray(data) ? data : [])).catch(err => {\n      console.error(\"Erreur lors du chargement des catégories:\", err);\n      setCategories([]);\n    });\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value,\n      type,\n      checked\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setSubmitting(true);\n\n    // Formatage des dates avant envoi\n    const dataToSend = {\n      ...formData,\n      date_achat: formData.date_achat || null,\n      date_debut_garantie: formData.date_debut_garantie || null,\n      date_fin_garantie: formData.date_fin_garantie || null,\n      fournisseur_id: formData.fournisseur_id || null\n    };\n    try {\n      const token = localStorage.getItem('authToken');\n      const headers = {\n        'Content-Type': 'application/json'\n      };\n      if (token) {\n        headers['Authorization'] = `Bearer ${token}`;\n      }\n      console.log(\"Données envoyées:\", dataToSend); // Ligne de debug utile\n\n      const response = await fetch(\"http://localhost:8081/api/equipements\", {\n        method: 'POST',\n        headers: headers,\n        body: JSON.stringify(dataToSend) // Ligne modifiée ici\n      });\n      if (!response.ok) {\n        throw new Error(`Erreur HTTP: ${response.status}`);\n      }\n      const newEquipement = await response.json();\n\n      // Ajouter le nouvel équipement à la liste\n      setEquipements(prev => [...prev, newEquipement]);\n\n      // Réinitialiser le formulaire et fermer le modal\n      setFormData({\n        nom: '',\n        type: '',\n        numero_serie: '',\n        statut: 'DISPONIBLE',\n        date_achat: '',\n        fournisseur_id: '',\n        date_debut_garantie: '',\n        date_fin_garantie: '',\n        en_stock: true,\n        stock_actuel: 1,\n        stock_max: 1,\n        stock_min: 1,\n        categorie_id: ''\n      });\n      setShowModal(false);\n      alert('Équipement ajouté avec succès !');\n    } catch (err) {\n      console.error(\"Erreur lors de l'ajout:\", err);\n      alert(`Erreur lors de l'ajout: ${err.message}`);\n    } finally {\n      setSubmitting(false);\n    }\n  };\n  const handleExportExcel = () => {\n    if (!Array.isArray(equipements) || equipements.length === 0) {\n      alert(\"Aucune donnée à exporter\");\n      return;\n    }\n    const columns = [\"Nom\", \"Type\", \"Numéro de série\", \"Statut\", \"Date d'achat\", \"Fournisseur\"];\n    const rows = equipements.map(eq => {\n      var _eq$fournisseur;\n      return [eq.nom || \"\", eq.type || \"\", eq.numero_serie || \"\", eq.statut || \"\", eq.date_achat || \"\", ((_eq$fournisseur = eq.fournisseur) === null || _eq$fournisseur === void 0 ? void 0 : _eq$fournisseur.nom) || \"\"];\n    });\n    const worksheet = XLSX.utils.aoa_to_sheet([columns, ...rows]);\n    const workbook = XLSX.utils.book_new();\n    XLSX.utils.book_append_sheet(workbook, worksheet, \"Equipements\");\n    XLSX.writeFile(workbook, \"equipements.xlsx\");\n  };\n  const handleExportPDF = () => {\n    if (!Array.isArray(equipements) || equipements.length === 0) {\n      alert(\"Aucune donnée à exporter\");\n      return;\n    }\n    const doc = new jsPDF();\n    doc.text(\"Liste des équipements\", 14, 16);\n    const columns = [\"Nom\", \"Type\", \"Numéro de série\", \"Statut\", \"Date d'achat\", \"Fournisseur\"];\n    const rows = equipements.map(eq => {\n      var _eq$fournisseur2;\n      return [eq.nom || \"\", eq.type || \"\", eq.numero_serie || \"\", eq.statut || \"\", eq.date_achat || \"\", ((_eq$fournisseur2 = eq.fournisseur) === null || _eq$fournisseur2 === void 0 ? void 0 : _eq$fournisseur2.nom) || \"\"];\n    });\n    doc.autoTable({\n      head: [columns],\n      body: rows,\n      startY: 22,\n      styles: {\n        fontSize: 10\n      }\n    });\n    doc.save(\"equipements.pdf\");\n  };\n  const handlePrint = () => {\n    window.print();\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"equipements-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sidebar\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"logo-section\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: logo,\n          alt: \"Logo Entreprise\",\n          className: \"company-logo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"main-menu\",\n        children: /*#__PURE__*/_jsxDEV(\"ul\", {\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/\",\n              className: location.pathname === '/' ? 'home-active active' : '',\n              children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: faLaptop,\n                style: {\n                  marginRight: 8\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 17\n              }, this), \" Accueil\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/equipements\",\n              className: location.pathname === '/equipements' ? 'active' : '',\n              children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: faLaptop,\n                style: {\n                  marginRight: 8\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 17\n              }, this), \" G\\xE9rer les \\xE9quipements\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/employes\",\n              className: location.pathname === '/employes' ? 'active' : '',\n              children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: faUsers,\n                style: {\n                  marginRight: 8\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 17\n              }, this), \" G\\xE9rer les employ\\xE9s\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/affectations\",\n              className: location.pathname === '/affectations' ? 'active' : '',\n              children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: faExchangeAlt,\n                style: {\n                  marginRight: 8\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 17\n              }, this), \" Suivi des affectations\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/fournisseurs\",\n              className: location.pathname === '/fournisseurs' ? 'active' : '',\n              children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: faTruck,\n                style: {\n                  marginRight: 8\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 17\n              }, this), \" G\\xE9rer les fournisseurs\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"secondary-links\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/profil\",\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faUserCog,\n            style: {\n              marginRight: 8\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 13\n          }, this), \" Mon profil & param\\xE8tres\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 307,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: 'auto',\n          padding: '20px 0 0 0',\n          textAlign: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"logout-btn\",\n          onClick: () => {\n            localStorage.removeItem('authToken');\n            window.location.href = '/';\n          },\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faSignOutAlt,\n            style: {\n              marginRight: 8\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 13\n          }, this), \" D\\xE9connexion\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 312,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 274,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"equipements-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-header-row\",\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between',\n          marginBottom: 32\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-bar\",\n          style: {\n            flex: 1,\n            maxWidth: 480,\n            marginRight: 24,\n            position: 'relative'\n          },\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faSearch,\n            style: {\n              position: 'absolute',\n              left: 14,\n              top: '50%',\n              transform: 'translateY(-50%)',\n              color: '#1976d2',\n              fontSize: '1.1rem',\n              opacity: 0.8\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Recherche par mot cl\\xE9\",\n            style: {\n              width: '100%',\n              padding: '10px 16px 10px 38px',\n              borderRadius: 8,\n              border: '1px solid #dbeafe',\n              fontSize: '1rem',\n              boxShadow: '0 2px 8px rgba(21,101,192,0.06)'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"profile-block\",\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 20\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"notification-icon\",\n            style: {\n              position: 'relative',\n              marginRight: 8,\n              cursor: 'pointer'\n            },\n            children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n              icon: faBell,\n              style: {\n                fontSize: '1.3rem',\n                color: '#1976d2'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                position: 'absolute',\n                top: -6,\n                right: -6,\n                background: '#e74a3b',\n                color: '#fff',\n                borderRadius: '50%',\n                fontSize: '0.7rem',\n                padding: '2px 6px',\n                fontWeight: 600\n              },\n              children: \"3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              fontWeight: 500,\n              color: '#2e3a4e',\n              fontSize: '1rem',\n              marginRight: 8\n            },\n            children: \"Responsable IT\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faUserCog,\n            style: {\n              fontSize: '1.5rem',\n              color: '#1976d2',\n              background: '#fff',\n              borderRadius: '50%',\n              padding: 6,\n              boxShadow: '0 2px 8px rgba(21,101,192,0.10)'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 328,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"equipements-title\",\n        children: [\"Liste des \\xE9quipements (\", Array.isArray(equipements) ? equipements.length : 0, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: 16,\n          marginBottom: 24\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          style: {\n            fontWeight: 700,\n            fontSize: '1rem',\n            borderRadius: 10,\n            background: 'linear-gradient(90deg, #1976d2 0%, #42a5f5 100%)',\n            color: '#fff',\n            boxShadow: '0 4px 16px rgba(21,101,192,0.18)'\n          },\n          onClick: () => setShowModal(true),\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faPlus,\n            style: {\n              marginRight: 8\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 13\n          }, this), \"Ajouter un \\xE9quipement\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          style: {\n            fontWeight: 700,\n            fontSize: '1rem',\n            borderRadius: 10,\n            background: 'linear-gradient(90deg, #43a047 0%, #66bb6a 100%)',\n            color: '#fff',\n            boxShadow: '0 4px 16px rgba(67,160,71,0.18)'\n          },\n          onClick: handleExportExcel,\n          children: \"Exporter Excel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          style: {\n            fontWeight: 700,\n            fontSize: '1rem',\n            borderRadius: 10,\n            background: 'linear-gradient(90deg, #d32f2f 0%, #e57373 100%)',\n            color: '#fff',\n            boxShadow: '0 4px 16px rgba(211,47,47,0.18)'\n          },\n          onClick: handleExportPDF,\n          children: \"Exporter PDF\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 363,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          style: {\n            fontWeight: 700,\n            fontSize: '1rem',\n            borderRadius: 10,\n            background: 'linear-gradient(90deg, #ffa000 0%, #ffd54f 100%)',\n            color: '#fff',\n            boxShadow: '0 4px 16px rgba(255,160,0,0.18)'\n          },\n          onClick: handlePrint,\n          children: \"Imprimer\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 351,\n        columnNumber: 9\n      }, this), showModal && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          backgroundColor: 'rgba(0, 0, 0, 0.5)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 1000\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            backgroundColor: 'white',\n            borderRadius: 12,\n            padding: 32,\n            maxWidth: 600,\n            width: '90%',\n            maxHeight: '90vh',\n            overflowY: 'auto',\n            boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              marginBottom: 24\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                margin: 0,\n                color: '#1976d2',\n                fontSize: '1.5rem',\n                fontWeight: 700\n              },\n              children: \"Ajouter un nouvel \\xE9quipement\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowModal(false),\n              style: {\n                background: 'none',\n                border: 'none',\n                fontSize: '1.5rem',\n                color: '#666',\n                cursor: 'pointer',\n                padding: 8,\n                borderRadius: '50%'\n              },\n              children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: faTimes\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 411,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 395,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSubmit,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: '1fr 1fr',\n                gap: 16,\n                marginBottom: 16\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: 8,\n                    fontWeight: 600,\n                    color: '#333'\n                  },\n                  children: \"Nom de l'\\xE9quipement *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 418,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"nom\",\n                  value: formData.nom,\n                  onChange: handleInputChange,\n                  required: true,\n                  style: {\n                    width: '100%',\n                    padding: '12px',\n                    border: '2px solid #e0e0e0',\n                    borderRadius: 8,\n                    fontSize: '1rem',\n                    transition: 'border-color 0.2s'\n                  },\n                  onFocus: e => e.target.style.borderColor = '#1976d2',\n                  onBlur: e => e.target.style.borderColor = '#e0e0e0'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 421,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: 8,\n                    fontWeight: 600,\n                    color: '#333'\n                  },\n                  children: \"Type *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 441,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"type\",\n                  value: formData.type,\n                  onChange: handleInputChange,\n                  required: true,\n                  style: {\n                    width: '100%',\n                    padding: '12px',\n                    border: '2px solid #e0e0e0',\n                    borderRadius: 8,\n                    fontSize: '1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"S\\xE9lectionner un type\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 457,\n                    columnNumber: 23\n                  }, this), typeOptions.map(type => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: type,\n                    children: type\n                  }, type, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 459,\n                    columnNumber: 25\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 444,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 440,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 416,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: '1fr 1fr',\n                gap: 16,\n                marginBottom: 16\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: 8,\n                    fontWeight: 600,\n                    color: '#333'\n                  },\n                  children: \"Num\\xE9ro de s\\xE9rie\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 467,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"numero_serie\",\n                  value: formData.numero_serie,\n                  onChange: handleInputChange,\n                  style: {\n                    width: '100%',\n                    padding: '12px',\n                    border: '2px solid #e0e0e0',\n                    borderRadius: 8,\n                    fontSize: '1rem'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 470,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 466,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: 8,\n                    fontWeight: 600,\n                    color: '#333'\n                  },\n                  children: \"Statut *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 486,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"statut\",\n                  value: formData.statut,\n                  onChange: handleInputChange,\n                  required: true,\n                  style: {\n                    width: '100%',\n                    padding: '12px',\n                    border: '2px solid #e0e0e0',\n                    borderRadius: 8,\n                    fontSize: '1rem'\n                  },\n                  children: statutOptions.map(statut => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: statut,\n                    children: statut\n                  }, statut, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 503,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 489,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 485,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 465,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: '1fr 1fr',\n                gap: 16,\n                marginBottom: 16\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: 8,\n                    fontWeight: 600,\n                    color: '#333'\n                  },\n                  children: \"Date d'achat\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 511,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  name: \"date_achat\",\n                  value: formData.date_achat,\n                  onChange: handleInputChange,\n                  style: {\n                    width: '100%',\n                    padding: '12px',\n                    border: '2px solid #e0e0e0',\n                    borderRadius: 8,\n                    fontSize: '1rem'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 514,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 510,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: 8,\n                    fontWeight: 600,\n                    color: '#333'\n                  },\n                  children: \"Fournisseur\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 530,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"fournisseur_id\",\n                  value: formData.fournisseur_id,\n                  onChange: handleInputChange,\n                  style: {\n                    width: '100%',\n                    padding: '12px',\n                    border: '2px solid #e0e0e0',\n                    borderRadius: 8,\n                    fontSize: '1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"S\\xE9lectionner un fournisseur\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 545,\n                    columnNumber: 23\n                  }, this), fournisseurs.map(fournisseur => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: fournisseur.id,\n                    children: fournisseur.nom\n                  }, fournisseur.id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 547,\n                    columnNumber: 25\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 533,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 529,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 509,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: '1fr 1fr',\n                gap: 16,\n                marginBottom: 16\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: 8,\n                    fontWeight: 600,\n                    color: '#333'\n                  },\n                  children: \"D\\xE9but de garantie\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 557,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  name: \"date_debut_garantie\",\n                  value: formData.date_debut_garantie,\n                  onChange: handleInputChange,\n                  style: {\n                    width: '100%',\n                    padding: '12px',\n                    border: '2px solid #e0e0e0',\n                    borderRadius: 8,\n                    fontSize: '1rem'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 560,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 556,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: 8,\n                    fontWeight: 600,\n                    color: '#333'\n                  },\n                  children: \"Fin de garantie\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 576,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  name: \"date_fin_garantie\",\n                  value: formData.date_fin_garantie,\n                  onChange: handleInputChange,\n                  style: {\n                    width: '100%',\n                    padding: '12px',\n                    border: '2px solid #e0e0e0',\n                    borderRadius: 8,\n                    fontSize: '1rem'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 579,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 575,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 555,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: '1fr 1fr 1fr',\n                gap: 16,\n                marginBottom: 16\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: 8,\n                    fontWeight: 600,\n                    color: '#333'\n                  },\n                  children: \"Stock actuel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 597,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  name: \"stock_actuel\",\n                  value: formData.stock_actuel,\n                  onChange: handleInputChange,\n                  min: \"0\",\n                  style: {\n                    width: '100%',\n                    padding: '12px',\n                    border: '2px solid #e0e0e0',\n                    borderRadius: 8,\n                    fontSize: '1rem'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 600,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 596,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: 8,\n                    fontWeight: 600,\n                    color: '#333'\n                  },\n                  children: \"Stock maximum\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 617,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  name: \"stock_max\",\n                  value: formData.stock_max,\n                  onChange: handleInputChange,\n                  min: \"1\",\n                  style: {\n                    width: '100%',\n                    padding: '12px',\n                    border: '2px solid #e0e0e0',\n                    borderRadius: 8,\n                    fontSize: '1rem'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 620,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 616,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: 8,\n                    fontWeight: 600,\n                    color: '#333'\n                  },\n                  children: \"Stock minimum\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 637,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  name: \"stock_min\",\n                  value: formData.stock_min,\n                  onChange: handleInputChange,\n                  min: \"0\",\n                  style: {\n                    width: '100%',\n                    padding: '12px',\n                    border: '2px solid #e0e0e0',\n                    borderRadius: 8,\n                    fontSize: '1rem'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 640,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 636,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 595,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: 24\n              },\n              children: /*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  fontWeight: 600,\n                  color: '#333',\n                  cursor: 'pointer'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  name: \"en_stock\",\n                  checked: formData.en_stock,\n                  onChange: handleInputChange,\n                  style: {\n                    marginRight: 8,\n                    transform: 'scale(1.2)'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 659,\n                  columnNumber: 21\n                }, this), \"\\xC9quipement en stock\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 658,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 657,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                gap: 12,\n                justifyContent: 'flex-end'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => setShowModal(false),\n                style: {\n                  padding: '12px 24px',\n                  border: '2px solid #e0e0e0',\n                  borderRadius: 8,\n                  background: 'white',\n                  color: '#666',\n                  fontSize: '1rem',\n                  fontWeight: 600,\n                  cursor: 'pointer'\n                },\n                children: \"Annuler\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 671,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                disabled: submitting,\n                style: {\n                  padding: '12px 24px',\n                  border: 'none',\n                  borderRadius: 8,\n                  background: submitting ? '#ccc' : 'linear-gradient(90deg, #1976d2 0%, #42a5f5 100%)',\n                  color: 'white',\n                  fontSize: '1rem',\n                  fontWeight: 600,\n                  cursor: submitting ? 'not-allowed' : 'pointer',\n                  boxShadow: '0 4px 16px rgba(21,101,192,0.18)'\n                },\n                children: submitting ? 'Ajout en cours...' : 'Ajouter l\\'équipement'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 687,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 670,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 385,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 373,\n        columnNumber: 11\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '40px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Chargement des \\xE9quipements...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 713,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 712,\n        columnNumber: 11\n      }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '40px',\n          color: '#e74a3b'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"Erreur lors du chargement : \", error]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 717,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.location.reload(),\n          style: {\n            marginTop: '10px',\n            padding: '8px 16px',\n            background: '#1976d2',\n            color: 'white',\n            border: 'none',\n            borderRadius: '4px',\n            cursor: 'pointer'\n          },\n          children: \"Actualiser la page\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 718,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 716,\n        columnNumber: 11\n      }, this) : !Array.isArray(equipements) || equipements.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '40px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Aucun \\xE9quipement trouv\\xE9\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 727,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 726,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"table\", {\n        className: \"equipements-table\",\n        children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Nom\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 733,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Type\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 734,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Num\\xE9ro de s\\xE9rie\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 735,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Statut\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 736,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Date d'achat\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 737,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Fournisseur\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 738,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 732,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 731,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n          children: equipements.map((eq, index) => {\n            var _eq$fournisseur3;\n            // Formatage des données avec vérifications plus robustes\n            const numeroSerie = eq.numero_serie || eq.numeroSerie || 'N/A';\n\n            // Fonction pour formater une date\n            const formatDate = dateString => {\n              if (!dateString) return 'N/A';\n              try {\n                const dateObj = new Date(dateString);\n                return isNaN(dateObj.getTime()) ? 'N/A' : dateObj.toLocaleDateString('fr-FR', {\n                  year: 'numeric',\n                  month: '2-digit',\n                  day: '2-digit'\n                });\n              } catch (e) {\n                console.error(\"Erreur de formatage de date\", e);\n                return 'N/A';\n              }\n            };\n            const dateAchat = formatDate(eq.date_achat);\n            const dateDebutGarantie = formatDate(eq.date_debut_garantie);\n            const dateFinGarantie = formatDate(eq.date_fin_garantie);\n            const fournisseur = ((_eq$fournisseur3 = eq.fournisseur) === null || _eq$fournisseur3 === void 0 ? void 0 : _eq$fournisseur3.nom) || eq.fournisseur_id || 'Non spécifié';\n            return /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                children: eq.nom || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 772,\n                columnNumber: 9\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: eq.type || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 773,\n                columnNumber: 9\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: numeroSerie\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 774,\n                columnNumber: 9\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: eq.statut || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 775,\n                columnNumber: 9\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: dateDebutGarantie\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 776,\n                columnNumber: 9\n              }, this), \" \", /*#__PURE__*/_jsxDEV(\"td\", {\n                children: dateFinGarantie\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 777,\n                columnNumber: 9\n              }, this), \" \", /*#__PURE__*/_jsxDEV(\"td\", {\n                children: fournisseur\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 778,\n                columnNumber: 9\n              }, this)]\n            }, eq.id || index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 771,\n              columnNumber: 7\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 741,\n          columnNumber: 7\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 730,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 326,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 272,\n    columnNumber: 5\n  }, this);\n}\n_s(Equipements, \"IokgRn1z3gJZHw8Gh1OYI+2IqVk=\", false, function () {\n  return [useLocation];\n});\n_c = Equipements;\nvar _c;\n$RefreshReg$(_c, \"Equipements\");", "map": {"version": 3, "names": ["Link", "useLocation", "logo", "FontAwesomeIcon", "faLaptop", "faUsers", "faExchangeAlt", "faTruck", "faUserCog", "faSignOutAlt", "faSearch", "faBell", "faTimes", "faPlus", "useEffect", "useState", "jsPDF", "XLSX", "jsxDEV", "_jsxDEV", "Equipements", "_s", "location", "equipements", "setEquipements", "loading", "setLoading", "error", "setError", "showModal", "setShowModal", "fournisseurs", "setFournisseurs", "categories", "setCategories", "submitting", "setSubmitting", "formData", "setFormData", "nom", "type", "numero_serie", "statut", "date_achat", "fournisseur_id", "date_debut_garantie", "date_fin_garantie", "en_stock", "stock_actuel", "stock_max", "stock_min", "categorie_id", "statutOptions", "typeOptions", "loadEquipements", "loadFournisseurs", "loadCategories", "console", "log", "token", "localStorage", "getItem", "headers", "fetch", "method", "then", "res", "status", "ok", "Error", "json", "data", "Array", "isArray", "warn", "catch", "err", "message", "handleInputChange", "e", "name", "value", "checked", "target", "prev", "handleSubmit", "preventDefault", "dataToSend", "response", "body", "JSON", "stringify", "newEquipement", "alert", "handleExportExcel", "length", "columns", "rows", "map", "eq", "_eq$fournisseur", "<PERSON><PERSON><PERSON><PERSON>", "worksheet", "utils", "aoa_to_sheet", "workbook", "book_new", "book_append_sheet", "writeFile", "handleExportPDF", "doc", "text", "_eq$fournisseur2", "autoTable", "head", "startY", "styles", "fontSize", "save", "handlePrint", "window", "print", "className", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "pathname", "icon", "style", "marginRight", "marginTop", "padding", "textAlign", "onClick", "removeItem", "href", "display", "alignItems", "justifyContent", "marginBottom", "flex", "max<PERSON><PERSON><PERSON>", "position", "left", "top", "transform", "color", "opacity", "placeholder", "width", "borderRadius", "border", "boxShadow", "gap", "cursor", "right", "background", "fontWeight", "bottom", "backgroundColor", "zIndex", "maxHeight", "overflowY", "margin", "onSubmit", "gridTemplateColumns", "onChange", "required", "transition", "onFocus", "borderColor", "onBlur", "id", "min", "disabled", "reload", "index", "_eq$fournisseur3", "numeroSerie", "formatDate", "dateString", "date<PERSON><PERSON>j", "Date", "isNaN", "getTime", "toLocaleDateString", "year", "month", "day", "dateAchat", "dateDebutGarantie", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/gestion-equipement-frontend/src/pages/Equipements.js"], "sourcesContent": ["import './Equipements.css';\nimport { Link, useLocation } from 'react-router-dom';\nimport logo from '../assets/images/asment logo.jpg';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { faLaptop, faUsers, faExchangeAlt, faTruck, faUserCog, faSignOutAlt, faSearch, faBell, faTimes, faPlus } from '@fortawesome/free-solid-svg-icons';\nimport { useEffect, useState } from \"react\";\nimport jsPDF from \"jspdf\";\nimport \"jspdf-autotable\";\nimport * as XLSX from \"xlsx\";\n\nexport default function Equipements() {\n  const location = useLocation();\n  const [equipements, setEquipements] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [showModal, setShowModal] = useState(false);\n  const [fournisseurs, setFournisseurs] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [submitting, setSubmitting] = useState(false);\n\n  // État du formulaire\n  const [formData, setFormData] = useState({\n    nom: '',\n    type: '',\n    numero_serie: '',\n    statut: 'DISPONIBLE',\n    date_achat: '',\n    fournisseur_id: '',\n    date_debut_garantie: '',\n    date_fin_garantie: '',\n    en_stock: true,\n    stock_actuel: 1,\n    stock_max: 1,\n    stock_min: 1,\n    categorie_id: ''\n  });\n\n  // Options pour les statuts et types\n  const statutOptions = ['DISPONIBLE', 'EN_MAINTENANCE', 'OCCUPE', 'HORS_SERVICE'];\n  const typeOptions = ['ORDINATEUR', 'IMPRIMANTE', 'SCANNER', 'TELEPHONE', 'TABLETTE', 'SERVEUR', 'AUTRE'];\n\n  useEffect(() => {\n    loadEquipements();\n    loadFournisseurs();\n    loadCategories();\n  }, []);\n\n  const loadEquipements = () => {\n    console.log(\"Chargement des équipements...\");\n    \n    const token = localStorage.getItem('authToken');\n    const headers = {\n      'Content-Type': 'application/json',\n    };\n    if (token) {\n      headers['Authorization'] = `Bearer ${token}`;\n    }\n\n    fetch(\"http://localhost:8081/api/equipements\", {\n      method: 'GET',\n      headers: headers\n    })\n      .then(res => {\n        console.log(\"Status de la réponse équipements:\", res.status);\n        if (!res.ok) {\n          throw new Error(`Erreur HTTP: ${res.status}`);\n        }\n        return res.json();\n      })\n      .then(data => {\n        console.log(\"Données équipements reçues:\", data);\n        if (Array.isArray(data)) {\n          setEquipements(data);\n          setError(null);\n        } else {\n          console.warn(\"Les données équipements ne sont pas un tableau:\", data);\n          setEquipements([]);\n          setError(\"Format de données incorrect\");\n        }\n        setLoading(false);\n      })\n      .catch(err => {\n        console.error(\"Erreur lors du chargement des équipements:\", err);\n        setEquipements([]);\n        setError(err.message || \"Erreur de chargement\");\n        setLoading(false);\n      });\n  };\n\n  const loadFournisseurs = () => {\n    const token = localStorage.getItem('authToken');\n    const headers = {\n      'Content-Type': 'application/json',\n    };\n    if (token) {\n      headers['Authorization'] = `Bearer ${token}`;\n    }\n\n    fetch(\"http://localhost:8081/api/fournisseurs\", {\n      method: 'GET',\n      headers: headers\n    })\n      .then(res => res.ok ? res.json() : [])\n      .then(data => setFournisseurs(Array.isArray(data) ? data : []))\n      .catch(err => {\n        console.error(\"Erreur lors du chargement des fournisseurs:\", err);\n        setFournisseurs([]);\n      });\n  };\n\n  const loadCategories = () => {\n    const token = localStorage.getItem('authToken');\n    const headers = {\n      'Content-Type': 'application/json',\n    };\n    if (token) {\n      headers['Authorization'] = `Bearer ${token}`;\n    }\n\n    fetch(\"http://localhost:8081/api/categories\", {\n      method: 'GET',\n      headers: headers\n    })\n      .then(res => res.ok ? res.json() : [])\n      .then(data => setCategories(Array.isArray(data) ? data : []))\n      .catch(err => {\n        console.error(\"Erreur lors du chargement des catégories:\", err);\n        setCategories([]);\n      });\n  };\n\n  const handleInputChange = (e) => {\n    const { name, value, type, checked } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n  };\n\n const handleSubmit = async (e) => {\n  e.preventDefault();\n  setSubmitting(true);\n\n  // Formatage des dates avant envoi\n  const dataToSend = {\n    ...formData,\n    date_achat: formData.date_achat || null,\n    date_debut_garantie: formData.date_debut_garantie || null,\n    date_fin_garantie: formData.date_fin_garantie || null,\n    fournisseur_id: formData.fournisseur_id || null\n  };\n\n  try {\n    const token = localStorage.getItem('authToken');\n    const headers = {\n      'Content-Type': 'application/json',\n    };\n    if (token) {\n      headers['Authorization'] = `Bearer ${token}`;\n    }\n\n    console.log(\"Données envoyées:\", dataToSend); // Ligne de debug utile\n\n    const response = await fetch(\"http://localhost:8081/api/equipements\", {\n      method: 'POST',\n      headers: headers,\n      body: JSON.stringify(dataToSend) // Ligne modifiée ici\n    });\n\n      if (!response.ok) {\n        throw new Error(`Erreur HTTP: ${response.status}`);\n      }\n\n      const newEquipement = await response.json();\n      \n      // Ajouter le nouvel équipement à la liste\n      setEquipements(prev => [...prev, newEquipement]);\n      \n      // Réinitialiser le formulaire et fermer le modal\n      setFormData({\n        nom: '',\n        type: '',\n        numero_serie: '',\n        statut: 'DISPONIBLE',\n        date_achat: '',\n        fournisseur_id: '',\n        date_debut_garantie: '',\n        date_fin_garantie: '',\n        en_stock: true,\n        stock_actuel: 1,\n        stock_max: 1,\n        stock_min: 1,\n        categorie_id: ''\n      });\n      setShowModal(false);\n      \n      alert('Équipement ajouté avec succès !');\n    } catch (err) {\n      console.error(\"Erreur lors de l'ajout:\", err);\n      alert(`Erreur lors de l'ajout: ${err.message}`);\n    } finally {\n      setSubmitting(false);\n    }\n  };\n\n  const handleExportExcel = () => {\n    if (!Array.isArray(equipements) || equipements.length === 0) {\n      alert(\"Aucune donnée à exporter\");\n      return;\n    }\n    \n    const columns = [\n      \"Nom\",\n      \"Type\",\n      \"Numéro de série\",\n      \"Statut\",\n      \"Date d'achat\",\n      \"Fournisseur\"\n    ];\n    const rows = equipements.map(eq => [\n      eq.nom || \"\",\n      eq.type || \"\",\n      eq.numero_serie || \"\",\n      eq.statut || \"\",\n      eq.date_achat || \"\",\n      eq.fournisseur?.nom || \"\"\n    ]);\n    const worksheet = XLSX.utils.aoa_to_sheet([columns, ...rows]);\n    const workbook = XLSX.utils.book_new();\n    XLSX.utils.book_append_sheet(workbook, worksheet, \"Equipements\");\n    XLSX.writeFile(workbook, \"equipements.xlsx\");\n  };\n\n  const handleExportPDF = () => {\n    if (!Array.isArray(equipements) || equipements.length === 0) {\n      alert(\"Aucune donnée à exporter\");\n      return;\n    }\n    \n    const doc = new jsPDF();\n    doc.text(\"Liste des équipements\", 14, 16);\n    const columns = [\n      \"Nom\",\n      \"Type\",\n      \"Numéro de série\",\n      \"Statut\",\n      \"Date d'achat\",\n      \"Fournisseur\"\n    ];\n    const rows = equipements.map(eq => [\n      eq.nom || \"\",\n      eq.type || \"\",\n      eq.numero_serie || \"\",\n      eq.statut || \"\",\n      eq.date_achat || \"\",\n      eq.fournisseur?.nom || \"\"\n    ]);\n    doc.autoTable({\n      head: [columns],\n      body: rows,\n      startY: 22,\n      styles: { fontSize: 10 }\n    });\n    doc.save(\"equipements.pdf\");\n  };\n\n  const handlePrint = () => {\n    window.print();\n  };\n\n  return (\n    <div className=\"equipements-container\">\n      {/* Sidebar */}\n      <div className=\"sidebar\">\n        <div className=\"logo-section\">\n          <img src={logo} alt=\"Logo Entreprise\" className=\"company-logo\" />\n        </div>\n        <nav className=\"main-menu\">\n          <ul>\n            <li>\n              <Link to=\"/\" className={location.pathname === '/' ? 'home-active active' : ''}>\n                <FontAwesomeIcon icon={faLaptop} style={{marginRight:8}} /> Accueil\n              </Link>\n            </li>\n            <li>\n              <Link to=\"/equipements\" className={location.pathname === '/equipements' ? 'active' : ''}>\n                <FontAwesomeIcon icon={faLaptop} style={{marginRight:8}} /> Gérer les équipements\n              </Link>\n            </li>\n            <li>\n              <Link to=\"/employes\" className={location.pathname === '/employes' ? 'active' : ''}>\n                <FontAwesomeIcon icon={faUsers} style={{marginRight:8}} /> Gérer les employés\n              </Link>\n            </li>\n            <li>\n              <Link to=\"/affectations\" className={location.pathname === '/affectations' ? 'active' : ''}>\n                <FontAwesomeIcon icon={faExchangeAlt} style={{marginRight:8}} /> Suivi des affectations\n              </Link>\n            </li>\n            <li>\n              <Link to=\"/fournisseurs\" className={location.pathname === '/fournisseurs' ? 'active' : ''}>\n                <FontAwesomeIcon icon={faTruck} style={{marginRight:8}} /> Gérer les fournisseurs\n              </Link>\n            </li>\n          </ul>\n        </nav>\n        <div className=\"secondary-links\">\n          <Link to=\"/profil\">\n            <FontAwesomeIcon icon={faUserCog} style={{marginRight:8}} /> Mon profil & paramètres\n          </Link>\n        </div>\n        <div style={{ marginTop: 'auto', padding: '20px 0 0 0', textAlign: 'center' }}>\n          <button\n            className=\"logout-btn\"\n            onClick={() => {\n              localStorage.removeItem('authToken');\n              window.location.href = '/';\n            }}\n          >\n            <FontAwesomeIcon icon={faSignOutAlt} style={{marginRight:8}} /> Déconnexion\n          </button>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"equipements-content\">\n        {/* Dashboard header row */}\n        <div className=\"dashboard-header-row\" style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: 32 }}>\n          <div className=\"search-bar\" style={{ flex: 1, maxWidth: 480, marginRight: 24, position: 'relative' }}>\n            <FontAwesomeIcon icon={faSearch} style={{ position: 'absolute', left: 14, top: '50%', transform: 'translateY(-50%)', color: '#1976d2', fontSize: '1.1rem', opacity: 0.8 }} />\n            <input\n              type=\"text\"\n              placeholder=\"Recherche par mot clé\"\n              style={{ width: '100%', padding: '10px 16px 10px 38px', borderRadius: 8, border: '1px solid #dbeafe', fontSize: '1rem', boxShadow: '0 2px 8px rgba(21,101,192,0.06)' }}\n            />\n          </div>\n          <div className=\"profile-block\" style={{ display: 'flex', alignItems: 'center', gap: 20 }}>\n            <div className=\"notification-icon\" style={{ position: 'relative', marginRight: 8, cursor: 'pointer' }}>\n              <FontAwesomeIcon icon={faBell} style={{ fontSize: '1.3rem', color: '#1976d2' }} />\n              <span style={{ position: 'absolute', top: -6, right: -6, background: '#e74a3b', color: '#fff', borderRadius: '50%', fontSize: '0.7rem', padding: '2px 6px', fontWeight: 600 }}>3</span>\n            </div>\n            <span style={{ fontWeight: 500, color: '#2e3a4e', fontSize: '1rem', marginRight: 8 }}>Responsable IT</span>\n            <FontAwesomeIcon icon={faUserCog} style={{ fontSize: '1.5rem', color: '#1976d2', background: '#fff', borderRadius: '50%', padding: 6, boxShadow: '0 2px 8px rgba(21,101,192,0.10)' }} />\n          </div>\n        </div>\n\n        <h2 className=\"equipements-title\">\n          Liste des équipements ({Array.isArray(equipements) ? equipements.length : 0})\n        </h2>\n        \n        <div style={{ display: 'flex', gap: 16, marginBottom: 24 }}>\n          <button \n            className=\"btn btn-primary\" \n            style={{ fontWeight: 700, fontSize: '1rem', borderRadius: 10, background: 'linear-gradient(90deg, #1976d2 0%, #42a5f5 100%)', color: '#fff', boxShadow: '0 4px 16px rgba(21,101,192,0.18)' }}\n            onClick={() => setShowModal(true)}\n          >\n            <FontAwesomeIcon icon={faPlus} style={{marginRight: 8}} />\n            Ajouter un équipement\n          </button>\n          <button className=\"btn btn-primary\" style={{ fontWeight: 700, fontSize: '1rem', borderRadius: 10, background: 'linear-gradient(90deg, #43a047 0%, #66bb6a 100%)', color: '#fff', boxShadow: '0 4px 16px rgba(67,160,71,0.18)' }} onClick={handleExportExcel}>\n            Exporter Excel\n          </button>\n          <button className=\"btn btn-primary\" style={{ fontWeight: 700, fontSize: '1rem', borderRadius: 10, background: 'linear-gradient(90deg, #d32f2f 0%, #e57373 100%)', color: '#fff', boxShadow: '0 4px 16px rgba(211,47,47,0.18)' }} onClick={handleExportPDF}>\n            Exporter PDF\n          </button>\n          <button className=\"btn btn-primary\" style={{ fontWeight: 700, fontSize: '1rem', borderRadius: 10, background: 'linear-gradient(90deg, #ffa000 0%, #ffd54f 100%)', color: '#fff', boxShadow: '0 4px 16px rgba(255,160,0,0.18)' }} onClick={handlePrint}>\n            Imprimer\n          </button>\n        </div>\n\n        {/* Modal d'ajout d'équipement */}\n        {showModal && (\n          <div style={{\n            position: 'fixed',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            backgroundColor: 'rgba(0, 0, 0, 0.5)',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            zIndex: 1000\n          }}>\n            <div style={{\n              backgroundColor: 'white',\n              borderRadius: 12,\n              padding: 32,\n              maxWidth: 600,\n              width: '90%',\n              maxHeight: '90vh',\n              overflowY: 'auto',\n              boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)'\n            }}>\n              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>\n                <h3 style={{ margin: 0, color: '#1976d2', fontSize: '1.5rem', fontWeight: 700 }}>\n                  Ajouter un nouvel équipement\n                </h3>\n                <button\n                  onClick={() => setShowModal(false)}\n                  style={{\n                    background: 'none',\n                    border: 'none',\n                    fontSize: '1.5rem',\n                    color: '#666',\n                    cursor: 'pointer',\n                    padding: 8,\n                    borderRadius: '50%'\n                  }}\n                >\n                  <FontAwesomeIcon icon={faTimes} />\n                </button>\n              </div>\n\n              <form onSubmit={handleSubmit}>\n                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 16, marginBottom: 16 }}>\n                  <div>\n                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>\n                      Nom de l'équipement *\n                    </label>\n                    <input\n                      type=\"text\"\n                      name=\"nom\"\n                      value={formData.nom}\n                      onChange={handleInputChange}\n                      required\n                      style={{\n                        width: '100%',\n                        padding: '12px',\n                        border: '2px solid #e0e0e0',\n                        borderRadius: 8,\n                        fontSize: '1rem',\n                        transition: 'border-color 0.2s'\n                      }}\n                      onFocus={(e) => e.target.style.borderColor = '#1976d2'}\n                      onBlur={(e) => e.target.style.borderColor = '#e0e0e0'}\n                    />\n                  </div>\n\n                  <div>\n                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>\n                      Type *\n                    </label>\n                    <select\n                      name=\"type\"\n                      value={formData.type}\n                      onChange={handleInputChange}\n                      required\n                      style={{\n                        width: '100%',\n                        padding: '12px',\n                        border: '2px solid #e0e0e0',\n                        borderRadius: 8,\n                        fontSize: '1rem'\n                      }}\n                    >\n                      <option value=\"\">Sélectionner un type</option>\n                      {typeOptions.map(type => (\n                        <option key={type} value={type}>{type}</option>\n                      ))}\n                    </select>\n                  </div>\n                </div>\n\n                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 16, marginBottom: 16 }}>\n                  <div>\n                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>\n                      Numéro de série\n                    </label>\n                    <input\n                      type=\"text\"\n                      name=\"numero_serie\"\n                      value={formData.numero_serie}\n                      onChange={handleInputChange}\n                      style={{\n                        width: '100%',\n                        padding: '12px',\n                        border: '2px solid #e0e0e0',\n                        borderRadius: 8,\n                        fontSize: '1rem'\n                      }}\n                    />\n                  </div>\n\n                  <div>\n                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>\n                      Statut *\n                    </label>\n                    <select\n                      name=\"statut\"\n                      value={formData.statut}\n                      onChange={handleInputChange}\n                      required\n                      style={{\n                        width: '100%',\n                        padding: '12px',\n                        border: '2px solid #e0e0e0',\n                        borderRadius: 8,\n                        fontSize: '1rem'\n                      }}\n                    >\n                      {statutOptions.map(statut => (\n                        <option key={statut} value={statut}>{statut}</option>\n                      ))}\n                    </select>\n                  </div>\n                </div>\n\n                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 16, marginBottom: 16 }}>\n                  <div>\n                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>\n                      Date d'achat\n                    </label>\n                    <input\n                      type=\"date\"\n                      name=\"date_achat\"\n                      value={formData.date_achat}\n                      onChange={handleInputChange}\n                      style={{\n                        width: '100%',\n                        padding: '12px',\n                        border: '2px solid #e0e0e0',\n                        borderRadius: 8,\n                        fontSize: '1rem'\n                      }}\n                    />\n                  </div>\n\n                  <div>\n                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>\n                      Fournisseur\n                    </label>\n                    <select\n                      name=\"fournisseur_id\"\n                      value={formData.fournisseur_id}\n                      onChange={handleInputChange}\n                      style={{\n                        width: '100%',\n                        padding: '12px',\n                        border: '2px solid #e0e0e0',\n                        borderRadius: 8,\n                        fontSize: '1rem'\n                      }}\n                    >\n                      <option value=\"\">Sélectionner un fournisseur</option>\n                      {fournisseurs.map(fournisseur => (\n                        <option key={fournisseur.id} value={fournisseur.id}>\n                          {fournisseur.nom}\n                        </option>\n                      ))}\n                    </select>\n                  </div>\n                </div>\n\n                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 16, marginBottom: 16 }}>\n                  <div>\n                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>\n                      Début de garantie\n                    </label>\n                    <input\n                      type=\"date\"\n                      name=\"date_debut_garantie\"\n                      value={formData.date_debut_garantie}\n                      onChange={handleInputChange}\n                      style={{\n                        width: '100%',\n                        padding: '12px',\n                        border: '2px solid #e0e0e0',\n                        borderRadius: 8,\n                        fontSize: '1rem'\n                      }}\n                    />\n                  </div>\n\n                  <div>\n                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>\n                      Fin de garantie\n                    </label>\n                    <input\n                      type=\"date\"\n                      name=\"date_fin_garantie\"\n                      value={formData.date_fin_garantie}\n                      onChange={handleInputChange}\n                      style={{\n                        width: '100%',\n                        padding: '12px',\n                        border: '2px solid #e0e0e0',\n                        borderRadius: 8,\n                        fontSize: '1rem'\n                      }}\n                    />\n                  </div>\n                </div>\n\n                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: 16, marginBottom: 16 }}>\n                  <div>\n                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>\n                      Stock actuel\n                    </label>\n                    <input\n                      type=\"number\"\n                      name=\"stock_actuel\"\n                      value={formData.stock_actuel}\n                      onChange={handleInputChange}\n                      min=\"0\"\n                      style={{\n                        width: '100%',\n                        padding: '12px',\n                        border: '2px solid #e0e0e0',\n                        borderRadius: 8,\n                        fontSize: '1rem'\n                      }}\n                    />\n                  </div>\n\n                  <div>\n                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>\n                      Stock maximum\n                    </label>\n                    <input\n                      type=\"number\"\n                      name=\"stock_max\"\n                      value={formData.stock_max}\n                      onChange={handleInputChange}\n                      min=\"1\"\n                      style={{\n                        width: '100%',\n                        padding: '12px',\n                        border: '2px solid #e0e0e0',\n                        borderRadius: 8,\n                        fontSize: '1rem'\n                      }}\n                    />\n                  </div>\n\n                  <div>\n                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 600, color: '#333' }}>\n                      Stock minimum\n                    </label>\n                    <input\n                      type=\"number\"\n                      name=\"stock_min\"\n                      value={formData.stock_min}\n                      onChange={handleInputChange}\n                      min=\"0\"\n                      style={{\n                        width: '100%',\n                        padding: '12px',\n                        border: '2px solid #e0e0e0',\n                        borderRadius: 8,\n                        fontSize: '1rem'\n                      }}\n                    />\n                  </div>\n                </div>\n\n                <div style={{ marginBottom: 24 }}>\n                  <label style={{ display: 'flex', alignItems: 'center', fontWeight: 600, color: '#333', cursor: 'pointer' }}>\n                    <input\n                      type=\"checkbox\"\n                      name=\"en_stock\"\n                      checked={formData.en_stock}\n                      onChange={handleInputChange}\n                      style={{ marginRight: 8, transform: 'scale(1.2)' }}\n                    />\n                    Équipement en stock\n                  </label>\n                </div>\n\n                <div style={{ display: 'flex', gap: 12, justifyContent: 'flex-end' }}>\n                  <button\n                    type=\"button\"\n                    onClick={() => setShowModal(false)}\n                    style={{\n                      padding: '12px 24px',\n                      border: '2px solid #e0e0e0',\n                      borderRadius: 8,\n                      background: 'white',\n                      color: '#666',\n                      fontSize: '1rem',\n                      fontWeight: 600,\n                      cursor: 'pointer'\n                    }}\n                  >\n                    Annuler\n                  </button>\n                  <button\n                    type=\"submit\"\n                    disabled={submitting}\n                    style={{\n                      padding: '12px 24px',\n                      border: 'none',\n                      borderRadius: 8,\n                      background: submitting ? '#ccc' : 'linear-gradient(90deg, #1976d2 0%, #42a5f5 100%)',\n                      color: 'white',\n                      fontSize: '1rem',\n                      fontWeight: 600,\n                      cursor: submitting ? 'not-allowed' : 'pointer',\n                      boxShadow: '0 4px 16px rgba(21,101,192,0.18)'\n                    }}\n                  >\n                    {submitting ? 'Ajout en cours...' : 'Ajouter l\\'équipement'}\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        )}\n\n        {/* Contenu principal */}\n        {loading ? (\n          <div style={{ textAlign: 'center', padding: '40px' }}>\n            <p>Chargement des équipements...</p>\n          </div>\n        ) : error ? (\n          <div style={{ textAlign: 'center', padding: '40px', color: '#e74a3b' }}>\n            <p>Erreur lors du chargement : {error}</p>\n            <button \n              onClick={() => window.location.reload()} \n              style={{ marginTop: '10px', padding: '8px 16px', background: '#1976d2', color: 'white', border: 'none', borderRadius: '4px', cursor: 'pointer' }}\n            >\n              Actualiser la page\n            </button>\n          </div>\n        ) : !Array.isArray(equipements) || equipements.length === 0 ? (\n          <div style={{ textAlign: 'center', padding: '40px' }}>\n            <p>Aucun équipement trouvé</p>\n          </div>\n        ) : (\n          <table className=\"equipements-table\">\n            <thead>\n              <tr>\n                <th>Nom</th>\n                <th>Type</th>\n                <th>Numéro de série</th>\n                <th>Statut</th>\n                <th>Date d'achat</th>\n                <th>Fournisseur</th>\n              </tr>\n            </thead>\n      <tbody>\n  {equipements.map((eq, index) => {\n    // Formatage des données avec vérifications plus robustes\n    const numeroSerie = eq.numero_serie || eq.numeroSerie || 'N/A';\n    \n    // Fonction pour formater une date\n    const formatDate = (dateString) => {\n      if (!dateString) return 'N/A';\n      try {\n        const dateObj = new Date(dateString);\n        return isNaN(dateObj.getTime()) \n          ? 'N/A' \n          : dateObj.toLocaleDateString('fr-FR', {\n              year: 'numeric',\n              month: '2-digit',\n              day: '2-digit'\n            });\n      } catch (e) {\n        console.error(\"Erreur de formatage de date\", e);\n        return 'N/A';\n      }\n    };\n\n    const dateAchat = formatDate(eq.date_achat);\n    const dateDebutGarantie = formatDate(eq.date_debut_garantie);\n    const dateFinGarantie = formatDate(eq.date_fin_garantie);\n    \n    const fournisseur = eq.fournisseur?.nom || eq.fournisseur_id || 'Non spécifié';\n\n    return (\n      <tr key={eq.id || index}>\n        <td>{eq.nom || 'N/A'}</td>\n        <td>{eq.type || 'N/A'}</td>\n        <td>{numeroSerie}</td>\n        <td>{eq.statut || 'N/A'}</td>\n        <td>{dateDebutGarantie}</td> {/* Afficher la date de début de garantie */}\n        <td>{dateFinGarantie}</td> {/* Afficher la date de fin de garantie */}\n        <td>{fournisseur}</td>\n      </tr>\n    );\n  })}\n</tbody>\n          </table>\n        )}\n      </div>\n    </div>\n  );\n}"], "mappings": ";;AAAA,OAAO,mBAAmB;AAC1B,SAASA,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,OAAOC,IAAI,MAAM,kCAAkC;AACnD,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,QAAQ,EAAEC,OAAO,EAAEC,aAAa,EAAEC,OAAO,EAAEC,SAAS,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAEC,MAAM,QAAQ,mCAAmC;AACzJ,SAASC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,iBAAiB;AACxB,OAAO,KAAKC,IAAI,MAAM,MAAM;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,eAAe,SAASC,WAAWA,CAAA,EAAG;EAAAC,EAAA;EACpC,MAAMC,QAAQ,GAAGrB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACsB,WAAW,EAAEC,cAAc,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACY,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACc,SAAS,EAAEC,YAAY,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACgB,YAAY,EAAEC,eAAe,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACkB,UAAU,EAAEC,aAAa,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACoB,UAAU,EAAEC,aAAa,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACA,MAAM,CAACsB,QAAQ,EAAEC,WAAW,CAAC,GAAGvB,QAAQ,CAAC;IACvCwB,GAAG,EAAE,EAAE;IACPC,IAAI,EAAE,EAAE;IACRC,YAAY,EAAE,EAAE;IAChBC,MAAM,EAAE,YAAY;IACpBC,UAAU,EAAE,EAAE;IACdC,cAAc,EAAE,EAAE;IAClBC,mBAAmB,EAAE,EAAE;IACvBC,iBAAiB,EAAE,EAAE;IACrBC,QAAQ,EAAE,IAAI;IACdC,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE,CAAC;IACZC,SAAS,EAAE,CAAC;IACZC,YAAY,EAAE;EAChB,CAAC,CAAC;;EAEF;EACA,MAAMC,aAAa,GAAG,CAAC,YAAY,EAAE,gBAAgB,EAAE,QAAQ,EAAE,cAAc,CAAC;EAChF,MAAMC,WAAW,GAAG,CAAC,YAAY,EAAE,YAAY,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,SAAS,EAAE,OAAO,CAAC;EAExGvC,SAAS,CAAC,MAAM;IACdwC,eAAe,CAAC,CAAC;IACjBC,gBAAgB,CAAC,CAAC;IAClBC,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMF,eAAe,GAAGA,CAAA,KAAM;IAC5BG,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;IAE5C,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IAC/C,MAAMC,OAAO,GAAG;MACd,cAAc,EAAE;IAClB,CAAC;IACD,IAAIH,KAAK,EAAE;MACTG,OAAO,CAAC,eAAe,CAAC,GAAG,UAAUH,KAAK,EAAE;IAC9C;IAEAI,KAAK,CAAC,uCAAuC,EAAE;MAC7CC,MAAM,EAAE,KAAK;MACbF,OAAO,EAAEA;IACX,CAAC,CAAC,CACCG,IAAI,CAACC,GAAG,IAAI;MACXT,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEQ,GAAG,CAACC,MAAM,CAAC;MAC5D,IAAI,CAACD,GAAG,CAACE,EAAE,EAAE;QACX,MAAM,IAAIC,KAAK,CAAC,gBAAgBH,GAAG,CAACC,MAAM,EAAE,CAAC;MAC/C;MACA,OAAOD,GAAG,CAACI,IAAI,CAAC,CAAC;IACnB,CAAC,CAAC,CACDL,IAAI,CAACM,IAAI,IAAI;MACZd,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEa,IAAI,CAAC;MAChD,IAAIC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,EAAE;QACvB/C,cAAc,CAAC+C,IAAI,CAAC;QACpB3C,QAAQ,CAAC,IAAI,CAAC;MAChB,CAAC,MAAM;QACL6B,OAAO,CAACiB,IAAI,CAAC,iDAAiD,EAAEH,IAAI,CAAC;QACrE/C,cAAc,CAAC,EAAE,CAAC;QAClBI,QAAQ,CAAC,6BAA6B,CAAC;MACzC;MACAF,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,CACDiD,KAAK,CAACC,GAAG,IAAI;MACZnB,OAAO,CAAC9B,KAAK,CAAC,4CAA4C,EAAEiD,GAAG,CAAC;MAChEpD,cAAc,CAAC,EAAE,CAAC;MAClBI,QAAQ,CAACgD,GAAG,CAACC,OAAO,IAAI,sBAAsB,CAAC;MAC/CnD,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC;EACN,CAAC;EAED,MAAM6B,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMI,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IAC/C,MAAMC,OAAO,GAAG;MACd,cAAc,EAAE;IAClB,CAAC;IACD,IAAIH,KAAK,EAAE;MACTG,OAAO,CAAC,eAAe,CAAC,GAAG,UAAUH,KAAK,EAAE;IAC9C;IAEAI,KAAK,CAAC,wCAAwC,EAAE;MAC9CC,MAAM,EAAE,KAAK;MACbF,OAAO,EAAEA;IACX,CAAC,CAAC,CACCG,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACE,EAAE,GAAGF,GAAG,CAACI,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,CACrCL,IAAI,CAACM,IAAI,IAAIvC,eAAe,CAACwC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,GAAGA,IAAI,GAAG,EAAE,CAAC,CAAC,CAC9DI,KAAK,CAACC,GAAG,IAAI;MACZnB,OAAO,CAAC9B,KAAK,CAAC,6CAA6C,EAAEiD,GAAG,CAAC;MACjE5C,eAAe,CAAC,EAAE,CAAC;IACrB,CAAC,CAAC;EACN,CAAC;EAED,MAAMwB,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMG,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IAC/C,MAAMC,OAAO,GAAG;MACd,cAAc,EAAE;IAClB,CAAC;IACD,IAAIH,KAAK,EAAE;MACTG,OAAO,CAAC,eAAe,CAAC,GAAG,UAAUH,KAAK,EAAE;IAC9C;IAEAI,KAAK,CAAC,sCAAsC,EAAE;MAC5CC,MAAM,EAAE,KAAK;MACbF,OAAO,EAAEA;IACX,CAAC,CAAC,CACCG,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACE,EAAE,GAAGF,GAAG,CAACI,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,CACrCL,IAAI,CAACM,IAAI,IAAIrC,aAAa,CAACsC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,GAAGA,IAAI,GAAG,EAAE,CAAC,CAAC,CAC5DI,KAAK,CAACC,GAAG,IAAI;MACZnB,OAAO,CAAC9B,KAAK,CAAC,2CAA2C,EAAEiD,GAAG,CAAC;MAC/D1C,aAAa,CAAC,EAAE,CAAC;IACnB,CAAC,CAAC;EACN,CAAC;EAED,MAAM4C,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC,KAAK;MAAEzC,IAAI;MAAE0C;IAAQ,CAAC,GAAGH,CAAC,CAACI,MAAM;IAC/C7C,WAAW,CAAC8C,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACJ,IAAI,GAAGxC,IAAI,KAAK,UAAU,GAAG0C,OAAO,GAAGD;IAC1C,CAAC,CAAC,CAAC;EACL,CAAC;EAEF,MAAMI,YAAY,GAAG,MAAON,CAAC,IAAK;IACjCA,CAAC,CAACO,cAAc,CAAC,CAAC;IAClBlD,aAAa,CAAC,IAAI,CAAC;;IAEnB;IACA,MAAMmD,UAAU,GAAG;MACjB,GAAGlD,QAAQ;MACXM,UAAU,EAAEN,QAAQ,CAACM,UAAU,IAAI,IAAI;MACvCE,mBAAmB,EAAER,QAAQ,CAACQ,mBAAmB,IAAI,IAAI;MACzDC,iBAAiB,EAAET,QAAQ,CAACS,iBAAiB,IAAI,IAAI;MACrDF,cAAc,EAAEP,QAAQ,CAACO,cAAc,IAAI;IAC7C,CAAC;IAED,IAAI;MACF,MAAMe,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;MAC/C,MAAMC,OAAO,GAAG;QACd,cAAc,EAAE;MAClB,CAAC;MACD,IAAIH,KAAK,EAAE;QACTG,OAAO,CAAC,eAAe,CAAC,GAAG,UAAUH,KAAK,EAAE;MAC9C;MAEAF,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE6B,UAAU,CAAC,CAAC,CAAC;;MAE9C,MAAMC,QAAQ,GAAG,MAAMzB,KAAK,CAAC,uCAAuC,EAAE;QACpEC,MAAM,EAAE,MAAM;QACdF,OAAO,EAAEA,OAAO;QAChB2B,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACJ,UAAU,CAAC,CAAC;MACnC,CAAC,CAAC;MAEA,IAAI,CAACC,QAAQ,CAACpB,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,gBAAgBmB,QAAQ,CAACrB,MAAM,EAAE,CAAC;MACpD;MAEA,MAAMyB,aAAa,GAAG,MAAMJ,QAAQ,CAAClB,IAAI,CAAC,CAAC;;MAE3C;MACA9C,cAAc,CAAC4D,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEQ,aAAa,CAAC,CAAC;;MAEhD;MACAtD,WAAW,CAAC;QACVC,GAAG,EAAE,EAAE;QACPC,IAAI,EAAE,EAAE;QACRC,YAAY,EAAE,EAAE;QAChBC,MAAM,EAAE,YAAY;QACpBC,UAAU,EAAE,EAAE;QACdC,cAAc,EAAE,EAAE;QAClBC,mBAAmB,EAAE,EAAE;QACvBC,iBAAiB,EAAE,EAAE;QACrBC,QAAQ,EAAE,IAAI;QACdC,YAAY,EAAE,CAAC;QACfC,SAAS,EAAE,CAAC;QACZC,SAAS,EAAE,CAAC;QACZC,YAAY,EAAE;MAChB,CAAC,CAAC;MACFrB,YAAY,CAAC,KAAK,CAAC;MAEnB+D,KAAK,CAAC,iCAAiC,CAAC;IAC1C,CAAC,CAAC,OAAOjB,GAAG,EAAE;MACZnB,OAAO,CAAC9B,KAAK,CAAC,yBAAyB,EAAEiD,GAAG,CAAC;MAC7CiB,KAAK,CAAC,2BAA2BjB,GAAG,CAACC,OAAO,EAAE,CAAC;IACjD,CAAC,SAAS;MACRzC,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAM0D,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI,CAACtB,KAAK,CAACC,OAAO,CAAClD,WAAW,CAAC,IAAIA,WAAW,CAACwE,MAAM,KAAK,CAAC,EAAE;MAC3DF,KAAK,CAAC,0BAA0B,CAAC;MACjC;IACF;IAEA,MAAMG,OAAO,GAAG,CACd,KAAK,EACL,MAAM,EACN,iBAAiB,EACjB,QAAQ,EACR,cAAc,EACd,aAAa,CACd;IACD,MAAMC,IAAI,GAAG1E,WAAW,CAAC2E,GAAG,CAACC,EAAE;MAAA,IAAAC,eAAA;MAAA,OAAI,CACjCD,EAAE,CAAC5D,GAAG,IAAI,EAAE,EACZ4D,EAAE,CAAC3D,IAAI,IAAI,EAAE,EACb2D,EAAE,CAAC1D,YAAY,IAAI,EAAE,EACrB0D,EAAE,CAACzD,MAAM,IAAI,EAAE,EACfyD,EAAE,CAACxD,UAAU,IAAI,EAAE,EACnB,EAAAyD,eAAA,GAAAD,EAAE,CAACE,WAAW,cAAAD,eAAA,uBAAdA,eAAA,CAAgB7D,GAAG,KAAI,EAAE,CAC1B;IAAA,EAAC;IACF,MAAM+D,SAAS,GAAGrF,IAAI,CAACsF,KAAK,CAACC,YAAY,CAAC,CAACR,OAAO,EAAE,GAAGC,IAAI,CAAC,CAAC;IAC7D,MAAMQ,QAAQ,GAAGxF,IAAI,CAACsF,KAAK,CAACG,QAAQ,CAAC,CAAC;IACtCzF,IAAI,CAACsF,KAAK,CAACI,iBAAiB,CAACF,QAAQ,EAAEH,SAAS,EAAE,aAAa,CAAC;IAChErF,IAAI,CAAC2F,SAAS,CAACH,QAAQ,EAAE,kBAAkB,CAAC;EAC9C,CAAC;EAED,MAAMI,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAACrC,KAAK,CAACC,OAAO,CAAClD,WAAW,CAAC,IAAIA,WAAW,CAACwE,MAAM,KAAK,CAAC,EAAE;MAC3DF,KAAK,CAAC,0BAA0B,CAAC;MACjC;IACF;IAEA,MAAMiB,GAAG,GAAG,IAAI9F,KAAK,CAAC,CAAC;IACvB8F,GAAG,CAACC,IAAI,CAAC,uBAAuB,EAAE,EAAE,EAAE,EAAE,CAAC;IACzC,MAAMf,OAAO,GAAG,CACd,KAAK,EACL,MAAM,EACN,iBAAiB,EACjB,QAAQ,EACR,cAAc,EACd,aAAa,CACd;IACD,MAAMC,IAAI,GAAG1E,WAAW,CAAC2E,GAAG,CAACC,EAAE;MAAA,IAAAa,gBAAA;MAAA,OAAI,CACjCb,EAAE,CAAC5D,GAAG,IAAI,EAAE,EACZ4D,EAAE,CAAC3D,IAAI,IAAI,EAAE,EACb2D,EAAE,CAAC1D,YAAY,IAAI,EAAE,EACrB0D,EAAE,CAACzD,MAAM,IAAI,EAAE,EACfyD,EAAE,CAACxD,UAAU,IAAI,EAAE,EACnB,EAAAqE,gBAAA,GAAAb,EAAE,CAACE,WAAW,cAAAW,gBAAA,uBAAdA,gBAAA,CAAgBzE,GAAG,KAAI,EAAE,CAC1B;IAAA,EAAC;IACFuE,GAAG,CAACG,SAAS,CAAC;MACZC,IAAI,EAAE,CAAClB,OAAO,CAAC;MACfP,IAAI,EAAEQ,IAAI;MACVkB,MAAM,EAAE,EAAE;MACVC,MAAM,EAAE;QAAEC,QAAQ,EAAE;MAAG;IACzB,CAAC,CAAC;IACFP,GAAG,CAACQ,IAAI,CAAC,iBAAiB,CAAC;EAC7B,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxBC,MAAM,CAACC,KAAK,CAAC,CAAC;EAChB,CAAC;EAED,oBACEtG,OAAA;IAAKuG,SAAS,EAAC,uBAAuB;IAAAC,QAAA,gBAEpCxG,OAAA;MAAKuG,SAAS,EAAC,SAAS;MAAAC,QAAA,gBACtBxG,OAAA;QAAKuG,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3BxG,OAAA;UAAKyG,GAAG,EAAE1H,IAAK;UAAC2H,GAAG,EAAC,iBAAiB;UAACH,SAAS,EAAC;QAAc;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC,eACN9G,OAAA;QAAKuG,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBxG,OAAA;UAAAwG,QAAA,gBACExG,OAAA;YAAAwG,QAAA,eACExG,OAAA,CAACnB,IAAI;cAACkI,EAAE,EAAC,GAAG;cAACR,SAAS,EAAEpG,QAAQ,CAAC6G,QAAQ,KAAK,GAAG,GAAG,oBAAoB,GAAG,EAAG;cAAAR,QAAA,gBAC5ExG,OAAA,CAAChB,eAAe;gBAACiI,IAAI,EAAEhI,QAAS;gBAACiI,KAAK,EAAE;kBAACC,WAAW,EAAC;gBAAC;cAAE;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,YAC7D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACL9G,OAAA;YAAAwG,QAAA,eACExG,OAAA,CAACnB,IAAI;cAACkI,EAAE,EAAC,cAAc;cAACR,SAAS,EAAEpG,QAAQ,CAAC6G,QAAQ,KAAK,cAAc,GAAG,QAAQ,GAAG,EAAG;cAAAR,QAAA,gBACtFxG,OAAA,CAAChB,eAAe;gBAACiI,IAAI,EAAEhI,QAAS;gBAACiI,KAAK,EAAE;kBAACC,WAAW,EAAC;gBAAC;cAAE;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gCAC7D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACL9G,OAAA;YAAAwG,QAAA,eACExG,OAAA,CAACnB,IAAI;cAACkI,EAAE,EAAC,WAAW;cAACR,SAAS,EAAEpG,QAAQ,CAAC6G,QAAQ,KAAK,WAAW,GAAG,QAAQ,GAAG,EAAG;cAAAR,QAAA,gBAChFxG,OAAA,CAAChB,eAAe;gBAACiI,IAAI,EAAE/H,OAAQ;gBAACgI,KAAK,EAAE;kBAACC,WAAW,EAAC;gBAAC;cAAE;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,6BAC5D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACL9G,OAAA;YAAAwG,QAAA,eACExG,OAAA,CAACnB,IAAI;cAACkI,EAAE,EAAC,eAAe;cAACR,SAAS,EAAEpG,QAAQ,CAAC6G,QAAQ,KAAK,eAAe,GAAG,QAAQ,GAAG,EAAG;cAAAR,QAAA,gBACxFxG,OAAA,CAAChB,eAAe;gBAACiI,IAAI,EAAE9H,aAAc;gBAAC+H,KAAK,EAAE;kBAACC,WAAW,EAAC;gBAAC;cAAE;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,2BAClE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACL9G,OAAA;YAAAwG,QAAA,eACExG,OAAA,CAACnB,IAAI;cAACkI,EAAE,EAAC,eAAe;cAACR,SAAS,EAAEpG,QAAQ,CAAC6G,QAAQ,KAAK,eAAe,GAAG,QAAQ,GAAG,EAAG;cAAAR,QAAA,gBACxFxG,OAAA,CAAChB,eAAe;gBAACiI,IAAI,EAAE7H,OAAQ;gBAAC8H,KAAK,EAAE;kBAACC,WAAW,EAAC;gBAAC;cAAE;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,8BAC5D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACN9G,OAAA;QAAKuG,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BxG,OAAA,CAACnB,IAAI;UAACkI,EAAE,EAAC,SAAS;UAAAP,QAAA,gBAChBxG,OAAA,CAAChB,eAAe;YAACiI,IAAI,EAAE5H,SAAU;YAAC6H,KAAK,EAAE;cAACC,WAAW,EAAC;YAAC;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,+BAC9D;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN9G,OAAA;QAAKkH,KAAK,EAAE;UAAEE,SAAS,EAAE,MAAM;UAAEC,OAAO,EAAE,YAAY;UAAEC,SAAS,EAAE;QAAS,CAAE;QAAAd,QAAA,eAC5ExG,OAAA;UACEuG,SAAS,EAAC,YAAY;UACtBgB,OAAO,EAAEA,CAAA,KAAM;YACb9E,YAAY,CAAC+E,UAAU,CAAC,WAAW,CAAC;YACpCnB,MAAM,CAAClG,QAAQ,CAACsH,IAAI,GAAG,GAAG;UAC5B,CAAE;UAAAjB,QAAA,gBAEFxG,OAAA,CAAChB,eAAe;YAACiI,IAAI,EAAE3H,YAAa;YAAC4H,KAAK,EAAE;cAACC,WAAW,EAAC;YAAC;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,mBACjE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9G,OAAA;MAAKuG,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAElCxG,OAAA;QAAKuG,SAAS,EAAC,sBAAsB;QAACW,KAAK,EAAE;UAAEQ,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,cAAc,EAAE,eAAe;UAAEC,YAAY,EAAE;QAAG,CAAE;QAAArB,QAAA,gBACxIxG,OAAA;UAAKuG,SAAS,EAAC,YAAY;UAACW,KAAK,EAAE;YAAEY,IAAI,EAAE,CAAC;YAAEC,QAAQ,EAAE,GAAG;YAAEZ,WAAW,EAAE,EAAE;YAAEa,QAAQ,EAAE;UAAW,CAAE;UAAAxB,QAAA,gBACnGxG,OAAA,CAAChB,eAAe;YAACiI,IAAI,EAAE1H,QAAS;YAAC2H,KAAK,EAAE;cAAEc,QAAQ,EAAE,UAAU;cAAEC,IAAI,EAAE,EAAE;cAAEC,GAAG,EAAE,KAAK;cAAEC,SAAS,EAAE,kBAAkB;cAAEC,KAAK,EAAE,SAAS;cAAElC,QAAQ,EAAE,QAAQ;cAAEmC,OAAO,EAAE;YAAI;UAAE;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7K9G,OAAA;YACEqB,IAAI,EAAC,MAAM;YACXiH,WAAW,EAAC,0BAAuB;YACnCpB,KAAK,EAAE;cAAEqB,KAAK,EAAE,MAAM;cAAElB,OAAO,EAAE,qBAAqB;cAAEmB,YAAY,EAAE,CAAC;cAAEC,MAAM,EAAE,mBAAmB;cAAEvC,QAAQ,EAAE,MAAM;cAAEwC,SAAS,EAAE;YAAkC;UAAE;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN9G,OAAA;UAAKuG,SAAS,EAAC,eAAe;UAACW,KAAK,EAAE;YAAEQ,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEgB,GAAG,EAAE;UAAG,CAAE;UAAAnC,QAAA,gBACvFxG,OAAA;YAAKuG,SAAS,EAAC,mBAAmB;YAACW,KAAK,EAAE;cAAEc,QAAQ,EAAE,UAAU;cAAEb,WAAW,EAAE,CAAC;cAAEyB,MAAM,EAAE;YAAU,CAAE;YAAApC,QAAA,gBACpGxG,OAAA,CAAChB,eAAe;cAACiI,IAAI,EAAEzH,MAAO;cAAC0H,KAAK,EAAE;gBAAEhB,QAAQ,EAAE,QAAQ;gBAAEkC,KAAK,EAAE;cAAU;YAAE;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClF9G,OAAA;cAAMkH,KAAK,EAAE;gBAAEc,QAAQ,EAAE,UAAU;gBAAEE,GAAG,EAAE,CAAC,CAAC;gBAAEW,KAAK,EAAE,CAAC,CAAC;gBAAEC,UAAU,EAAE,SAAS;gBAAEV,KAAK,EAAE,MAAM;gBAAEI,YAAY,EAAE,KAAK;gBAAEtC,QAAQ,EAAE,QAAQ;gBAAEmB,OAAO,EAAE,SAAS;gBAAE0B,UAAU,EAAE;cAAI,CAAE;cAAAvC,QAAA,EAAC;YAAC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpL,CAAC,eACN9G,OAAA;YAAMkH,KAAK,EAAE;cAAE6B,UAAU,EAAE,GAAG;cAAEX,KAAK,EAAE,SAAS;cAAElC,QAAQ,EAAE,MAAM;cAAEiB,WAAW,EAAE;YAAE,CAAE;YAAAX,QAAA,EAAC;UAAc;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3G9G,OAAA,CAAChB,eAAe;YAACiI,IAAI,EAAE5H,SAAU;YAAC6H,KAAK,EAAE;cAAEhB,QAAQ,EAAE,QAAQ;cAAEkC,KAAK,EAAE,SAAS;cAAEU,UAAU,EAAE,MAAM;cAAEN,YAAY,EAAE,KAAK;cAAEnB,OAAO,EAAE,CAAC;cAAEqB,SAAS,EAAE;YAAkC;UAAE;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN9G,OAAA;QAAIuG,SAAS,EAAC,mBAAmB;QAAAC,QAAA,GAAC,4BACT,EAACnD,KAAK,CAACC,OAAO,CAAClD,WAAW,CAAC,GAAGA,WAAW,CAACwE,MAAM,GAAG,CAAC,EAAC,GAC9E;MAAA;QAAA+B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAEL9G,OAAA;QAAKkH,KAAK,EAAE;UAAEQ,OAAO,EAAE,MAAM;UAAEiB,GAAG,EAAE,EAAE;UAAEd,YAAY,EAAE;QAAG,CAAE;QAAArB,QAAA,gBACzDxG,OAAA;UACEuG,SAAS,EAAC,iBAAiB;UAC3BW,KAAK,EAAE;YAAE6B,UAAU,EAAE,GAAG;YAAE7C,QAAQ,EAAE,MAAM;YAAEsC,YAAY,EAAE,EAAE;YAAEM,UAAU,EAAE,kDAAkD;YAAEV,KAAK,EAAE,MAAM;YAAEM,SAAS,EAAE;UAAmC,CAAE;UAC7LnB,OAAO,EAAEA,CAAA,KAAM5G,YAAY,CAAC,IAAI,CAAE;UAAA6F,QAAA,gBAElCxG,OAAA,CAAChB,eAAe;YAACiI,IAAI,EAAEvH,MAAO;YAACwH,KAAK,EAAE;cAACC,WAAW,EAAE;YAAC;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,4BAE5D;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT9G,OAAA;UAAQuG,SAAS,EAAC,iBAAiB;UAACW,KAAK,EAAE;YAAE6B,UAAU,EAAE,GAAG;YAAE7C,QAAQ,EAAE,MAAM;YAAEsC,YAAY,EAAE,EAAE;YAAEM,UAAU,EAAE,kDAAkD;YAAEV,KAAK,EAAE,MAAM;YAAEM,SAAS,EAAE;UAAkC,CAAE;UAACnB,OAAO,EAAE5C,iBAAkB;UAAA6B,QAAA,EAAC;QAE7P;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT9G,OAAA;UAAQuG,SAAS,EAAC,iBAAiB;UAACW,KAAK,EAAE;YAAE6B,UAAU,EAAE,GAAG;YAAE7C,QAAQ,EAAE,MAAM;YAAEsC,YAAY,EAAE,EAAE;YAAEM,UAAU,EAAE,kDAAkD;YAAEV,KAAK,EAAE,MAAM;YAAEM,SAAS,EAAE;UAAkC,CAAE;UAACnB,OAAO,EAAE7B,eAAgB;UAAAc,QAAA,EAAC;QAE3P;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT9G,OAAA;UAAQuG,SAAS,EAAC,iBAAiB;UAACW,KAAK,EAAE;YAAE6B,UAAU,EAAE,GAAG;YAAE7C,QAAQ,EAAE,MAAM;YAAEsC,YAAY,EAAE,EAAE;YAAEM,UAAU,EAAE,kDAAkD;YAAEV,KAAK,EAAE,MAAM;YAAEM,SAAS,EAAE;UAAkC,CAAE;UAACnB,OAAO,EAAEnB,WAAY;UAAAI,QAAA,EAAC;QAEvP;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGLpG,SAAS,iBACRV,OAAA;QAAKkH,KAAK,EAAE;UACVc,QAAQ,EAAE,OAAO;UACjBE,GAAG,EAAE,CAAC;UACND,IAAI,EAAE,CAAC;UACPY,KAAK,EAAE,CAAC;UACRG,MAAM,EAAE,CAAC;UACTC,eAAe,EAAE,oBAAoB;UACrCvB,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBsB,MAAM,EAAE;QACV,CAAE;QAAA1C,QAAA,eACAxG,OAAA;UAAKkH,KAAK,EAAE;YACV+B,eAAe,EAAE,OAAO;YACxBT,YAAY,EAAE,EAAE;YAChBnB,OAAO,EAAE,EAAE;YACXU,QAAQ,EAAE,GAAG;YACbQ,KAAK,EAAE,KAAK;YACZY,SAAS,EAAE,MAAM;YACjBC,SAAS,EAAE,MAAM;YACjBV,SAAS,EAAE;UACb,CAAE;UAAAlC,QAAA,gBACAxG,OAAA;YAAKkH,KAAK,EAAE;cAAEQ,OAAO,EAAE,MAAM;cAAEE,cAAc,EAAE,eAAe;cAAED,UAAU,EAAE,QAAQ;cAAEE,YAAY,EAAE;YAAG,CAAE;YAAArB,QAAA,gBACvGxG,OAAA;cAAIkH,KAAK,EAAE;gBAAEmC,MAAM,EAAE,CAAC;gBAAEjB,KAAK,EAAE,SAAS;gBAAElC,QAAQ,EAAE,QAAQ;gBAAE6C,UAAU,EAAE;cAAI,CAAE;cAAAvC,QAAA,EAAC;YAEjF;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL9G,OAAA;cACEuH,OAAO,EAAEA,CAAA,KAAM5G,YAAY,CAAC,KAAK,CAAE;cACnCuG,KAAK,EAAE;gBACL4B,UAAU,EAAE,MAAM;gBAClBL,MAAM,EAAE,MAAM;gBACdvC,QAAQ,EAAE,QAAQ;gBAClBkC,KAAK,EAAE,MAAM;gBACbQ,MAAM,EAAE,SAAS;gBACjBvB,OAAO,EAAE,CAAC;gBACVmB,YAAY,EAAE;cAChB,CAAE;cAAAhC,QAAA,eAEFxG,OAAA,CAAChB,eAAe;gBAACiI,IAAI,EAAExH;cAAQ;gBAAAkH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN9G,OAAA;YAAMsJ,QAAQ,EAAEpF,YAAa;YAAAsC,QAAA,gBAC3BxG,OAAA;cAAKkH,KAAK,EAAE;gBAAEQ,OAAO,EAAE,MAAM;gBAAE6B,mBAAmB,EAAE,SAAS;gBAAEZ,GAAG,EAAE,EAAE;gBAAEd,YAAY,EAAE;cAAG,CAAE;cAAArB,QAAA,gBACzFxG,OAAA;gBAAAwG,QAAA,gBACExG,OAAA;kBAAOkH,KAAK,EAAE;oBAAEQ,OAAO,EAAE,OAAO;oBAAEG,YAAY,EAAE,CAAC;oBAAEkB,UAAU,EAAE,GAAG;oBAAEX,KAAK,EAAE;kBAAO,CAAE;kBAAA5B,QAAA,EAAC;gBAErF;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR9G,OAAA;kBACEqB,IAAI,EAAC,MAAM;kBACXwC,IAAI,EAAC,KAAK;kBACVC,KAAK,EAAE5C,QAAQ,CAACE,GAAI;kBACpBoI,QAAQ,EAAE7F,iBAAkB;kBAC5B8F,QAAQ;kBACRvC,KAAK,EAAE;oBACLqB,KAAK,EAAE,MAAM;oBACblB,OAAO,EAAE,MAAM;oBACfoB,MAAM,EAAE,mBAAmB;oBAC3BD,YAAY,EAAE,CAAC;oBACftC,QAAQ,EAAE,MAAM;oBAChBwD,UAAU,EAAE;kBACd,CAAE;kBACFC,OAAO,EAAG/F,CAAC,IAAKA,CAAC,CAACI,MAAM,CAACkD,KAAK,CAAC0C,WAAW,GAAG,SAAU;kBACvDC,MAAM,EAAGjG,CAAC,IAAKA,CAAC,CAACI,MAAM,CAACkD,KAAK,CAAC0C,WAAW,GAAG;gBAAU;kBAAAjD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN9G,OAAA;gBAAAwG,QAAA,gBACExG,OAAA;kBAAOkH,KAAK,EAAE;oBAAEQ,OAAO,EAAE,OAAO;oBAAEG,YAAY,EAAE,CAAC;oBAAEkB,UAAU,EAAE,GAAG;oBAAEX,KAAK,EAAE;kBAAO,CAAE;kBAAA5B,QAAA,EAAC;gBAErF;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR9G,OAAA;kBACE6D,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAE5C,QAAQ,CAACG,IAAK;kBACrBmI,QAAQ,EAAE7F,iBAAkB;kBAC5B8F,QAAQ;kBACRvC,KAAK,EAAE;oBACLqB,KAAK,EAAE,MAAM;oBACblB,OAAO,EAAE,MAAM;oBACfoB,MAAM,EAAE,mBAAmB;oBAC3BD,YAAY,EAAE,CAAC;oBACftC,QAAQ,EAAE;kBACZ,CAAE;kBAAAM,QAAA,gBAEFxG,OAAA;oBAAQ8D,KAAK,EAAC,EAAE;oBAAA0C,QAAA,EAAC;kBAAoB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EAC7C5E,WAAW,CAAC6C,GAAG,CAAC1D,IAAI,iBACnBrB,OAAA;oBAAmB8D,KAAK,EAAEzC,IAAK;oBAAAmF,QAAA,EAAEnF;kBAAI,GAAxBA,IAAI;oBAAAsF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAA6B,CAC/C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN9G,OAAA;cAAKkH,KAAK,EAAE;gBAAEQ,OAAO,EAAE,MAAM;gBAAE6B,mBAAmB,EAAE,SAAS;gBAAEZ,GAAG,EAAE,EAAE;gBAAEd,YAAY,EAAE;cAAG,CAAE;cAAArB,QAAA,gBACzFxG,OAAA;gBAAAwG,QAAA,gBACExG,OAAA;kBAAOkH,KAAK,EAAE;oBAAEQ,OAAO,EAAE,OAAO;oBAAEG,YAAY,EAAE,CAAC;oBAAEkB,UAAU,EAAE,GAAG;oBAAEX,KAAK,EAAE;kBAAO,CAAE;kBAAA5B,QAAA,EAAC;gBAErF;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR9G,OAAA;kBACEqB,IAAI,EAAC,MAAM;kBACXwC,IAAI,EAAC,cAAc;kBACnBC,KAAK,EAAE5C,QAAQ,CAACI,YAAa;kBAC7BkI,QAAQ,EAAE7F,iBAAkB;kBAC5BuD,KAAK,EAAE;oBACLqB,KAAK,EAAE,MAAM;oBACblB,OAAO,EAAE,MAAM;oBACfoB,MAAM,EAAE,mBAAmB;oBAC3BD,YAAY,EAAE,CAAC;oBACftC,QAAQ,EAAE;kBACZ;gBAAE;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN9G,OAAA;gBAAAwG,QAAA,gBACExG,OAAA;kBAAOkH,KAAK,EAAE;oBAAEQ,OAAO,EAAE,OAAO;oBAAEG,YAAY,EAAE,CAAC;oBAAEkB,UAAU,EAAE,GAAG;oBAAEX,KAAK,EAAE;kBAAO,CAAE;kBAAA5B,QAAA,EAAC;gBAErF;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR9G,OAAA;kBACE6D,IAAI,EAAC,QAAQ;kBACbC,KAAK,EAAE5C,QAAQ,CAACK,MAAO;kBACvBiI,QAAQ,EAAE7F,iBAAkB;kBAC5B8F,QAAQ;kBACRvC,KAAK,EAAE;oBACLqB,KAAK,EAAE,MAAM;oBACblB,OAAO,EAAE,MAAM;oBACfoB,MAAM,EAAE,mBAAmB;oBAC3BD,YAAY,EAAE,CAAC;oBACftC,QAAQ,EAAE;kBACZ,CAAE;kBAAAM,QAAA,EAEDvE,aAAa,CAAC8C,GAAG,CAACxD,MAAM,iBACvBvB,OAAA;oBAAqB8D,KAAK,EAAEvC,MAAO;oBAAAiF,QAAA,EAAEjF;kBAAM,GAA9BA,MAAM;oBAAAoF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAiC,CACrD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN9G,OAAA;cAAKkH,KAAK,EAAE;gBAAEQ,OAAO,EAAE,MAAM;gBAAE6B,mBAAmB,EAAE,SAAS;gBAAEZ,GAAG,EAAE,EAAE;gBAAEd,YAAY,EAAE;cAAG,CAAE;cAAArB,QAAA,gBACzFxG,OAAA;gBAAAwG,QAAA,gBACExG,OAAA;kBAAOkH,KAAK,EAAE;oBAAEQ,OAAO,EAAE,OAAO;oBAAEG,YAAY,EAAE,CAAC;oBAAEkB,UAAU,EAAE,GAAG;oBAAEX,KAAK,EAAE;kBAAO,CAAE;kBAAA5B,QAAA,EAAC;gBAErF;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR9G,OAAA;kBACEqB,IAAI,EAAC,MAAM;kBACXwC,IAAI,EAAC,YAAY;kBACjBC,KAAK,EAAE5C,QAAQ,CAACM,UAAW;kBAC3BgI,QAAQ,EAAE7F,iBAAkB;kBAC5BuD,KAAK,EAAE;oBACLqB,KAAK,EAAE,MAAM;oBACblB,OAAO,EAAE,MAAM;oBACfoB,MAAM,EAAE,mBAAmB;oBAC3BD,YAAY,EAAE,CAAC;oBACftC,QAAQ,EAAE;kBACZ;gBAAE;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN9G,OAAA;gBAAAwG,QAAA,gBACExG,OAAA;kBAAOkH,KAAK,EAAE;oBAAEQ,OAAO,EAAE,OAAO;oBAAEG,YAAY,EAAE,CAAC;oBAAEkB,UAAU,EAAE,GAAG;oBAAEX,KAAK,EAAE;kBAAO,CAAE;kBAAA5B,QAAA,EAAC;gBAErF;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR9G,OAAA;kBACE6D,IAAI,EAAC,gBAAgB;kBACrBC,KAAK,EAAE5C,QAAQ,CAACO,cAAe;kBAC/B+H,QAAQ,EAAE7F,iBAAkB;kBAC5BuD,KAAK,EAAE;oBACLqB,KAAK,EAAE,MAAM;oBACblB,OAAO,EAAE,MAAM;oBACfoB,MAAM,EAAE,mBAAmB;oBAC3BD,YAAY,EAAE,CAAC;oBACftC,QAAQ,EAAE;kBACZ,CAAE;kBAAAM,QAAA,gBAEFxG,OAAA;oBAAQ8D,KAAK,EAAC,EAAE;oBAAA0C,QAAA,EAAC;kBAA2B;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACpDlG,YAAY,CAACmE,GAAG,CAACG,WAAW,iBAC3BlF,OAAA;oBAA6B8D,KAAK,EAAEoB,WAAW,CAAC4E,EAAG;oBAAAtD,QAAA,EAChDtB,WAAW,CAAC9D;kBAAG,GADL8D,WAAW,CAAC4E,EAAE;oBAAAnD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEnB,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN9G,OAAA;cAAKkH,KAAK,EAAE;gBAAEQ,OAAO,EAAE,MAAM;gBAAE6B,mBAAmB,EAAE,SAAS;gBAAEZ,GAAG,EAAE,EAAE;gBAAEd,YAAY,EAAE;cAAG,CAAE;cAAArB,QAAA,gBACzFxG,OAAA;gBAAAwG,QAAA,gBACExG,OAAA;kBAAOkH,KAAK,EAAE;oBAAEQ,OAAO,EAAE,OAAO;oBAAEG,YAAY,EAAE,CAAC;oBAAEkB,UAAU,EAAE,GAAG;oBAAEX,KAAK,EAAE;kBAAO,CAAE;kBAAA5B,QAAA,EAAC;gBAErF;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR9G,OAAA;kBACEqB,IAAI,EAAC,MAAM;kBACXwC,IAAI,EAAC,qBAAqB;kBAC1BC,KAAK,EAAE5C,QAAQ,CAACQ,mBAAoB;kBACpC8H,QAAQ,EAAE7F,iBAAkB;kBAC5BuD,KAAK,EAAE;oBACLqB,KAAK,EAAE,MAAM;oBACblB,OAAO,EAAE,MAAM;oBACfoB,MAAM,EAAE,mBAAmB;oBAC3BD,YAAY,EAAE,CAAC;oBACftC,QAAQ,EAAE;kBACZ;gBAAE;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN9G,OAAA;gBAAAwG,QAAA,gBACExG,OAAA;kBAAOkH,KAAK,EAAE;oBAAEQ,OAAO,EAAE,OAAO;oBAAEG,YAAY,EAAE,CAAC;oBAAEkB,UAAU,EAAE,GAAG;oBAAEX,KAAK,EAAE;kBAAO,CAAE;kBAAA5B,QAAA,EAAC;gBAErF;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR9G,OAAA;kBACEqB,IAAI,EAAC,MAAM;kBACXwC,IAAI,EAAC,mBAAmB;kBACxBC,KAAK,EAAE5C,QAAQ,CAACS,iBAAkB;kBAClC6H,QAAQ,EAAE7F,iBAAkB;kBAC5BuD,KAAK,EAAE;oBACLqB,KAAK,EAAE,MAAM;oBACblB,OAAO,EAAE,MAAM;oBACfoB,MAAM,EAAE,mBAAmB;oBAC3BD,YAAY,EAAE,CAAC;oBACftC,QAAQ,EAAE;kBACZ;gBAAE;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN9G,OAAA;cAAKkH,KAAK,EAAE;gBAAEQ,OAAO,EAAE,MAAM;gBAAE6B,mBAAmB,EAAE,aAAa;gBAAEZ,GAAG,EAAE,EAAE;gBAAEd,YAAY,EAAE;cAAG,CAAE;cAAArB,QAAA,gBAC7FxG,OAAA;gBAAAwG,QAAA,gBACExG,OAAA;kBAAOkH,KAAK,EAAE;oBAAEQ,OAAO,EAAE,OAAO;oBAAEG,YAAY,EAAE,CAAC;oBAAEkB,UAAU,EAAE,GAAG;oBAAEX,KAAK,EAAE;kBAAO,CAAE;kBAAA5B,QAAA,EAAC;gBAErF;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR9G,OAAA;kBACEqB,IAAI,EAAC,QAAQ;kBACbwC,IAAI,EAAC,cAAc;kBACnBC,KAAK,EAAE5C,QAAQ,CAACW,YAAa;kBAC7B2H,QAAQ,EAAE7F,iBAAkB;kBAC5BoG,GAAG,EAAC,GAAG;kBACP7C,KAAK,EAAE;oBACLqB,KAAK,EAAE,MAAM;oBACblB,OAAO,EAAE,MAAM;oBACfoB,MAAM,EAAE,mBAAmB;oBAC3BD,YAAY,EAAE,CAAC;oBACftC,QAAQ,EAAE;kBACZ;gBAAE;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN9G,OAAA;gBAAAwG,QAAA,gBACExG,OAAA;kBAAOkH,KAAK,EAAE;oBAAEQ,OAAO,EAAE,OAAO;oBAAEG,YAAY,EAAE,CAAC;oBAAEkB,UAAU,EAAE,GAAG;oBAAEX,KAAK,EAAE;kBAAO,CAAE;kBAAA5B,QAAA,EAAC;gBAErF;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR9G,OAAA;kBACEqB,IAAI,EAAC,QAAQ;kBACbwC,IAAI,EAAC,WAAW;kBAChBC,KAAK,EAAE5C,QAAQ,CAACY,SAAU;kBAC1B0H,QAAQ,EAAE7F,iBAAkB;kBAC5BoG,GAAG,EAAC,GAAG;kBACP7C,KAAK,EAAE;oBACLqB,KAAK,EAAE,MAAM;oBACblB,OAAO,EAAE,MAAM;oBACfoB,MAAM,EAAE,mBAAmB;oBAC3BD,YAAY,EAAE,CAAC;oBACftC,QAAQ,EAAE;kBACZ;gBAAE;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN9G,OAAA;gBAAAwG,QAAA,gBACExG,OAAA;kBAAOkH,KAAK,EAAE;oBAAEQ,OAAO,EAAE,OAAO;oBAAEG,YAAY,EAAE,CAAC;oBAAEkB,UAAU,EAAE,GAAG;oBAAEX,KAAK,EAAE;kBAAO,CAAE;kBAAA5B,QAAA,EAAC;gBAErF;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR9G,OAAA;kBACEqB,IAAI,EAAC,QAAQ;kBACbwC,IAAI,EAAC,WAAW;kBAChBC,KAAK,EAAE5C,QAAQ,CAACa,SAAU;kBAC1ByH,QAAQ,EAAE7F,iBAAkB;kBAC5BoG,GAAG,EAAC,GAAG;kBACP7C,KAAK,EAAE;oBACLqB,KAAK,EAAE,MAAM;oBACblB,OAAO,EAAE,MAAM;oBACfoB,MAAM,EAAE,mBAAmB;oBAC3BD,YAAY,EAAE,CAAC;oBACftC,QAAQ,EAAE;kBACZ;gBAAE;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN9G,OAAA;cAAKkH,KAAK,EAAE;gBAAEW,YAAY,EAAE;cAAG,CAAE;cAAArB,QAAA,eAC/BxG,OAAA;gBAAOkH,KAAK,EAAE;kBAAEQ,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEoB,UAAU,EAAE,GAAG;kBAAEX,KAAK,EAAE,MAAM;kBAAEQ,MAAM,EAAE;gBAAU,CAAE;gBAAApC,QAAA,gBACzGxG,OAAA;kBACEqB,IAAI,EAAC,UAAU;kBACfwC,IAAI,EAAC,UAAU;kBACfE,OAAO,EAAE7C,QAAQ,CAACU,QAAS;kBAC3B4H,QAAQ,EAAE7F,iBAAkB;kBAC5BuD,KAAK,EAAE;oBAAEC,WAAW,EAAE,CAAC;oBAAEgB,SAAS,EAAE;kBAAa;gBAAE;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC,0BAEJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAEN9G,OAAA;cAAKkH,KAAK,EAAE;gBAAEQ,OAAO,EAAE,MAAM;gBAAEiB,GAAG,EAAE,EAAE;gBAAEf,cAAc,EAAE;cAAW,CAAE;cAAApB,QAAA,gBACnExG,OAAA;gBACEqB,IAAI,EAAC,QAAQ;gBACbkG,OAAO,EAAEA,CAAA,KAAM5G,YAAY,CAAC,KAAK,CAAE;gBACnCuG,KAAK,EAAE;kBACLG,OAAO,EAAE,WAAW;kBACpBoB,MAAM,EAAE,mBAAmB;kBAC3BD,YAAY,EAAE,CAAC;kBACfM,UAAU,EAAE,OAAO;kBACnBV,KAAK,EAAE,MAAM;kBACblC,QAAQ,EAAE,MAAM;kBAChB6C,UAAU,EAAE,GAAG;kBACfH,MAAM,EAAE;gBACV,CAAE;gBAAApC,QAAA,EACH;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT9G,OAAA;gBACEqB,IAAI,EAAC,QAAQ;gBACb2I,QAAQ,EAAEhJ,UAAW;gBACrBkG,KAAK,EAAE;kBACLG,OAAO,EAAE,WAAW;kBACpBoB,MAAM,EAAE,MAAM;kBACdD,YAAY,EAAE,CAAC;kBACfM,UAAU,EAAE9H,UAAU,GAAG,MAAM,GAAG,kDAAkD;kBACpFoH,KAAK,EAAE,OAAO;kBACdlC,QAAQ,EAAE,MAAM;kBAChB6C,UAAU,EAAE,GAAG;kBACfH,MAAM,EAAE5H,UAAU,GAAG,aAAa,GAAG,SAAS;kBAC9C0H,SAAS,EAAE;gBACb,CAAE;gBAAAlC,QAAA,EAEDxF,UAAU,GAAG,mBAAmB,GAAG;cAAuB;gBAAA2F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAxG,OAAO,gBACNN,OAAA;QAAKkH,KAAK,EAAE;UAAEI,SAAS,EAAE,QAAQ;UAAED,OAAO,EAAE;QAAO,CAAE;QAAAb,QAAA,eACnDxG,OAAA;UAAAwG,QAAA,EAAG;QAA6B;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,GACJtG,KAAK,gBACPR,OAAA;QAAKkH,KAAK,EAAE;UAAEI,SAAS,EAAE,QAAQ;UAAED,OAAO,EAAE,MAAM;UAAEe,KAAK,EAAE;QAAU,CAAE;QAAA5B,QAAA,gBACrExG,OAAA;UAAAwG,QAAA,GAAG,8BAA4B,EAAChG,KAAK;QAAA;UAAAmG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1C9G,OAAA;UACEuH,OAAO,EAAEA,CAAA,KAAMlB,MAAM,CAAClG,QAAQ,CAAC8J,MAAM,CAAC,CAAE;UACxC/C,KAAK,EAAE;YAAEE,SAAS,EAAE,MAAM;YAAEC,OAAO,EAAE,UAAU;YAAEyB,UAAU,EAAE,SAAS;YAAEV,KAAK,EAAE,OAAO;YAAEK,MAAM,EAAE,MAAM;YAAED,YAAY,EAAE,KAAK;YAAEI,MAAM,EAAE;UAAU,CAAE;UAAApC,QAAA,EAClJ;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,GACJ,CAACzD,KAAK,CAACC,OAAO,CAAClD,WAAW,CAAC,IAAIA,WAAW,CAACwE,MAAM,KAAK,CAAC,gBACzD5E,OAAA;QAAKkH,KAAK,EAAE;UAAEI,SAAS,EAAE,QAAQ;UAAED,OAAO,EAAE;QAAO,CAAE;QAAAb,QAAA,eACnDxG,OAAA;UAAAwG,QAAA,EAAG;QAAuB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CAAC,gBAEN9G,OAAA;QAAOuG,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAClCxG,OAAA;UAAAwG,QAAA,eACExG,OAAA;YAAAwG,QAAA,gBACExG,OAAA;cAAAwG,QAAA,EAAI;YAAG;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACZ9G,OAAA;cAAAwG,QAAA,EAAI;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACb9G,OAAA;cAAAwG,QAAA,EAAI;YAAe;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxB9G,OAAA;cAAAwG,QAAA,EAAI;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACf9G,OAAA;cAAAwG,QAAA,EAAI;YAAY;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrB9G,OAAA;cAAAwG,QAAA,EAAI;YAAW;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACd9G,OAAA;UAAAwG,QAAA,EACHpG,WAAW,CAAC2E,GAAG,CAAC,CAACC,EAAE,EAAEkF,KAAK,KAAK;YAAA,IAAAC,gBAAA;YAC9B;YACA,MAAMC,WAAW,GAAGpF,EAAE,CAAC1D,YAAY,IAAI0D,EAAE,CAACoF,WAAW,IAAI,KAAK;;YAE9D;YACA,MAAMC,UAAU,GAAIC,UAAU,IAAK;cACjC,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;cAC7B,IAAI;gBACF,MAAMC,OAAO,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;gBACpC,OAAOG,KAAK,CAACF,OAAO,CAACG,OAAO,CAAC,CAAC,CAAC,GAC3B,KAAK,GACLH,OAAO,CAACI,kBAAkB,CAAC,OAAO,EAAE;kBAClCC,IAAI,EAAE,SAAS;kBACfC,KAAK,EAAE,SAAS;kBAChBC,GAAG,EAAE;gBACP,CAAC,CAAC;cACR,CAAC,CAAC,OAAOlH,CAAC,EAAE;gBACVtB,OAAO,CAAC9B,KAAK,CAAC,6BAA6B,EAAEoD,CAAC,CAAC;gBAC/C,OAAO,KAAK;cACd;YACF,CAAC;YAED,MAAMmH,SAAS,GAAGV,UAAU,CAACrF,EAAE,CAACxD,UAAU,CAAC;YAC3C,MAAMwJ,iBAAiB,GAAGX,UAAU,CAACrF,EAAE,CAACtD,mBAAmB,CAAC;YAC5D,MAAMuJ,eAAe,GAAGZ,UAAU,CAACrF,EAAE,CAACrD,iBAAiB,CAAC;YAExD,MAAMuD,WAAW,GAAG,EAAAiF,gBAAA,GAAAnF,EAAE,CAACE,WAAW,cAAAiF,gBAAA,uBAAdA,gBAAA,CAAgB/I,GAAG,KAAI4D,EAAE,CAACvD,cAAc,IAAI,cAAc;YAE9E,oBACEzB,OAAA;cAAAwG,QAAA,gBACExG,OAAA;gBAAAwG,QAAA,EAAKxB,EAAE,CAAC5D,GAAG,IAAI;cAAK;gBAAAuF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC1B9G,OAAA;gBAAAwG,QAAA,EAAKxB,EAAE,CAAC3D,IAAI,IAAI;cAAK;gBAAAsF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC3B9G,OAAA;gBAAAwG,QAAA,EAAK4D;cAAW;gBAAAzD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtB9G,OAAA;gBAAAwG,QAAA,EAAKxB,EAAE,CAACzD,MAAM,IAAI;cAAK;gBAAAoF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC7B9G,OAAA;gBAAAwG,QAAA,EAAKwE;cAAiB;gBAAArE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,KAAC,eAC7B9G,OAAA;gBAAAwG,QAAA,EAAKyE;cAAe;gBAAAtE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,KAAC,eAC3B9G,OAAA;gBAAAwG,QAAA,EAAKtB;cAAW;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA,GAPf9B,EAAE,CAAC8E,EAAE,IAAII,KAAK;cAAAvD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAQnB,CAAC;UAET,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC5G,EAAA,CAzwBuBD,WAAW;EAAA,QAChBnB,WAAW;AAAA;AAAAoM,EAAA,GADNjL,WAAW;AAAA,IAAAiL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}